[tables]
[tables.global]
sheet = ["全局配置"]
workbook = "global.xlsx"
constant = true
horizontal = true

##################### 多语言描述配置文件:start #####################
# 多语言常量表
[tables.language_const]
sheet = ["LanguageConst"]
workbook = "language_const.xlsx"
enums = [
]

# 多语言系统提示表
[tables.language_sys]
sheet = ["LanguageSys"]
workbook = "language_const.xlsx"
enums = [
]

# 多语言 UI 表
[tables.language_ui]
sheet = ["LanguageUI"]
workbook = "language_ui.xlsx"
enums = [
]

# 多语言信息表
[tables.language_info]
sheet = ["LanguageInfo"]
workbook = "language_info.xlsx"
enums = [
]

# 物品——多语言表
[tables.language_item]
sheet = ["LanguageItem"]
workbook = "item.xlsx"
enums = [
]

# 商品——多语言表
[tables.language_store]
sheet = ["LanguageStore"]
workbook = "store.xlsx"
enums = [
]

# 鱼——多语言表
[tables.language_fish]
sheet = ["LanguageFish"]
workbook = "fish_basic.xlsx"
enums = [
]

# 地图——多语言表
[tables.language_map]
sheet = ["LanguageMap"]
workbook = "map_info.xlsx"
enums = [
]

# 钓场——多语言表
[tables.language_pond]
sheet = ["LanguagePond"]
workbook = "fish_pond.xlsx"
enums = [
]

# 新手——多语言表
[tables.language_novice_scene]
sheet = ["LanguageNoviceSceneGuide"]
workbook = "novice_scene_guide.xlsx"
enums = [
]

[tables.mail_id_map]
sheet = ["MailIdMap"]
workbook = "language_mail.xlsx"
enums = [
]

# 邮件——多语言表
[tables.language_mail]
sheet = ["LanguageMail"]
workbook = "language_mail.xlsx"
enums = [
	{field = "id", table = "mail_id_map"},
]

# 钓鱼日志 多语言
[tables.language_fish_log]
sheet = ["LanguageFishLog"]
workbook = "fish_log.xlsx"
enums = [
]

[tables.mail_key_const]
sheet = ["MailKeyConst"]
workbook = "language_mail.xlsx"
constant = true
horizontal = true

##################### 多语言描述配置文件:end #####################

# 网关封杀配置
[tables.gate_info]
sheet = ["GateInfo"]
workbook = "servers.xlsx"
constant = true
horizontal = true

# 动态路由配置
[tables.route_key]
sheet = ["RouteKey"]
workbook = "servers.xlsx"

# 灰度策略配置
[tables.gray_strategy]
sheet = ["GrayStrategy"]
workbook = "servers.xlsx"
constant = true
horizontal = true

# 实名认证配置
[tables.real_name_auth]
sheet = ["RealNameAuth"]
workbook = "servers.xlsx"
constant = true
horizontal = true

# 充值限制
[tables.recharge_limit]
sheet = ["RechargeLimit"]
workbook = "servers.xlsx"

# 封杀策略配置
[tables.block_strategy]
sheet = ["BlockStrategy"]
workbook = "servers.xlsx"
constant = true
horizontal = true

# 白名单配置
[tables.white_list]
sheet = ["WhiteList"]
workbook = "servers.xlsx"

# ip封杀配置
[tables.location_block]
sheet = ["LocationBlock"]
workbook = "servers.xlsx"

#####################物品配置:start #####################

# 物品常量配置
[tables.item_const]
sheet = ["ItemConst"]
workbook = "item.xlsx"
constant = true
horizontal = true

# 物品相关配置
[tables.item]
sheet = ["Item"]
workbook = "item.xlsx"
enums = [
	{field = "name_language", table = "language_item"},
	{field = "desc_language", table = "language_item"},
]

# 物品背包显示相关配置
[tables.item_warehouse_show]
sheet = ["ItemWarehouseShow"]
workbook = "item.xlsx"
enums = [
]

# 道具类型映射配置
[tables.item_type_map]
sheet = ["ItemTypeMap"]
workbook = "item.xlsx"
enums = [
	{field = "language_id", table = "language_const"}
]

# 食物道具配置表
[tables.item_food]
sheet = ["ItemFood"]
workbook = "item.xlsx"
enums = [
]

[tables.trip_bag_store_rule]
sheet = ["TripBagStoreRule"]
workbook = "item.xlsx"

# 礼包配置表
[tables.gift_basic]
sheet = ["GiftBasic"]
workbook = "gift.xlsx"
enums = [
	{field = "item_id", table = "item"},
]

#####################物品配置:end #####################

[tables.app_update_info]
sheet = ["AppUpdateInfo"]
workbook = "AppInfo.xlsx"
constant = true # 标注为常量表
horizontal = true

[tables.app_resource_info]
sheet = ["AppResourceInfo"]
workbook = "AppInfo.xlsx"
constant = true # 标注为常量表
horizontal = true

[tables.app_address_info]
sheet = ["AppAddressInfo"]
workbook = "AppInfo.xlsx"
constant = true # 标注为常量表
horizontal = true

[tables.app_client_info]
sheet = ["ClientInfo"]
workbook = "AppInfo.xlsx"
constant = true # 标注为常量表
horizontal = true

[tables.language_app_update_info]
sheet = ["LanguageAppUpdateInfo"]
workbook = "AppInfo.xlsx"

# 天气配置
[tables.weather_Const]
sheet = ["WeatherConst"]
workbook = "Weather.xlsx"
constant = true # 标注为常量表
horizontal = true

[tables.weather_conditions]
sheet = ["WeatherConditions"]
workbook = "Weather.xlsx"
enums = [
]

[tables.weather_factor]
sheet = ["WeatherFactor"]
workbook = "Weather.xlsx"
enums = [
    {field = "weather_id", table = "weather_conditions"}
]

[tables.weather_period]
sheet = ["WeatherPeriod"]
workbook = "Weather.xlsx"
enums = [
    {field = "period", table = "weather_factor"},
]

[tables.weather_pond]
sheet = ["WeatherPond"]
workbook = "Weather.xlsx"
enums = [
    {field = "pond", table = "fish_pond_list"},
    {field = "period", table = "weather_period"},
]

##################### 鱼基础配置:start #####################

# Basic鱼配置
[tables.basic_fish_Const]
sheet = ["BasicFishConst"]
workbook = "fish_basic.xlsx"
constant = true # 标注为常量表
horizontal = true

[tables.basic_fish_species]
sheet = ["BasicFishSpecies"]
workbook = "fish_basic.xlsx"
enums = [
	{field = "character", table = "basic_fish_character"},
]

[tables.basic_fish_quality]
sheet = ["BasicFishQuality"]
workbook = "fish_basic.xlsx"
enums = [
	{field = "basic_reward_item", table = "item"},
	{field = "name_language", table = "language_fish"},
	{field = "desc_lanuage", table = "language_fish"},
	{field = "species", table = "basic_fish_species"},
	{field = "character", table = "basic_fish_character"},
]

[tables.basic_fish_character]
sheet = ["BasicFishCharacter"]
workbook = "fish_basic.xlsx"
enums = [
]

[tables.basic_fish_habitus]
sheet = ["BasicFishHabitus"]
workbook = "fish_basic.xlsx"
enums = [
    #    {field = "preferred_weather", table = "weather_conditions"}
]

[tables.basic_fish_period]
sheet = ["BasicFishPeriod"]
workbook = "fish_basic.xlsx"
enums = [
]

[tables.basic_fish_water_layer]
sheet = ["BasicFishWaterLayer"]
workbook = "fish_basic.xlsx"

[tables.basic_fish_temperature]
sheet = ["BasicFishTemperature"]
workbook = "fish_basic.xlsx"

[tables.basic_fish_habitat_water]
sheet = ["BasicFishHabitatWater"]
workbook = "fish_basic.xlsx"

[tables.basic_fish_barrier]
sheet = ["BasicFishBarrier"]
workbook = "fish_basic.xlsx"

[tables.basic_fish_weather]
sheet = ["BasicFishWeather"]
workbook = "fish_basic.xlsx"
enums = [
	{field = "weather_type", table = "weather_conditions"},
]
##################### 鱼基础配置:end #####################

##################### 鱼习性配置:start #####################
# 鱼习性配置
[tables.fish_distribute_Const]
sheet = ["FishDistributeConst"]
workbook = "fish_habit.xlsx"
constant = true # 标注为常量表
horizontal = true

[tables.fish_distribute]
sheet = ["FishDistribute"]
workbook = "fish_habit.xlsx"
enums = [
    {field = "species", table = "basic_fish_species"},
    {field = "fish_quality", table = "basic_fish_quality"},
    {field = "fish_character", table = "basic_fish_character"},
    {field = "fish_habitus", table = "basic_fish_habitus"},
    {field = "period_id", table = "fish_distribute_period"},
    {field = "water_layer_id", table = "fish_distribute_water_layer"},
    {field = "water_temperature_id", table = "fish_distribute_temperature"},
    {field = "habitat_water_id", table = "fish_distribute_habitat_water"},
    {field = "barrier_id", table = "fish_distribute_barrier"},
	{field = "bait_id", table = "fish_distribute_bait"},
	{field = "weather_id", table = "fish_distribute_weather"},
]

[tables.fish_distribute_period]
sheet = ["FishDistributePeriod"]
workbook = "fish_habit.xlsx"
enums = [
    {field = "period_id", table = "basic_fish_period"},
]

[tables.fish_distribute_water_layer]
sheet = ["FishDistributeWaterLayer"]
workbook = "fish_habit.xlsx"
enums = [
    {field = "water_layer_id", table = "basic_fish_water_layer"},
]

[tables.fish_distribute_temperature]
sheet = ["FishDistributeTemperature"]
workbook = "fish_habit.xlsx"
enums = [
    {field = "temperature_id", table = "basic_fish_temperature"},
]

[tables.fish_distribute_habitat_water]
sheet = ["FishDistributeHabitatWater"]
workbook = "fish_habit.xlsx"
enums = [
    {field = "habitat_water_id", table = "basic_fish_habitat_water"},
]

[tables.fish_distribute_barrier]
sheet = ["FishDistributeBarrier"]
workbook = "fish_habit.xlsx"
enums = [
    {field = "barrier_id", table = "basic_fish_barrier"},
]

[tables.fish_distribute_bait]
sheet = ["FishDistributeBait"]
workbook = "fish_habit.xlsx"

[tables.fish_distribute_weather]
sheet = ["FishDistributeWeather"]
workbook = "fish_habit.xlsx"
enums = [
    {field = "weather_id", table = "basic_fish_weather"},
]
##################### 鱼习性配置:end #####################

# 钓场投鱼配置
[tables.fish_stock]
sheet = ["FishStock"]
workbook = "fish_pond.xlsx"
enums = [
]

# 钓场列表配置
[tables.fish_pond_list]
sheet = ["PondList"]
workbook = "fish_pond.xlsx"
enums = [
    {field = "fish_stock_id", table = "fish_stock"},
	{field = "entry_item", table = "item"},
	{field = "pond_name", table = "language_pond"},
	{field = "map_id", table = "map_basic"},
]

# 钓场鱼投放配置
[tables.fish_release]
sheet = ["FishRelease"]
workbook = "fish_pond.xlsx"
enums = [
    {field = "fish_id", table = "fish_env_affinity"},
	{field = "stock_id", table = "fish_stock"},
]

#中鱼const配置
[tables.hook_const]
sheet = ["HookConst"]
workbook = "fish_pond.xlsx"
constant = true # 标注为常量表
horizontal = true

##################### 地图配置:start #####################
#地图钓点信息
[tables.map_spot_info]
sheet = ["MapSpot"]
workbook = "map_info.xlsx"
enums = [
	{field = "spot_name", table = "language_map"},
]

# 钓场坐标配置
[tables.map_basic]
sheet = ["MapBasic"]
workbook = "map_info.xlsx"
enums = [
   {field = "desc", table = "language_map"},
   {field = "spot_info_id", table = "map_spot"},
]

# 钓场坐标配置
[tables.map_spot]
sheet = ["MapSpot"]
workbook = "map_info.xlsx"
enums = [
   {field = "spot_name", table = "language_map"},
]

# 场景设置配置
[tables.map_scene]
sheet = ["MapScene"]
workbook = "map_info.xlsx"
enums = [
	{field = "desc", table = "language_map"},
]

##################### 渔具配置:start #####################

# 鱼竿配置
[tables.rods]
sheet = ["Rods"]
workbook = "tackle.xlsx"
enums = [
]

# 鱼轮配置
[tables.reels]
sheet = ["Reels"]
workbook = "tackle.xlsx"
enums = [
]

# 主线配置
[tables.lines]
sheet = ["Lines"]
workbook = "tackle.xlsx"
enums = [
]

# 子线配置
[tables.leaders]
sheet = ["Leaders"]
workbook = "tackle.xlsx"
enums = [
]

# 真饵配置
[tables.baits]
sheet = ["Baits"]
workbook = "tackle.xlsx"
enums = [
	{field = "behavior_id", table = "behavior"},
	{field = "hook_id", table = "hooks"},
]

# 拟饵配置
[tables.lures]
sheet = ["Lures"]
workbook = "tackle.xlsx"
enums = [
	{field = "behavior_id", table = "behavior"},
	{field = "hook_id", table = "hooks"},
]

# 浮漂配置
[tables.bobbers]
sheet = ["Bobbers"]
workbook = "tackle.xlsx"
enums = [
]

# 鱼库配置
[tables.keepnets]
sheet = ["Keepnets"]
workbook = "tackle.xlsx"
enums = [
]

# 鱼钩配置
[tables.hooks]
sheet = ["Hooks"]
workbook = "tackle.xlsx"
enums = [
]

# 钓组规则配置表
[tables.rig_rule]
sheet = ["RigRule"]
workbook = "tackle.xlsx"
enums = [

]

# 钓具耐久度计算规则配置表
[tables.tackle_durability_cost]
sheet = ["TackleDurabilityCost"]
workbook = "tackle.xlsx"
constant = true # 标注为常量表
horizontal = true

# 鱼竿调性配置
[tables.rod_action]
sheet = ["RodAction"]
workbook = "tackle.xlsx"
enums = [
]

# 鱼竿硬度配置
[tables.rod_hardness]
sheet = ["RodHardness"]
workbook = "tackle.xlsx"
enums = [
]

# 刺鱼结果
[tables.hookset_result]
sheet = ["HooksetResult"]
workbook = "tackle.xlsx"
enums = [
]

##################### 渔具配置:end   #####################

##################### 商城配置:start   ###################

# 商品配置
[tables.goods_basic]
sheet = ["GoodsBasic"]
workbook = "store.xlsx"
enums = [
	{field = "item_id", table = "item"},
	{field = "name_language", table = "language_store"},
]

# 商城购买配置
[tables.store_buy]
sheet = ["StoreBuy"]
workbook = "store.xlsx"
enums = [
	{field = "goods_id", table = "goods_basic"},
	{field = "cost_item", table = "item"},
]

# 商城推荐配置
[tables.store_recommend]
sheet = ["StoreRecommend"]
workbook = "store.xlsx"
enums = [
	{field = "store_buy_id", table = "store_buy"},
	{field = "name_language", table = "language_store"}
]

# 商城推荐配置
[tables.store_room]
sheet = ["StoreRoom"]
workbook = "store.xlsx"
enums = [
	{field = "goods_id", table = "goods_basic"},
	{field = "cost_item", table = "item"},
	{field = "pond_id", table = "fish_pond_list"},
]

# 商城Pay配置
[tables.purchase_list]
sheet = ["PurchaseList"]
workbook = "purchase.xlsx"
enums = [
	{field = "goods_id", table = "goods_basic"},
]

##################### 商城配置:end     #####################

## ------------------------------------
##               任务
## ------------------------------------
# enum
[tables.task_const]
sheet = ["TaskConst"]
workbook = "task.xlsx"
missRepeat = true

# 任务配置
[tables.task]
sheet = ["Task"]
workbook = "task.xlsx"
enums = [
	{field = "type", table = "task_const"},
	{field = "sub_id", table = "fish_pond_list,task_achieve_type"},
	# {field = "open_rule", table = "task_open_rule"},
	{field = "name_language", table = "language_info"},
	{field = "desc_language", table = "language_info"},
	{field = "cond_group", table = "task_cond_group"},
	{field = "reward", table = "task_reward"},
]

# 任务配置条件
[tables.task_cond_group]
sheet = ["TaskCondGroup"]
workbook = "task.xlsx"
enums = [
	{field = "open_cond", table = "task_open_rule"},
	{field = "cond", table = "task_cond"},
	{field = "open_val", table = "task"},
]

# 任务配置条件
[tables.task_cond]
sheet = ["TaskCond"]
workbook = "task.xlsx"
enums = [
	{field = "desc_language", table = "language_info"},
	{field = "control", table = "task_const"},
	{field = "event", table = "task_const"},
	{field = "value", table = "item,fish_pond_list,weather_conditions,basic_fish_quality,basic_fish_species"},
]

[tables.task_reward]
sheet = ["TaskReward"]
workbook = "task.xlsx"
enums = [
	{field = "item_id", table = "item"},
]

# 任务开启规则
[tables.task_open_rule]
sheet = ["TaskOpenRule"]
workbook = "task.xlsx"
enums = [
	{field = "language", table = "language_info"},
	{field = "label", table = "task_const"},
]

# 成就类型
[tables.task_achieve_type]
sheet = ["TaskAchieveType"]
workbook = "task.xlsx"
enums = [
]

# 成就进度任务
[tables.task_progress]
sheet = ["TaskProgress"]
workbook = "task.xlsx"
enums = [
	{field = "type", table = "task_const"},
	{field = "sub_id", table = "fish_pond_list,task_achieve_type"},
	{field = "reward", table = "task_reward"},
	{field = "name_language", table = "language_info"},
]

# 成就分组
[tables.task_group]
sheet = ["TaskGroup"]
workbook = "task.xlsx"
enums = [
	{field = "name_language", table = "language_info"},
]

##################### 角色信息:start #####################
# 角色const表
[tables.role_const]
sheet = ["RoleConst"]
workbook = "role_info.xlsx"
constant = true # 标注为常量表
horizontal = true

# 角色经验等级
[tables.role_level]
sheet = ["RoleLevel"]
workbook = "role_info.xlsx"
enums = [
	{field = "item_id", table = "item"},
]

# 角色头像表
[tables.role_avatar]
sheet = ["RoleAvatar"]
workbook = "role_info.xlsx"
enums = [
]

# 角色头像框表
[tables.role_frame]
sheet = ["RoleFrame"]
workbook = "role_info.xlsx"
enums = [
]
# 角色杂项表
 [tables.role_fishing_info]
 sheet = ["RoleFishingInfo"]
 workbook = "role_info.xlsx"
 enums = [
 ]

# 角色等级解锁功能表
[tables.modules_open]
sheet = ["ModulesOpen"]
workbook = "role_info.xlsx"
enums = [
	{field = "module_name", table = "language_ui"}
]

# 功能隐藏表
[tables.feature_hide]
sheet = ["FeatureHide"]
workbook = "role_info.xlsx"
enums = [
]

# 数据上报开关表
[tables.data_report]
sheet = ["DataReport"]
workbook = "role_info.xlsx"
enums = [
]

##################### 角色信息:end #####################

##################### 统计:start #####################

[tables.stats_const]
sheet = ["StatsConst"]
workbook = "stats.xlsx"
enums = []
missRepeat = true


[tables.stats_event]
sheet = ["StatsEvent"]
workbook = "stats.xlsx"
enums = []


[tables.stats_event_field]
sheet = ["StatsEventField"]
workbook = "stats.xlsx"
enums = []

[tables.stats]
sheet = ["Stats"]
workbook = "stats.xlsx"
enums = [
	{field = "type", table = "stats_const"},
	{field = "language", table = "language_ui"},
	{field = "sub_type", table = "stats_const"},
	{field = "event", table = "stats_event"},
	{field = "field", table = "stats_event_field"},
	{field = "label", table = "stats_event_field"},
	{field = "target", table = "item,fish_pond_list,weather_conditions,basic_fish_quality,basic_fish_species"}
]

[tables.stats_rule]
sheet = ["StatsRule"]
workbook = "stats.xlsx"
enums = [
	{field = "typ", table = "stats_type"},
]

[tables.stats_type]
sheet = ["StatsType"]
workbook = "stats.xlsx"
enums = [
]

#####################播报配置:start #####################

# 播报配置
[tables.broadcast_temp]
sheet = ["BroadcastTemp"]
workbook = "msg.xlsx"
enums = [
	{field = "content", table = "language_info"},
]

# 播报配置
[tables.event_temp_map]
sheet = ["EventTempMap"]
workbook = "msg.xlsx"
enums = [
	{field = "temp_id", table = "broadcast_temp"},
]
#####################播报配置:end #####################


#####################能量配置:start #####################
# 能量基础表
[tables.energy_basic]
sheet = ["EnergyBasic"]
workbook = "energy.xlsx"
constant = true # 标注为常量表
horizontal = true

# 能量基础表
[tables.energy_cost]
sheet = ["EnergyCost"]
workbook = "energy.xlsx"
constant = true # 标注为常量表
horizontal = true
#####################能量配置:end #####################


#####################鱼咬口配置:start #####################
# 咬口名称
[tables.behaviour_portrait]
sheet = ["BehaviourPortrait"]
workbook = "fish_action.xlsx"
enums = [
]

# 动力特性
[tables.kinetic_profile]
sheet = ["KineticProfile"]
workbook = "fish_action.xlsx"
enums = [
]

# 真咬口配置
[tables.real_bite]
sheet = ["RealBite"]
workbook = "fish_action.xlsx"
enums = [
	{field = "behaviour_id", table = "behaviour_portrait"},
]

# 假咬口配置
[tables.fake_bite]
sheet = ["FakeBite"]
workbook = "fish_action.xlsx"
enums = [
	{field = "behaviour_id", table = "behaviour_portrait"},
]

# 鱼行为配置
[tables.fish_fight_behaviour]
sheet = ["FishFightBehaviour"]
workbook = "fish_action.xlsx"
enums = [
	{field = "realbite_id", table = "behaviour_portrait"},
	{field = "fakebite_id", table = "behaviour_portrait"},
	{field = "kinetic_profile_id", table = "kinetic_profile"},
	{field = "species_id", table = "basic_fish_species"},

]

# 行为动作配置
[tables.behavior]
sheet = ["Behavior"]
workbook = "fish_action.xlsx"
enums = [
]

# 行为表现配置
[tables.expression]
sheet = ["Expression"]
workbook = "fish_action.xlsx"
enums = [
]

# 中鱼间隔配置
[tables.action_sort_cost]
sheet = ["ActionSortCost"]
workbook = "fish_action.xlsx"
constant = true # 标注为常量表
horizontal = true
#####################鱼咬口配置:end #####################


#####################鱼环境波动系数配置:start #####################
#鱼环境系数静态配置
[tables.env_affinity_const]
sheet = ["EnvAffinityConst"]
workbook = "fish_env_affinity.xlsx"
constant = true # 标注为常量表
horizontal = true

#鱼环境系数总配置表
[tables.fish_env_affinity]
sheet = ["FishEnvAffinity"]
workbook = "fish_env_affinity.xlsx"
enums = [
	{field = "fish_quality", table = "basic_fish_quality"},
	{field = "struct_id", table = "struct_affinity"},
	{field = "temp_id", table = "temp_affinity"},
	{field = "layer_id", table = "water_layer_affinity"},
	{field = "light_id", table = "light_affinity"},
	{field = "bait_coeff_group", table = "env_affinity_map"},
	{field = "bait_type_coeff_group", table = "env_affinity_map"},
	{field = "period_coeff_group", table = "env_affinity_map"},
]

#水下结构体波动系数配置
[tables.struct_affinity]
sheet = ["StructAffinity"]
workbook = "fish_env_affinity.xlsx"
enums = [
]

#温度波动系数配置
[tables.temp_affinity]
sheet = ["TempAffinity"]
workbook = "fish_env_affinity.xlsx"
enums = [
]

#水层波动系数配置
[tables.water_layer_affinity]
sheet = ["WaterLayerAffinity"]
workbook = "fish_env_affinity.xlsx"
enums = [
]

#鱼饵波动系数配置
[tables.bait_affinity]
sheet = ["BaitAffinity"]
workbook = "fish_env_affinity.xlsx"
enums = [
	{field = "bait_id", table = "item"},
	{field = "bait_coeff_group", table = "env_affinity_map"},
]

#鱼饵类型波动系数配置
[tables.bait_type_affinity]
sheet = ["BaitTypeAffinity"]
workbook = "fish_env_affinity.xlsx"
enums = [
	{field = "pose_group", table = "env_affinity_map"},
	{field = "bait_type_coeff_group", table = "env_affinity_map"},
]

#鱼饵类型姿态映射配置
[tables.env_affinity_map]
sheet = ["EnvAffinityMap"]
workbook = "fish_env_affinity.xlsx"
enums = [
]

#鱼饵姿态波动系数
[tables.pose_affinity]
sheet = ["PoseAffinity"]
workbook = "fish_env_affinity.xlsx"
enums = [
	{field = "pose_group", table = "env_affinity_map"},
]

# 光照波动系数配置
[tables.light_affinity]
sheet = ["LightAffinity"]
workbook = "fish_env_affinity.xlsx"
enums = [
]

# 时段波动系数配置
[tables.period_affinity]
sheet = ["PeriodAffinity"]
workbook = "fish_env_affinity.xlsx"
enums = [
	{field = "period_group", table = "env_affinity_map"},
	{field = "period_id", table = "basic_fish_period"},
]


#####################鱼环境波动系数配置:end #######################

#####################技法配置:start #####################
# 技法常量表
[tables.pose_basic]
sheet = ["PoseBasic"]
workbook = "pose.xlsx"
constant = true # 标注为常量表
horizontal = true

# 技法基础表
[tables.pose_lure_entities]
sheet = ["PoseLureEntities"]
workbook = "pose.xlsx"
[tables.pose_float_entities]
sheet = ["PoseFloatEntities"]
workbook = "pose.xlsx"

# 技法增益表
[tables.pose_effects]
sheet = ["PoseEffects"]
workbook = "pose.xlsx"
#####################技法配置:end #####################

##################### 排行榜:start #####################
# 排行榜类型
[tables.rank_type]
sheet = ["RankType"]
workbook = "rank.xlsx"

# 排行榜主表配置
[tables.rank]
sheet = ["Rank"]
workbook = "rank.xlsx"
enums = [
	{field = "rule", table = "rank_type"},
	{field = "reward_desc", table = "language_ui"},
]

# 排行榜奖励配置
[tables.rank_reward]
sheet = ["RankReward"]
workbook = "rank.xlsx"
enums = [
	{field = "rank_id", table = "rank"},
	{field = "item_id", table = "item"},
	{field = "template_id", table = "mail_id_map"},
]

##################### 排行榜.end #####################

##################### 鱼情推荐:start #####################


# 钓法推荐主表配置
[tables.recomm_types]
sheet = ["RecommTypes"]
workbook = "method_recomm.xlsx"


# 钓法推荐映射配置
[tables.recomm_mapping]
sheet = ["RecommMapping"]
workbook = "method_recomm.xlsx"
enums = [
	{field = "weather_factor_name", table = "weather_factor"},
	{field = "recomm_type", table = "recomm_types"},
]

##################### 鱼情推荐.end #####################

#####################公告:start #####################

# 常规公告配置
[tables.ann_common]
sheet = ["AnnCommon"]
workbook = "announcement.xlsx"

# 弹窗公告配置
[tables.ann_pop_up]
sheet = ["AnnPopUp"]
workbook = "announcement.xlsx"

# 公告问卷配置
[tables.ann_survey]
sheet = ["AnnSurvey"]
workbook = "announcement.xlsx"
constant = true # 标注为常量表
horizontal = true
#####################公告:end #####################

#####################新手场景引导:start #####################
# 新手场景引导中鱼列表显示
[tables.novice_scene_fish_info]
sheet = ["NoviceSceneFishInfo"]
workbook = "novice_scene_guide.xlsx"
enums = [
	{field = "fish_id", table = "basic_fish_quality"},
]


# 引导类型
[tables.novice_scene_guide_type]
sheet = ["NoviceSceneGuideType"]
workbook = "novice_scene_guide.xlsx"

# 触发类型
[tables.novice_scene_trigger_type]
sheet = ["NoviceSceneTriggerType"]
workbook = "novice_scene_guide.xlsx"


# 对话列表
#[tables.language_novice_guide_list]
#sheet = ["LanguageNoviceGuideList"]
#workbook = "novice_scene_guide.xlsx"
#enums = [
#	{field = "title", table = "language_novice_scene"},
#	{field = "msg_text", table = "language_novice_scene"},
#]


# 引导内容
[tables.novice_scene_guide]
sheet = ["NoviceSceneGuide"]
workbook = "novice_scene_guide.xlsx"
enums = [
	{field = "type", table = "novice_scene_guide_type"},
	{field = "trigger_type", table = "novice_scene_trigger_type"},
	{field = "gift_id", table = "item"},
]


# 随机名字
[tables.random_name]
sheet = ["RandomName"]
workbook = "random_name.xlsx"
enums = [
]


#####################新手场景引导:end #####################


##################### 活动配置:start #####################
# 连续登录
[tables.continuous_login]
sheet = ["ContinuousLogin"]
workbook = "activity.xlsx"
enums = [
	{field = "item_id", table = "item"},
]

[tables.activity_const]
sheet = ["ActivityConst"]
workbook = "activity.xlsx"
constant = true


[tables.activity]
sheet = ["Activity"]
workbook = "activity.xlsx"
enums = [
	{field = "id", table = "activity_const"},
]

[tables.stages]
sheet = ["Stages"]
workbook = "activity.xlsx"
enums = [
	{field = "activity_id", table = "activity_const"},
	{field = "stage_rewards", table = "stage_rewards"},
]

[tables.stage_rewards]
sheet = ["StageRewards"]
workbook = "activity.xlsx"
enums = [
	{field = "item_id", table = "item"},
]

##################### 活动配置:end #####################

##################### 钓鱼日志配置:start #####################

# 钓鱼日志 文本信息
[tables.fish_log_info]
sheet = ["FishLogInfo"]
workbook = "fish_log.xlsx"
enums = [
	{field = "info_desc", table = "language_fish_log"},
]

##################### 钓鱼日志配置:end #####################


##################### 搏鱼配置:start #####################
[tables.unhooking_const]
sheet = ["UnhookingConst"]
workbook = "battling.xlsx"
constant = true
horizontal = true

[tables.fish_damaged_level]
sheet = ["FishDamagedLevel"]
workbook = "battling.xlsx"
enums = [
]

[tables.battling_fight]
sheet = ["BattlingFight"]
workbook = "battling.xlsx"
enums = [
]

[tables.battling_rod_fight]
sheet = ["BattlingRodFight"]
workbook = "battling.xlsx"
enums = [
]

[tables.battling_tackle_fight]
sheet = ["BattlingTackleFight"]
workbook = "battling.xlsx"
enums = [
]
##################### 搏鱼配置:end #####################
