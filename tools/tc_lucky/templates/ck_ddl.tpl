-- ClickHouse DDL file gen by gen
-- {{.SqlModule}} 模块
/*队列表*/
CREATE TABLE fancyckdb.{{.SqlModule}}_kafka
(
    `Ktable` String,
    `ProductID` UInt8,
    `ChannelType` UInt8,
    `Platform` UInt8,
    `AppLanguage` UInt8,
    `Country` String,
    `AccType` UInt8,
    `AppVersion` String,
    `NowDate` Date,
    `TimeStampValue` DateTime,
-- 上面为固定头，下列为专属字段

)
    ENGINE = Kafka
SETTINGS kafka_broker_list = '************:9092',
kafka_topic_list = '{{.SqlModule}}',
kafka_group_name = 'group1',
kafka_format = 'CSV',
format_csv_delimiter = '|',
kafka_skip_broken_messages = 10;

/*视图表*/
CREATE MATERIALIZED VIEW fancyckdb.{{.SqlModule}}_view TO fancyckdb.{{.SqlModule}}_ods
(
    `Ktable` String,
    `ProductID` UInt8,
    `ChannelType` UInt8,
    `Platform` UInt8,
    `AppLanguage` UInt8,
    `Country` String,
    `AccType` UInt8,
    `AppVersion` String,
    `NowDate` Date,
    `TimeStampValue` DateTime,
-- 上面为固定头，下列为专属字段

) AS
SELECT
	Ktable,
	ProductID,
	ChannelType,
	Platform,
	AppLanguage,
	Country,
	AccType,
	AppVersion,
	NowDate,
	TimeStampValue,
-- 上面为固定头，下列为专属字段

FROM
	fancyckdb.{{.SqlModule}}_kafka;

/*元数据表*/
CREATE TABLE fancyckdb.{{.SqlModule}}_ods
(
    `Ktable` String,
    `ProductID` UInt8,
    `ChannelType` UInt8,
    `Platform` UInt8,
    `AppLanguage` UInt8,
    `Country` String,
    `AccType` UInt8,
    `AppVersion` String,
    `NowDate` Date,
    `TimeStampValue` DateTime,
-- 上面为固定头，下列为专属字段

)
    ENGINE = MergeTree
PARTITION BY NowDate
ORDER BY
Uid
SETTINGS index_granularity = 8192;