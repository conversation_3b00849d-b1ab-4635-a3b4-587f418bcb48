#!/bin/bash
	PROJECT_NAME={{.SrvName}}srv

	APP_NAME=$(PROJECT_NAME)
	APP_VERSION=$(shell git describe --tags --abbrev=0 2> /dev/null || echo "untagged")
	BUILD_VERSION=$(shell git log -1 --oneline | cut -d ' ' -f1)
	BUILD_TIME=$(shell date "+%FT%T%z")
	GIT_REVISION=$(shell git rev-parse --short HEAD)
	GIT_BRANCH=$(shell git name-rev --name-only HEAD)
	GO_VERSION = $(shell go version 2>&1 | awk '/^go version/ {print $$3}')
	GIT_USER = $(shell git config --get user.name)

	LDFLAGS=$(shell echo '"-s -X 'git.keepfancy.xyz/back-end/frameworks/kit/driver.AppName=${APP_NAME}' \
                  -X 'git.keepfancy.xyz/back-end/frameworks/kit/driver.AppVersion=${APP_VERSION}' \
                  -X 'git.keepfancy.xyz/back-end/frameworks/kit/driver.BuildVersion=${BUILD_VERSION}' \
                  -X 'git.keepfancy.xyz/back-end/frameworks/kit/driver.BuildTime=${BUILD_TIME}' \
                  -X 'git.keepfancy.xyz/back-end/frameworks/kit/driver.GitRevision=${GIT_REVISION}' \
                  -X 'git.keepfancy.xyz/back-end/frameworks/kit/driver.GitBranch=${GIT_BRANCH}' \
                  -X 'git.keepfancy.xyz/back-end/frameworks/kit/driver.GitUser=${GIT_USER}' \
                  -X 'git.keepfancy.xyz/back-end/frameworks/kit/driver.GoVersion=${GO_VERSION}'"')

all:
	make {{.SrvName}}srv

# 钓点服务器
.PHONY: {{.SrvName}}srv
{{.SrvName}}srv:
	echo "{{.SrvName}}srv build"
	go mod tidy && go build -ldflags $(LDFLAGS) -o ../bin/$@/$@ ./cmd/main.go
	cp ./cmd/config.yml ../bin/$@/

clean:
	go clean && rm -rf ../bin/*