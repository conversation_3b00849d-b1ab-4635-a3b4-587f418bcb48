# tc_lucky
lucky工具集

# 功能
* tcgo微服务代码生成

# 使用
## 生成微服务代码
Mac执行如下命令：
./tc_lucky_mac.exe --config=config.yml --output=../bin --srv_name=asset

windows执行如下命令：
./tc_lucky_win.exe --config=config.yml --output=../bin --srv_name=asset

-config：配置文件，默认为config.toml
-output：输出目录，默认为当前目录
-srv_name：微服务名


## 备注
1. 微服务名称不要带后缀，统一会在名称后添加srv，并且在Consul注册不带srv。
2. 代码生成后需要去config配置文件中修改数据库连接信息，端口信息等

# 生成ClickHouse带头字段模版
./tc_lucky_mac.exe --report=r_login --output=../bin