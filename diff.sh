#!/bin/bash


function diff_files() {
    local file1=$1
    local file2=$2
    if [[ ! -e "$file2" ]]; then 
        echo "new file: $file1"
        return
    fi
    local diff_output=$(diff -u "$file1" "$file2")
    if  [[ $diff_output != "" ]]; then
        echo "Files differ:$file1"
        echo "$diff_output"
    fi
}

function data_diff() {
    local path=$1
    if [[ ! -d $1 ]];  then 
        echo no tmp
        return
    fi
    local fileList=`find $path -type f`

    for file in ${fileList[@]} ; do
        diff_files $file tmp.$file
    done
}

data_diff "data"