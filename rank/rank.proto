syntax = "proto3";
package rankPB;

option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/rank;rankPB";

import "errors.proto";
import "common.proto";

// 查询排行榜
message GetRankListReq {
    int64 id    = 1;  // 排行榜ID
    int32 start = 2;  // 开始位置
    int32 end   = 3;  // 结束位置
}

// 查询排行榜
message GetRankListRsp {
    common.Result     ret                    = 1;  // 结果
    int64             id                     = 2;  // 排行榜ID
    repeated          common.RankPlayer list = 3;  // 排行榜列表
    common.RankPlayer self                   = 4;  // 自己
    int32             round                  = 5;  // 排名周期
}