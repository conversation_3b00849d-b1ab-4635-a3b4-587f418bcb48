syntax = "proto3";

package common;

option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common;commonPB";

/*************************************************************************************/
/*                                    通用错误码                                      */
/*************************************************************************************/
// Result 通用处理结果
message Result {
    ErrCode code = 1;
    string  desc = 2;
}

// Pagination 通用分页
message PaginationReq {
    int32 page_index  = 1;
    int32 page_size   = 2;
}

// ErrCode 错误码
enum ErrCode {
    /*************************************************************************************/
    /*                                    通用 (0 ~ 99)                                      */
    /*************************************************************************************/
    ERR_SUCCESS                     = 0; // 成功
    ERR_FAIL                        = 1; // 失败
    ERR_BAD_PARAM                   = 2; // 参数错误
    ERR_NOT_EXIST                   = 3; // 不存在
    ERR_SYSTEM_MISTAKE              = 4; // 系统错误
    ERR_OPERATION                   = 5; // 操作异常
    ERR_NOT_ENOUGH                  = 6; // 条件不足
    ERR_ILLEGAL_OPERATION           = 7; // 非法操作
    ERR_EXCEED                      = 8; // 超出，超过
    ERR_CONF_ERROR                  = 9; // 配置错误
    ERR_JSON_PARSE_ERROR            = 10; // JSON解析错误
    ERR_DB_OPE_ERROR                = 11; // DB操作错误
    ERR_FORBID_WORD                 = 12; // 含有敏感字

    ERR_UNKNOWN                     = 99; // 未知错误

    /*************************************************************************************/
    /*                                    登录相关                                        */
    /*************************************************************************************/
    ERR_LOGIN_TOKEN_EXPIRE           = 100;  // Token失效
    ERR_LOGIN_TYPE                   = 101;  // 登录类型错误
    ERR_LOGIN_FORBID                 = 102;  // 账号禁止登录
    ERR_LOGIN_PLAY_FORBID            = 103;  // 账号禁玩，可以登录
    ERR_LOGIN_BAN                    = 104;  // 封号
    ERR_LOGIN_REGISTERED             = 105;  // 已注册
    ERR_LOGIN_SDK_BAD_INFO           = 106;  // SDK信息错误
    ERR_LOGIN_ACCOUNT_PASSWORD       = 107;  // 账号或密码格式不对
    ERR_LOGIN_ACCOUNT_NOT_EXIST      = 108;  // 账号不存在(针对登录)
    ERR_LOGIN_ACCOUNT_IS_EXIST       = 109;  // 账号已存在(针对注册
    ERR_LOGIN_ACCOUNT_PASSWORD_ERROR = 110;  // 账号或密码错误
    ERR_LOGIN_ANTI_ADDICTION         = 111;  // 反沉迷状态不可登录
    ERR_LOGIN_BLOCK_LOCATION         = 112;  // 地区封杀


    ERR_LOGIN_UNKNOWN                = 199; // 未知登录错误

    /*************************************************************************************/
    /*                                    大厅相关                                       */
    /*************************************************************************************/
    ERR_HALL_ENTER_SPOT             = 200;  // 进入钓场失败
    ERR_HALL_GET_ROOM               = 201;  // 获取房间失败
    ERR_HALL_ITEM_NOT_ENOUGH        = 202;  // 道具不足
    ERR_HALL_GOODS_LIMIT            = 203;  // 超过商品限制购买次数
    ERR_HALL_ADD_REWARD_ERR         = 204;  // 添加奖励失败
    ERR_HALL_EXIST_IN_ROOM          = 205;  // 玩家已经在房间
    ERR_HALL_ENTRY_LEVEL            = 206;  // 玩家等级不够
    ERR_HALL_SPOT_CLOSE             = 207;  // 钓点关闭
    ERR_HALL_RIG_RULE_WRONG         = 208;  // 规则错误
    ERR_HALL_RIG_SLOT_NOT_ENOUGH    = 209;  // 钓组槽位不足
    ERR_HALL_REAL_NAME_AUTH         = 210;  // 实名认证失败
    ERR_HALL_REAL_NAME_NOT_OPEN     = 211;  // 实名认证未开启
    ERR_HALL_BAG_FULL               = 212;  // 背包已满
    ERR_HALL_CDKEY_EXCHANGE_FAILED  = 213;  // CDKey兑换失败
    ERR_HALL_CDKEY_EXCHANGE_EXPIRED = 214;  // CDKey已过期
    ERR_HALL_CDKEY_EXCHANGE_MAX     = 215;  // CDKey已被兑换完
    ERR_HALL_CDKEY_EXCHANGE_USED    = 216;  // CDKey已使用

    ERR_HALL_UNKNOWN               = 299; // 未知错误

    /*************************************************************************************/
    /*                                    钓点相关                                        */
    /*************************************************************************************/
    ERR_SPOT_GET_ROOM               = 300; // 获取房间id错误
    ERR_SPOT_NOT_IN_ROOM            = 301; // 玩家不在此房间
    ERR_SPOT_ROOM_PLAYER            = 302; // 钓点房间玩家查询失败
    ERR_SPOT_KEEPENT_FULL           = 303; // 鱼护已满
    ERR_SPOT_FISH_WEIGHT_LIMIT      = 304; // 鱼重限制
    ERR_SPOT_ADD_FISH_EXIST         = 305; // 添加鱼已存在
    ERR_SPOT_NOT_ENOUGH_POWER       = 306; // 体力不足
    ERR_SPOT_FISH_OUT               = 307; // 鱼跑
    ERR_SPOT_FISH_IN_KEEPENT        = 308; // 已经入护
    ERR_SPOT_FISH_HAS_OUT           = 309; // 已经放生

    /*************************************************************************************/
    /*                                    支付相关                                        */
    /*************************************************************************************/
    ERR_PAY_CHANNEL_NOT_EXIST                   = 400; // 支付渠道不存在

    ERR_ORDER_PLACE_FAIL                        = 401; // 下单失败
    ERR_ORDER_DELIVER_FAIL                      = 402; // 发货通知失败
    ERR_ORDER_INVALID                           = 403; // 无效订单
    ERR_ORDER_NON_PAYMENT                       = 404; // 未支付
    ERR_ORDER_EXIST_UNFINISHED                  = 405; // 存在未完成订单
    ERR_ORDER_DELIVER_YET                       = 406; // 已发货订单
    ERR_ORDER_CLOSE_FAIL                        = 407; // 关闭订单处理失败
    ERR_ORDER_VERIFY_PAYED_ORDER_CANCELED       = 408; // 校验到取消已支付订单
    ERR_ORDER_VERIFY_HAD_CONSUMPTION            = 409; // 校验到订单已消费
    ERR_ORDER_VERIFY_ACCESS_TOKEN_INVALID       = 410; // 校验 ACCESS_TOKEN已失效
    ERR_ORDER_VERIFY_CLIENT_JSON_FAILED         = 411; // 校验 解析客户端上报Json失败
    ERR_ORDER_VERIFY_ACCESS_TOKEN_GET_FAILED    = 412; // 校验 获取AccessToken失败
    ERR_ORDER_NOT_MATCH_CONDITION               = 413; // 下单条件不匹配
    ERR_ORDER_ORDER_NOT_FOUND                   = 414; // 订单没有找到
    ERR_ORDER_DELIVER_NO_SUCH_PURCHASE_TYPE     = 415; // 没有此种支付类型
    ERR_ORDER_CREATE_ORDER_WRONG_PURCHASE_ID    = 416; // 错误的支付purchaseID, 非商城支付
    ERR_ORDER_SERVER_PROCESSING_EXCEPTION       = 417; // 服务器处理异常
    ERR_ORDER_VERIFY_FAILED                     = 418; // 校验失败
    ERR_ORDER_UNBALLOWED_RECHARGE_AGE           = 419; // 年龄不可充值
    ERR_ORDER_SINGLE_LIMIT_AGE                  = 420; // 单笔充值金额限制
    ERR_ORDER_MONTH_LIMIT_AGE                   = 421; // 月充值金额限制

   /*************************************************************************************/
    /*                                    任务                                        */
    /*************************************************************************************/
    ERR_TASK_NOT_EXIST   = 500;  // 任务不存在
    ERR_TASK_NOT_FINISH  = 501;  // 任务未完成
    ERR_TASK_NOT_DISPOSE = 502;  // 任务类型不支持
   /*************************************************************************************/
    /*                                    排行榜                                        */
    /*************************************************************************************/
    ERR_RANK_NOT_EXIST   = 600;  // 排行榜不存在
    ERR_RANK_NOT_OPEN = 601;  // 排行榜未开放

   /*************************************************************************************/
    /*                                    活动                                        */
    /*************************************************************************************/
    ERR_ACTIVITY_NOT_EXIST       = 700; // 活动不存在
    ERR_ACTIVITY_NOT_STARTED     = 701; // 活动未开始
    ERR_ACTIVITY_ENDED           = 702; // 活动已结束
    ERR_ACTIVITY_ALREADY_CLAIMED = 703; // 奖励已领取
    ERR_ACTIVITY_NOT_COMPLETED   = 704; // 活动阶段未完成

}
