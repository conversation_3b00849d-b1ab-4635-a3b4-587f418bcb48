package rpc

import (
	"activitysrv/internal/services"
	"context"

	activityPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/activity"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/gm_handler"
)

type ActivityRpcServer struct {
	// TODO: 需要在 fancy-common 项目中添加 ActivityService 的 gRPC 服务定义
	// activityPB.UnimplementedActivityServiceServer
}

// GetActivityProgress 获取活动进度
func (s *ActivityRpcServer) GetActivityProgress(ctx context.Context, req *activityPB.GetActivityProgressReq) (*activityPB.GetActivityProgressRsp, error) {
	instance := services.GetActivityServiceInstance()
	return instance.GetActivityProgress(ctx, req)
}

// ClaimActivityReward 领取活动奖励
func (s *ActivityRpcServer) ClaimActivityReward(ctx context.Context, req *activityPB.ClaimActivityRewardReq) (*activityPB.ClaimActivityRewardRsp, error) {
	instance := services.GetActivityServiceInstance()
	return instance.ClaimActivityReward(ctx, req)
}

func InitActivityRpc() {
	gm_handler.InitGmHandler()

	// TODO: 注册RPC服务 - 需要在 fancy-common 项目中添加 ActivityService 的 gRPC 服务定义
	// rpcServer := &ActivityRpcServer{}
	// activityPB.RegisterActivityServiceServer(rpc.Server, rpcServer)

	instance := services.GetActivityServiceInstance()
	// 这里可以注册 GM 命令处理器
	// gm_handler.Handler(commonPB.GM_CMD_GM_ACTIVITY_OPERATE, instance.OperateActivity)
	_ = instance
}
