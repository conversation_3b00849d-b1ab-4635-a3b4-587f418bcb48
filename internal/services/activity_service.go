package services

import (
	"activitysrv/internal/logic/l_explosive_protection"
	"context"
	"sync"

	activityPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/activity"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

// IActivityService 活动服务接口
type IActivityService interface {
	// EventUpdate 处理事件更新
	EventUpdate(ctx context.Context, playerId uint64, event *commonPB.EventCommon)

	// GetActivityProgress 获取活动进度
	GetActivityProgress(ctx context.Context, req *activityPB.GetActivityProgressReq) (*activityPB.GetActivityProgressRsp, error)

	// ClaimActivityReward 领取活动奖励
	ClaimActivityReward(ctx context.Context, req *activityPB.ClaimActivityRewardReq) (*activityPB.ClaimActivityRewardRsp, error)

	// CheckRedDot 检查红点状态
	CheckRedDot(ctx context.Context, playerId uint64) (bool, error)
}

// ActivityService 活动服务实现
type ActivityService struct {
	explosiveProtectionLogic *l_explosive_protection.ExplosiveProtectionLogic
}

var (
	activityServiceInstance IActivityService
	activityServiceOnce     sync.Once
)

// GetActivityServiceInstance 获取活动服务单例
func GetActivityServiceInstance() IActivityService {
	activityServiceOnce.Do(func() {
		activityServiceInstance = NewActivityService()
	})
	return activityServiceInstance
}

// NewActivityService 创建新的活动服务
func NewActivityService() IActivityService {
	return &ActivityService{
		explosiveProtectionLogic: l_explosive_protection.NewExplosiveProtectionLogic(),
	}
}

// EventUpdate 处理事件更新
func (s *ActivityService) EventUpdate(ctx context.Context, playerId uint64, event *commonPB.EventCommon) {
	entry := logx.NewLogEntry(ctx)

	// 根据事件类型分发到不同的活动逻辑
	switch event.EventType {
	case commonPB.EVENT_TYPE_ET_FISH_GET:
		// 入护成功事件，处理爆护之路活动
		if err := s.explosiveProtectionLogic.HandleWeightUpdateEvent(ctx, playerId, event); err != nil {
			entry.Errorf("处理爆护之路入护事件失败: playerId=%d, err=%v", playerId, err)
		}
	case commonPB.EVENT_TYPE_ET_LOGIN:
		// 登录事件，可以用于触发活动状态检查
		entry.Debugf("处理登录事件: playerId=%d", playerId)
	default:
		entry.Debugf("未处理的事件类型: %v, playerId=%d", event.EventType, playerId)
	}
}

// GetActivityProgress 获取活动进度
func (s *ActivityService) GetActivityProgress(ctx context.Context, req *activityPB.GetActivityProgressReq) (*activityPB.GetActivityProgressRsp, error) {
	entry := logx.NewLogEntry(ctx)

	// 从请求中获取玩家ID
	playerId, err := protox.GetPlayerIdFromCtx(ctx)
	if err != nil {
		entry.Errorf("获取玩家ID失败: %v", err)
		return &activityPB.GetActivityProgressRsp{
			Ret: protox.ErrCode2PbRet(commonPB.ErrCode_ERR_BAD_PARAM),
		}, nil
	}

	// 根据活动ID分发到不同的逻辑
	switch req.ActivityId {
	case 1001: // 爆护之路活动
		progress, err := s.explosiveProtectionLogic.GetActivityProgress(ctx, playerId)
		if err != nil {
			entry.Errorf("获取爆护之路活动进度失败: playerId=%d, err=%v", playerId, err)
			return &activityPB.GetActivityProgressRsp{
				Ret: protox.ErrCode2PbRet(commonPB.ErrCode_ERR_INTERNAL),
			}, nil
		}

		// 转换为protobuf格式 (简化实现)
		// pbProgress := progress.ToProtoActivityProgress()
		return &activityPB.GetActivityProgressRsp{
			Ret: protox.ErrCode2PbRet(commonPB.ErrCode_ERR_SUCCESS),
			// Progress: pbProgress, // 暂时注释，等待protobuf定义
		}, nil

	default:
		entry.Errorf("不支持的活动ID: %d", req.ActivityId)
		return &activityPB.GetActivityProgressRsp{
			Ret: protox.ErrCode2PbRet(commonPB.ErrCode_ERR_BAD_PARAM),
		}, nil
	}
}

// ClaimActivityReward 领取活动奖励
func (s *ActivityService) ClaimActivityReward(ctx context.Context, req *activityPB.ClaimActivityRewardReq) (*activityPB.ClaimActivityRewardRsp, error) {
	entry := logx.NewLogEntry(ctx)

	// 从请求中获取玩家ID
	playerId, err := protox.GetPlayerIdFromCtx(ctx)
	if err != nil {
		entry.Errorf("获取玩家ID失败: %v", err)
		return &activityPB.ClaimActivityRewardRsp{
			Ret: protox.ErrCode2PbRet(commonPB.ErrCode_ERR_BAD_PARAM),
		}, nil
	}

	// 根据活动ID分发到不同的逻辑
	switch req.ActivityId {
	case 1001: // 爆护之路活动
		err := s.explosiveProtectionLogic.ClaimReward(ctx, playerId, req.CycleId, req.StageId)
		if err != nil {
			entry.Errorf("领取爆护之路奖励失败: playerId=%d, cycleId=%d, stageId=%d, err=%v", 
				playerId, req.CycleId, req.StageId, err)
			return &activityPB.ClaimActivityRewardRsp{
				Ret: protox.ErrCode2PbRet(commonPB.ErrCode_ERR_INTERNAL),
			}, nil
		}

		return &activityPB.ClaimActivityRewardRsp{
			Ret:        protox.ErrCode2PbRet(commonPB.ErrCode_ERR_SUCCESS),
			ActivityId: req.ActivityId,
			CycleId:    req.CycleId,
			StageId:    req.StageId,
		}, nil

	default:
		entry.Errorf("不支持的活动ID: %d", req.ActivityId)
		return &activityPB.ClaimActivityRewardRsp{
			Ret: protox.ErrCode2PbRet(commonPB.ErrCode_ERR_BAD_PARAM),
		}, nil
	}
}

// CheckRedDot 检查红点状态
func (s *ActivityService) CheckRedDot(ctx context.Context, playerId uint64) (bool, error) {
	entry := logx.NewLogEntry(ctx)

	// 检查爆护之路活动红点
	hasRedDot, err := s.explosiveProtectionLogic.CheckRedDot(ctx, playerId)
	if err != nil {
		entry.Errorf("检查爆护之路红点失败: playerId=%d, err=%v", playerId, err)
		return false, err
	}

	if hasRedDot {
		return true, nil
	}

	// 可以在这里添加其他活动的红点检查

	return false, nil
}
