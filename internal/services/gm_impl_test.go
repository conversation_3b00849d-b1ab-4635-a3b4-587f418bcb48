package services

import (
	"context"
	"testing"

	test_init "spotsrv/internal/test"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gmRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/gmrpc"
)

func TestGmService_ModifyPlayerEnergy(t *testing.T) {
	test_init.InitRedisConsul()
	ctx := context.Background()
	req := &gmRpc.GmCmdReq{
		Cmd:  commonPB.GM_CMD_GM_CMD_GC_MODIFY_ENERGY,
		Data: `{"player_id":1001,"energy_num":50}`,
	}
	rsp, err := GetGmInstance().ModifyPlayerEnergy(ctx, req)
	t.Logf("rsp: %v, err: %v", rsp, err)
}
