package services

import (
	"context"
	"fmt"
	logicEvent "spotsrv/internal/logic/logic_event"
	"sync"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	spotRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/spotrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

type SpotService struct {
}

var (
	onceSpot = &sync.Once{}
	instance *SpotService
)

func GetSpotServiceInstance() *SpotService {
	if instance != nil {
		return instance
	}

	onceSpot.Do(func() {
		instance = &SpotService{}
	})
	return instance
}

// PondChangeEventNotify 钓场事件通知
func (s *SpotService) PondChangeEventNotify(ctx context.Context, req *spotRpc.PondChangeEventNtf) (*commonPB.Result, error) {
	entry := logx.NewLogEntry(ctx)
	if req == nil || req.GetPlayerId() <= 0 || req.GetRoomInfo() == nil || len(req.GetEventList()) <= 0 {
		entry.Errorf("recv pond change event notify req error, req:%s", req.String())
		return protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM), fmt.Errorf("param error")
	}

	err := logicEvent.RecvPlayerInfoChangeEvent(ctx, req.GetPlayerId(), req.GetRoomInfo(), req.GetEventList())
	if err != nil {
		entry.Errorf("recv pond change event notify error, req:%s, err:%v", req.String(), err)
		return protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL), err
	}

	return protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS), nil
}
