package services

import (
	"activitysrv/config"
	"activitysrv/internal/model"
	"context"
	"testing"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"github.com/stretchr/testify/assert"
)

// 测试业务逻辑层的核心功能（不依赖Redis）
func TestExplosiveProtectionLogic_CoreLogic(t *testing.T) {
	service := NewActivityService()
	logic := service.(*ActivityService).explosiveProtectionLogic

	// 测试指标提取
	t.Run("ExtractMetricsFromEvent", func(t *testing.T) {
		event := &commonPB.EventCommon{
			EventType: commonPB.EVENT_TYPE_ET_FISH_GET,
			IntData: map[int32]int64{
				int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT): 2000, // 2kg
			},
		}

		metrics, err := logic.extractMetricsFromEvent(event, nil)
		assert.NoError(t, err, "提取指标应该成功")
		assert.Equal(t, int64(2000), metrics[model.MetricTypeFishWeight], "鱼重量应该正确")
		assert.Equal(t, int64(1), metrics[model.MetricTypeFishCount], "鱼数量应该正确")
		assert.Equal(t, int64(2000), metrics[model.MetricTypeMaxSingleWeight], "最大单次重量应该正确")
	})

	// 测试阶段配置
	t.Run("GetStageConfig", func(t *testing.T) {
		stageCfg, err := logic.getStageConfig(1)
		assert.NoError(t, err, "获取阶段配置应该成功")
		assert.NotNil(t, stageCfg, "阶段配置不应为空")
		assert.Equal(t, int32(1), stageCfg.StageId, "阶段ID应该正确")
		assert.NotEmpty(t, stageCfg.Conditions, "条件不应为空")
		assert.NotEmpty(t, stageCfg.Rewards, "奖励不应为空")

		// 测试不存在的阶段
		_, err = logic.getStageConfig(999)
		assert.Error(t, err, "获取不存在的阶段配置应该失败")
	})

	// 测试条件检查
	t.Run("CheckStageCondition", func(t *testing.T) {
		userData := model.NewUserActivityData()
		userData.UpdateMetric(model.MetricTypeFishWeight, 2000, model.MetricOperationAdd) // 2kg

		stageCfg, err := logic.getStageConfig(1) // 需要1kg
		assert.NoError(t, err, "获取阶段配置应该成功")

		canClaim := logic.checkStageCondition(userData, stageCfg)
		assert.True(t, canClaim, "应该满足领取条件")

		// 测试不满足条件
		userData2 := model.NewUserActivityData()
		userData2.UpdateMetric(model.MetricTypeFishWeight, 500, model.MetricOperationAdd) // 0.5kg

		canClaim2 := logic.checkStageCondition(userData2, stageCfg)
		assert.False(t, canClaim2, "不应该满足领取条件")
	})

	// 测试活动状态检查
	t.Run("IsActivityActive", func(t *testing.T) {
		// 测试活跃的活动
		activeCfg := &cmodel.Activity{
			Status:    1,
			StartTime: 0, // 没有开始时间限制
			EndTime:   0, // 没有结束时间限制
		}

		isActive := logic.isActivityActive(activeCfg)
		assert.True(t, isActive, "活动应该是活跃的")

		// 测试非活跃的活动
		inactiveCfg := &cmodel.Activity{
			Status: 0, // 非活跃状态
		}

		isActive2 := logic.isActivityActive(inactiveCfg)
		assert.False(t, isActive2, "活动应该是非活跃的")
	})

	// 测试事件指标检查
	t.Run("HasActivityMetrics", func(t *testing.T) {
		// 包含指标的事件
		event1 := &commonPB.EventCommon{
			EventType: commonPB.EVENT_TYPE_ET_FISH_GET,
			IntData: map[int32]int64{
				int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT): 1500,
			},
		}

		hasMetrics1 := logic.hasActivityMetrics(event1, nil)
		assert.True(t, hasMetrics1, "应该包含活动指标")

		// 不包含指标的事件
		event2 := &commonPB.EventCommon{
			EventType: commonPB.EVENT_TYPE_ET_LOGIN,
			IntData:   map[int32]int64{},
		}

		hasMetrics2 := logic.hasActivityMetrics(event2, nil)
		assert.False(t, hasMetrics2, "不应该包含活动指标")
	})

	// 测试奖励配置构建
	t.Run("BuildRewardConfig", func(t *testing.T) {
		stageCfg, err := logic.getStageConfig(1)
		assert.NoError(t, err, "获取阶段配置应该成功")

		rewardConfig := logic.buildRewardConfig(stageCfg)
		assert.NotEmpty(t, rewardConfig, "奖励配置不应为空")
		assert.Contains(t, rewardConfig, "item_id", "奖励配置应该包含道具ID")
		assert.Contains(t, rewardConfig, "count", "奖励配置应该包含数量")
	})

	// 测试可领取奖励检查
	t.Run("CheckAvailableRewards", func(t *testing.T) {
		stageConfigs, err := logic.getAllStageConfigs()
		assert.NoError(t, err, "获取所有阶段配置应该成功")

		// 有可领取奖励的情况
		metrics := map[int32]int64{
			model.MetricTypeFishWeight: 2000, // 2kg，满足第一阶段
		}
		claimedRecords := []int32{} // 没有领取过

		hasReward := logic.checkAvailableRewards(metrics, claimedRecords, stageConfigs)
		assert.True(t, hasReward, "应该有可领取的奖励")

		// 已经领取过的情况
		claimedRecords2 := []int32{1} // 已领取第一阶段
		hasReward2 := logic.checkAvailableRewards(metrics, claimedRecords2, stageConfigs)
		// 这里的结果取决于是否还有其他满足条件的阶段
		assert.IsType(t, false, hasReward2, "返回值应该是布尔类型")
	})
}

// 测试配置常量和工具函数
func TestConfigUtils(t *testing.T) {
	// 测试Redis Key生成
	t.Run("RedisKeyGeneration", func(t *testing.T) {
		activityId := int64(1001)
		playerId := uint64(12345)
		cycleId := int32(1)

		// 测试活动周期Key
		cycleKey := config.ActivityCycleKey(activityId)
		assert.Equal(t, "activity:1001", cycleKey, "活动周期Key应该正确")

		// 测试玩家数据Key
		playerKey := config.PlayerActivityDataKey(activityId, playerId, cycleId)
		assert.Equal(t, "act:1001:12345:1", playerKey, "玩家数据Key应该正确")
	})

	// 测试TTL计算
	t.Run("TTLCalculation", func(t *testing.T) {
		cycleDays := int32(7)
		remainingDays := int32(3)

		ttl := config.CalculateActivityTTL(cycleDays, remainingDays)
		
		// TTL应该是 (3 + 7 + 1) * 24小时 + 随机时间
		expectedMinHours := (3 + 7 + 1) * 24
		expectedMaxHours := expectedMinHours + 24
		
		actualHours := int(ttl.Hours())
		assert.True(t, actualHours >= expectedMinHours, "TTL应该大于等于最小值")
		assert.True(t, actualHours <= expectedMaxHours, "TTL应该小于等于最大值")
	})
}

// 测试错误处理
func TestErrorHandling(t *testing.T) {
	service := NewActivityService()
	logic := service.(*ActivityService).explosiveProtectionLogic

	// 测试获取不存在的阶段配置
	t.Run("GetNonExistentStageConfig", func(t *testing.T) {
		_, err := logic.getStageConfig(999)
		assert.Error(t, err, "获取不存在的阶段配置应该返回错误")
		assert.Contains(t, err.Error(), "阶段配置不存在", "错误信息应该包含相关描述")
	})

	// 测试空事件处理
	t.Run("HandleEmptyEvent", func(t *testing.T) {
		event := &commonPB.EventCommon{
			EventType: commonPB.EVENT_TYPE_ET_FISH_GET,
			IntData:   map[int32]int64{}, // 空数据
		}

		metrics, err := logic.extractMetricsFromEvent(event, nil)
		assert.NoError(t, err, "处理空事件应该成功")
		assert.Equal(t, 0, len(metrics), "空事件应该返回空指标")
	})
}

// 测试服务层基本功能
func TestActivityService_Basic(t *testing.T) {
	// 创建服务实例
	service := NewActivityService()
	ctx := context.Background()
	playerId := uint64(12345)

	// 测试事件处理（不会真正连接Redis）
	t.Run("EventUpdate_NoRedis", func(t *testing.T) {
		// 模拟入护事件
		event := &commonPB.EventCommon{
			EventType: commonPB.EVENT_TYPE_ET_FISH_GET,
			PlayerId:  playerId,
			ProductId: 1,
			IntData: map[int32]int64{
				int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT): 1500, // 1.5kg
			},
		}

		// 处理事件（这里不会真正连接Redis，但可以测试代码逻辑）
		// 主要验证代码不会panic
		assert.NotPanics(t, func() {
			service.EventUpdate(ctx, playerId, event)
		}, "事件处理不应该panic")
	})

	// 测试红点检查（不会真正连接Redis）
	t.Run("CheckRedDot_NoRedis", func(t *testing.T) {
		// 检查红点状态
		// 主要验证代码不会panic
		assert.NotPanics(t, func() {
			_, _ = service.CheckRedDot(ctx, playerId)
		}, "红点检查不应该panic")
	})
}

// 性能测试
func BenchmarkExtractMetricsFromEvent(b *testing.B) {
	service := NewActivityService()
	logic := service.(*ActivityService).explosiveProtectionLogic

	event := &commonPB.EventCommon{
		EventType: commonPB.EVENT_TYPE_ET_FISH_GET,
		IntData: map[int32]int64{
			int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT): 1500,
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = logic.extractMetricsFromEvent(event, nil)
	}
}

func BenchmarkCheckStageCondition(b *testing.B) {
	service := NewActivityService()
	logic := service.(*ActivityService).explosiveProtectionLogic

	userData := model.NewUserActivityData()
	userData.UpdateMetric(model.MetricTypeFishWeight, 2000, model.MetricOperationAdd)

	stageCfg, _ := logic.getStageConfig(1)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = logic.checkStageCondition(userData, stageCfg)
	}
}

func BenchmarkActivityCycleKey(b *testing.B) {
	activityId := int64(1001)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = config.ActivityCycleKey(activityId)
	}
}

func BenchmarkPlayerActivityDataKey(b *testing.B) {
	activityId := int64(1001)
	playerId := uint64(12345)
	cycleId := int32(1)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = config.PlayerActivityDataKey(activityId, playerId, cycleId)
	}
}
