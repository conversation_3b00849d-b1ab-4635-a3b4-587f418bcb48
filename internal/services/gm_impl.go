package services

import (
	"context"
	"sync"

	daoPlayer "spotsrv/internal/dao/dao_player"
	logicEvent "spotsrv/internal/logic/logic_event"
	logicNotify "spotsrv/internal/logic/logic_notify"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gmPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gm"
	gmRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/gmrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_hall"
	"google.golang.org/protobuf/encoding/protojson"
)

type GmService struct{}

var (
	gmOnce              = &sync.Once{}
	gmSingletonInstance *GmService
)

func GetGmInstance() *GmService {
	if gmSingletonInstance != nil {
		return gmSingletonInstance
	}
	gmOnce.Do(func() {
		gmSingletonInstance = &GmService{}
	})
	return gmSingletonInstance
}

// ModifyPlayerEnergy 修改玩家体力
func (g *GmService) ModifyPlayerEnergy(ctx context.Context, req *gmRpc.GmCmdReq) (*gmRpc.GmCmdRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("[gm:modifyEnergy]:%s", req.String())

	rsp := &gmRpc.GmCmdRsp{
		Ret: protox.DefaultResult(),
	}

	reqd := &gmPB.GmCmdModifyEnergyReq{}
	err := protojson.Unmarshal([]byte(req.Data), reqd)
	if err != nil {
		return rsp, err
	}

	if reqd.GetPlayerId() <= 0 || reqd.GetEnergyNum() < 0 {
		entry.Errorf("[gm:modifyEnergy] invalid param:%s", reqd.String())
		return rsp, nil
	}

	// 玩家是否在钓场
	playerInfo, err := daoPlayer.QuerySpotPlayerInfo(ctx, reqd.GetPlayerId())
	if playerInfo == nil || err != nil {
		entry.Errorf("gm:modifyEnergy] query player:%d info error:%v", reqd.GetPlayerId(), err)
		return rsp, nil
	}

	playerInfo.Energy = reqd.GetEnergyNum()

	// 查询玩家基本数据
	playerBaseInfo, err := crpc_hall.RpcQueryPlayerBaseInfo(ctx, reqd.GetPlayerId())
	if err != nil {
		entry.Errorf("gm:modifyEnergy] query player:%d base info error:%v", reqd.GetPlayerId(), err)
		return rsp, nil
	}

	// 当前等级的体力
	maxEnergy, err := logicEvent.GetMaxEnergyForExpLevel(ctx, int64(playerBaseInfo.ExpLevel))
	if err != nil {
		entry.Errorf("[gm:modifyEnergy] get max energy for level:%d error:%v", playerBaseInfo.ExpLevel, err)
		return rsp, nil
	}

	// 超过最大值 赋值为最大值
	if playerInfo.Energy > maxEnergy {
		playerInfo.Energy = maxEnergy
	}

	// 更新体力值
	err = daoPlayer.UpdateSpotPlayerRds(ctx, reqd.GetPlayerId(), playerInfo.BuildRedisHMSetMap([]string{"energy"}))
	if err != nil {
		entry.Errorf("[gm:modifyEnergy] update player:%d energy error:%v", reqd.GetPlayerId(), err)
		return rsp, nil
	}

	// 通知客户端刷新体力
	logicNotify.SendPlayerEnergyChange(ctx, reqd.GetPlayerId(), playerInfo.Energy)

	entry.Debugf("[gm:modifyEnergy] player:%d energy:%d success", reqd.GetPlayerId(), reqd.GetEnergyNum())

	rsp.Data = "success"
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	return rsp, nil
}
