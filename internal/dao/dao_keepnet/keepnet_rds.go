package daoKeepnet

import (
	"context"
	"errors"
	"fmt"
	"spotsrv/internal/config"
	modelKeepnet "spotsrv/internal/model/model_keepnet"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/go-redis/redis/v8"
)

// 鱼护相关redis操作

// AddPlayerKeepnetFish 添加鱼到鱼护
func AddPlayerKeepnetFish(ctx context.Context, playerId uint64, fishDetail *modelKeepnet.FishDetailInfo) error {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || fishDetail == nil {
		entry.Errorf("AddPlayerKeepnetFish invalid param playerId:%d fishDetail:%v", playerId, fishDetail)
		return fmt.Errorf("invalid param playerId:%d fishDetail:%v", playerId, fishDetail)
	}

	pipLine := redisx.GetGameCli().TxPipeline()
	pipLine.HSet(ctx, config.FmtPlayerFishKeepnetRdsKey(playerId), fishDetail.FishInstance, fishDetail.ToJsonStr())
	pipLine.Expire(ctx, config.FmtPlayerFishKeepnetRdsKey(playerId), config.FISH_KEEPNET_EXPIRE)

	_, err := pipLine.Exec(ctx)
	// 更新鱼护玩家信息
	if err == nil {
		AddKeepnetPlayer(ctx, playerId, timex.Now().Unix())
	}

	return err
}

// DiscardFishKeepnet 摧毁鱼护中的鱼
func DiscardFishKeepnet(ctx context.Context, playerId uint64, fishInstance string) error {

	entry := logx.NewLogEntry(ctx)
	if playerId == 0 {
		entry.Errorf("DiscardFishKeepnet invalid param playerId:%d", playerId)
		return fmt.Errorf("invalid param playerId:%d", playerId)
	}

	pipLine := redisx.GetGameCli().TxPipeline()
	pipLine.HDel(ctx, config.FmtPlayerFishKeepnetRdsKey(playerId), fishInstance)
	pipLine.Expire(ctx, config.FmtPlayerFishKeepnetRdsKey(playerId), config.FISH_KEEPNET_EXPIRE)

	_, err := pipLine.Exec(ctx)
	
	// 更新鱼护玩家信息
	if err == nil {
		AddKeepnetPlayer(ctx, playerId, timex.Now().Unix())
	}

	return err
}

// ClearPlayerKeepnetFish 清空鱼护中的鱼
func ClearPlayerKeepnetFish(ctx context.Context, playerId uint64) error {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 {
		entry.Errorf("ClearPlayerKeepnetFish invalid param playerId:%d", playerId)
		return fmt.Errorf("invalid param playerId:%d", playerId)
	}

	pipLine := redisx.GetGameCli().TxPipeline()

	pipLine.Del(ctx, config.FmtPlayerFishKeepnetRdsKey(playerId))
	pipLine.ZRem(ctx, config.RDS_KEEPNET_PLAYER, playerId)

	_, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Errorf("del player:%d fishKeepnet redis error:%v", playerId, err)
	}

	return err
}

// GetPlayerKeepnetFish 查询鱼护中的所有鱼信息
func GetPlayerKeepnetFish(ctx context.Context, playerId uint64) (map[string]*modelKeepnet.FishDetailInfo, error) {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 {
		entry.Errorf("GetPlayerKeepnetFish invalid param playerId:%d", playerId)
		return nil, fmt.Errorf("invalid param playerId:%d", playerId)
	}

	fishMap, err := redisx.GetGameCli().HGetAll(ctx, config.FmtPlayerFishKeepnetRdsKey(playerId)).Result()

	fishList := make(map[string]*modelKeepnet.FishDetailInfo)

	// 没有数据 返回空
	if errors.Is(err, redis.Nil) {
		return fishList, nil
	}

	if err != nil {
		entry.Errorf("GetPlayerKeepnetFish redis error:%v", err)
		return nil, err
	}

	for fishInstance, fishJson := range fishMap {
		fishDetail := &modelKeepnet.FishDetailInfo{}
		err := fishDetail.InitFromJson(fishJson)
		if err != nil {
			entry.Errorf("GetPlayerKeepnetFish json error:%v", err)
			continue
		}
		fishList[fishInstance] = fishDetail
	}

	return fishList, nil
}

// GetPlayerKeepnetFishBatch 批量查询玩家鱼护中鱼的信息
func GetPlayerKeepnetFishBatch(ctx context.Context, playerIdList []uint64) (map[uint64]*modelKeepnet.FishKeepnet, error) {
	entry := logx.NewLogEntry(ctx)
	if len(playerIdList) == 0 {
		entry.Warnf("get keepnet batch invalid param playerIdList:%v", playerIdList)
		return nil, fmt.Errorf("invalid param playerIdList:%v", playerIdList)
	}

	pipLine := redisx.GetGameCli().TxPipeline()
	for _, playerId := range playerIdList {
		pipLine.HGetAll(ctx, config.FmtPlayerFishKeepnetRdsKey(playerId))
	}

	values, err := pipLine.Exec(ctx)
	if err != nil || len(values) != len(playerIdList) {
		entry.Errorf("get keepnet batch playerList:%v redis values:%v error:%v", playerIdList, values, err)
		return nil, err
	}

	fishListMap := make(map[uint64]*modelKeepnet.FishKeepnet, len(playerIdList))
	for i, cmd := range values {
		playerId := playerIdList[i]
		fishMap, err := cmd.(*redis.StringStringMapCmd).Result()
		if errors.Is(err, redis.Nil) {
			continue
		}
		if err != nil {
			entry.Errorf("get keepnet batch player:%d redis error:%v", playerId, err)
			return nil, err
		}
		fishList := make(map[string]*modelKeepnet.FishDetailInfo)
		for fishInstance, fishJson := range fishMap {
			fishDetail := &modelKeepnet.FishDetailInfo{}
			err := fishDetail.InitFromJson(fishJson)
			if err != nil {
				entry.Errorf("init fish detail json error:%v", err)
				continue
			}
			fishList[fishInstance] = fishDetail
		}

		keepnetInfo := modelKeepnet.NewFishKeepnetFromRedisHash(ctx, fishList)
		if keepnetInfo != nil {
			fishListMap[playerId] = modelKeepnet.NewFishKeepnetFromRedisHash(ctx, fishList)
		}
	}

	return fishListMap, nil
}


// BatchDelPlayerKeepnet 批量删除玩家鱼护信息(针对鱼护过期玩家)
func BatchDelPlayerKeepnet(ctx context.Context, playerIds []uint64) error {
	entry := logx.NewLogEntry(ctx)
	if len(playerIds) == 0 {
		return nil
	}

	pipLine := redisx.GetGameCli().TxPipeline()
	for _, playerId := range playerIds {
		pipLine.Del(ctx, config.FmtPlayerFishKeepnetRdsKey(playerId))
		pipLine.ZRem(ctx, config.RDS_KEEPNET_PLAYER, playerId)
	}

	_, err := pipLine.Exec(ctx)
	if err != nil {
		entry.Errorf("del keepnet batch playerList:%v error:%v", playerIds, err)
	}

	return err
}