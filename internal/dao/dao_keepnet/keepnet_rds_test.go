package daoKeepnet

import (
	"context"
	"testing"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"github.com/spf13/viper"
)

func init() {
	conf := map[string]string{
		"addr":   "************:6379",
		"passwd": "8888",
	}
	viper.SetDefault("redis_list", map[string]interface{}{
		dict_redis.RDBGame: conf,
	})
}

func TestGetPlayerKeepnetFish(t *testing.T) {
	
	fishMap, err := GetPlayerKeepnetFish(context.TODO(), 179)
	if err != nil {
		t.<PERSON>rror(err)
	}

	t.Logf("get player keepnet fish: %v", fishMap)
}
