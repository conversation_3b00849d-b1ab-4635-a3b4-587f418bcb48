package dao_explosive_protection

import (
	"activitysrv/config"
	"activitysrv/internal/model"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/redisx"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

// UserDataDAO 用户活动数据访问对象
type UserDataDAO struct{}

// NewUserDataDAO 创建新的用户数据DAO
func NewUserDataDAO() *UserDataDAO {
	return &UserDataDAO{}
}

// GetUserData 获取用户活动数据 (按照需求文档格式)
func (dao *UserDataDAO) GetUserData(ctx context.Context, activityId int64, userId uint64, cycleId int32) (*model.UserActivityData, error) {
	key := config.PlayerActivityDataKey(activityId, userId, cycleId)
	redisCli := redisx.GetClient("activity")

	// 使用 HGETALL 获取所有字段
	data, err := redisCli.HGetAll(ctx, key).Result()
	if err != nil {
		return nil, fmt.Errorf("获取用户活动数据失败: %w", err)
	}

	// 如果没有数据，返回初始化的数据
	if len(data) == 0 {
		return model.NewUserActivityData(), nil
	}

	userData := model.NewUserActivityData()

	// 解析 metric 字段
	if metricStr, exists := data["metric"]; exists && metricStr != "" {
		if err := json.Unmarshal([]byte(metricStr), &userData.Metrics); err != nil {
			return nil, fmt.Errorf("解析指标数据失败: %w", err)
		}
	}

	// 解析 stages 字段
	if stagesStr, exists := data["stages"]; exists && stagesStr != "" {
		var stages []int32
		if err := json.Unmarshal([]byte(stagesStr), &stages); err != nil {
			return nil, fmt.Errorf("解析阶段数据失败: %w", err)
		}
		// 转换为 map 格式
		userData.ClaimedStages = make(map[int32]int64)
		for _, stageId := range stages {
			userData.ClaimedStages[stageId] = time.Now().Unix()
		}
	}

	// 确保数据结构已初始化
	userData.EnsureInitialized()

	return userData, nil
}

// SaveUserData 保存用户活动数据 (按照需求文档格式)
func (dao *UserDataDAO) SaveUserData(ctx context.Context, activityId int64, userId uint64, cycleId int32, userData *model.UserActivityData) error {
	key := config.PlayerActivityDataKey(activityId, userId, cycleId)
	redisCli := redisx.GetClient("activity")

	// 更新时间戳
	userData.UpdatedAt = time.Now().Unix()

	// 序列化指标数据
	metricData, err := json.Marshal(userData.Metrics)
	if err != nil {
		return fmt.Errorf("序列化指标数据失败: %w", err)
	}

	// 序列化阶段数据 (转换为数组格式)
	stages := userData.GetClaimedStagesList()
	stagesData, err := json.Marshal(stages)
	if err != nil {
		return fmt.Errorf("序列化阶段数据失败: %w", err)
	}

	// 计算TTL (当前周期剩余天数 + 周期天数 + 1 + 随机1天)
	// 这里简化处理，使用固定的7天周期
	ttl := config.CalculateActivityTTL(7, 1)

	// 使用 HSET 保存数据
	pipe := redisCli.Pipeline()
	pipe.HSet(ctx, key, "metric", string(metricData))
	pipe.HSet(ctx, key, "stages", string(stagesData))
	pipe.Expire(ctx, key, ttl)

	_, err = pipe.Exec(ctx)
	return err
}

// UpdateUserMetrics 原子性更新用户指标 (按照需求文档格式)
func (dao *UserDataDAO) UpdateUserMetrics(ctx context.Context, activityId int64, userId uint64, cycleId int32,
	metricUpdates map[int32]int64, operation model.MetricOperationType) error {

	key := config.PlayerActivityDataKey(activityId, userId, cycleId)
	redisCli := redisx.GetClient("activity")

	// 使用Redis事务保证原子性
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		err := redisCli.Watch(ctx, func(tx *redis.Tx) error {
			// 获取当前数据
			userData, err := dao.GetUserData(ctx, activityId, userId, cycleId)
			if err != nil {
				return err
			}

			// 更新指标
			for metricType, value := range metricUpdates {
				userData.UpdateMetric(metricType, value, operation)
			}

			// 序列化指标数据
			metricData, err := json.Marshal(userData.Metrics)
			if err != nil {
				return fmt.Errorf("序列化指标数据失败: %w", err)
			}

			// 计算TTL
			ttl := config.CalculateActivityTTL(7, 1)

			_, err = tx.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
				pipe.HSet(ctx, key, "metric", string(metricData))
				pipe.Expire(ctx, key, ttl)
				return nil
			})

			return err
		}, key)

		if err == nil {
			return nil
		}

		if errors.Is(err, redis.TxFailedErr) {
			// 事务冲突，重试
			logrus.Warnf("更新用户指标事务冲突，重试 %d/%d: userId=%d, cycleId=%d", i+1, maxRetries, userId, cycleId)
			time.Sleep(time.Duration(i+1) * 10 * time.Millisecond) // 指数退避
			continue
		}

		return fmt.Errorf("更新用户指标失败: %w", err)
	}

	return fmt.Errorf("更新用户指标失败，超过最大重试次数")
}

// ClaimReward 原子性领取奖励 (按照需求文档格式)
func (dao *UserDataDAO) ClaimReward(ctx context.Context, activityId int64, userId uint64, cycleId int32,
	stageId int32, rewardConfig string) error {

	key := config.PlayerActivityDataKey(activityId, userId, cycleId)
	redisCli := redisx.GetClient("activity")

	// 使用Redis事务保证原子性
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		err := redisCli.Watch(ctx, func(tx *redis.Tx) error {
			// 获取当前数据
			userData, err := dao.GetUserData(ctx, activityId, userId, cycleId)
			if err != nil {
				return err
			}

			// 检查是否已经领取过
			if userData.IsStageClaimedStage(stageId) {
				return fmt.Errorf("阶段 %d 已经领取过奖励", stageId)
			}

			// 领取奖励
			userData.ClaimStage(stageId, rewardConfig)

			// 序列化阶段数据
			stages := userData.GetClaimedStagesList()
			stagesData, err := json.Marshal(stages)
			if err != nil {
				return fmt.Errorf("序列化阶段数据失败: %w", err)
			}

			// 计算TTL
			ttl := config.CalculateActivityTTL(7, 1)

			_, err = tx.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
				pipe.HSet(ctx, key, "stages", string(stagesData))
				pipe.Expire(ctx, key, ttl)
				return nil
			})

			return err
		}, key)

		if err == nil {
			return nil
		}

		if errors.Is(err, redis.TxFailedErr) {
			// 事务冲突，重试
			logrus.Warnf("领取奖励事务冲突，重试 %d/%d: userId=%d, cycleId=%d, stageId=%d",
				i+1, maxRetries, userId, cycleId, stageId)
			time.Sleep(time.Duration(i+1) * 10 * time.Millisecond) // 指数退避
			continue
		}

		return fmt.Errorf("领取奖励失败: %w", err)
	}

	return fmt.Errorf("领取奖励失败，超过最大重试次数")
}

// GetUserDataBatch 批量获取用户数据（用于获取多个周期的数据）
func (dao *UserDataDAO) GetUserDataBatch(ctx context.Context, activityId int64, userId uint64, cycleIds []int32) (map[int32]*model.UserActivityData, error) {
	if len(cycleIds) == 0 {
		return make(map[int32]*model.UserActivityData), nil
	}

	userDataMap := make(map[int32]*model.UserActivityData)

	// 逐个获取数据 (因为新格式使用 Hash，无法批量获取)
	for _, cycleId := range cycleIds {
		userData, err := dao.GetUserData(ctx, activityId, userId, cycleId)
		if err != nil {
			logrus.Errorf("获取用户数据失败: cycleId=%d, err=%v", cycleId, err)
			userDataMap[cycleId] = model.NewUserActivityData()
			continue
		}
		userDataMap[cycleId] = userData
	}

	return userDataMap, nil
}

// DeleteUserData 删除用户数据（用于数据清理）
func (dao *UserDataDAO) DeleteUserData(ctx context.Context, activityId int64, userId uint64, cycleId int32) error {
	key := config.PlayerActivityDataKey(activityId, userId, cycleId)
	redisCli := redisx.GetClient("activity")

	return redisCli.Del(ctx, key).Err()
}
