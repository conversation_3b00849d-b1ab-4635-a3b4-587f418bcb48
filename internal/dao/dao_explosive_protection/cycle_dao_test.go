package dao_explosive_protection

import (
	"activitysrv/internal/model"
	"context"
	"testing"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/testx"
	"github.com/stretchr/testify/assert"
)

func TestCycleDAO_CreateNewCycle(t *testing.T) {
	// 初始化测试环境
	testx.Init()
	ctx := testx.TestCtx(1, 1)
	
	dao := NewCycleDAO()
	activityId := int64(1001)
	cycleDays := int32(7)
	
	// 创建新周期
	cycle, err := dao.CreateNewCycle(ctx, activityId, cycleDays)
	
	// 验证结果
	assert.NoError(t, err, "创建新周期应该成功")
	assert.NotNil(t, cycle, "周期数据不应为空")
	assert.Equal(t, int32(1), cycle.CycleId, "第一个周期ID应该是1")
	assert.Equal(t, int32(1), cycle.Status, "周期状态应该是活跃的")
	assert.True(t, cycle.EndTime > cycle.StartTime, "结束时间应该大于开始时间")
	
	// 验证周期时长
	expectedDuration := int64(cycleDays) * 24 * 3600
	actualDuration := cycle.EndTime - cycle.StartTime
	assert.InDelta(t, expectedDuration, actualDuration, 60, "周期时长应该正确(允许60秒误差)")
}

func TestCycleDAO_GetCurrentCycle(t *testing.T) {
	// 初始化测试环境
	testx.Init()
	ctx := testx.TestCtx(1, 1)
	
	dao := NewCycleDAO()
	activityId := int64(1001)
	
	// 第一次获取，应该没有当前周期
	cycle, err := dao.GetCurrentCycle(ctx, activityId)
	assert.NoError(t, err, "获取当前周期应该成功")
	assert.Nil(t, cycle, "第一次获取应该没有当前周期")
	
	// 创建一个周期
	createdCycle, err := dao.CreateNewCycle(ctx, activityId, 7)
	assert.NoError(t, err, "创建新周期应该成功")
	
	// 再次获取，应该能获取到刚创建的周期
	cycle, err = dao.GetCurrentCycle(ctx, activityId)
	assert.NoError(t, err, "获取当前周期应该成功")
	assert.NotNil(t, cycle, "应该能获取到当前周期")
	assert.Equal(t, createdCycle.CycleId, cycle.CycleId, "周期ID应该一致")
	assert.Equal(t, createdCycle.EndTime, cycle.EndTime, "结束时间应该一致")
}

func TestCycleDAO_CheckAndCreateCycleIfNeeded(t *testing.T) {
	// 初始化测试环境
	testx.Init()
	ctx := testx.TestCtx(1, 1)
	
	dao := NewCycleDAO()
	activityId := int64(1001)
	cycleDays := int32(7)
	
	// 第一次调用，应该创建新周期
	cycle1, err := dao.CheckAndCreateCycleIfNeeded(ctx, activityId, cycleDays)
	assert.NoError(t, err, "检查并创建周期应该成功")
	assert.NotNil(t, cycle1, "应该创建新周期")
	assert.Equal(t, int32(1), cycle1.CycleId, "第一个周期ID应该是1")
	
	// 第二次调用，应该返回相同的周期
	cycle2, err := dao.CheckAndCreateCycleIfNeeded(ctx, activityId, cycleDays)
	assert.NoError(t, err, "检查并创建周期应该成功")
	assert.NotNil(t, cycle2, "应该返回现有周期")
	assert.Equal(t, cycle1.CycleId, cycle2.CycleId, "应该返回相同的周期")
	assert.Equal(t, cycle1.EndTime, cycle2.EndTime, "结束时间应该一致")
}

func TestCycleDAO_GetPreviousCycle(t *testing.T) {
	// 初始化测试环境
	testx.Init()
	ctx := testx.TestCtx(1, 1)
	
	dao := NewCycleDAO()
	activityId := int64(1001)
	
	// 没有当前周期时，应该没有上一个周期
	prevCycle, err := dao.GetPreviousCycle(ctx, activityId)
	assert.NoError(t, err, "获取上一个周期应该成功")
	assert.Nil(t, prevCycle, "没有当前周期时应该没有上一个周期")
	
	// 创建第一个周期
	cycle1, err := dao.CreateNewCycle(ctx, activityId, 7)
	assert.NoError(t, err, "创建新周期应该成功")
	
	// 第一个周期时，应该没有上一个周期
	prevCycle, err = dao.GetPreviousCycle(ctx, activityId)
	assert.NoError(t, err, "获取上一个周期应该成功")
	assert.Nil(t, prevCycle, "第一个周期时应该没有上一个周期")
	
	// 模拟创建第二个周期（通过手动设置周期ID）
	// 这里简化测试，实际中需要等待周期过期或手动触发
	cycle2 := &model.ActivityCycle{
		CycleId:   2,
		StartTime: cycle1.EndTime,
		EndTime:   cycle1.EndTime + 7*24*3600,
		Status:    1,
		CreatedAt: time.Now().Unix(),
	}
	
	// 保存第二个周期
	err = dao.saveCurrentCycle(ctx, activityId, cycle2)
	assert.NoError(t, err, "保存第二个周期应该成功")
	
	// 现在应该能获取到上一个周期
	prevCycle, err = dao.GetPreviousCycle(ctx, activityId)
	assert.NoError(t, err, "获取上一个周期应该成功")
	assert.NotNil(t, prevCycle, "应该能获取到上一个周期")
	assert.Equal(t, int32(1), prevCycle.CycleId, "上一个周期ID应该是1")
}

func TestCycleDAO_GetNextCycleId(t *testing.T) {
	// 初始化测试环境
	testx.Init()
	ctx := testx.TestCtx(1, 1)
	
	dao := NewCycleDAO()
	activityId := int64(1001)
	
	// 第一次获取，应该返回1
	nextId, err := dao.getNextCycleId(ctx, activityId)
	assert.NoError(t, err, "获取下一个周期ID应该成功")
	assert.Equal(t, int32(1), nextId, "第一个周期ID应该是1")
	
	// 创建一个周期后，下一个ID应该是2
	_, err = dao.CreateNewCycle(ctx, activityId, 7)
	assert.NoError(t, err, "创建新周期应该成功")
	
	nextId, err = dao.getNextCycleId(ctx, activityId)
	assert.NoError(t, err, "获取下一个周期ID应该成功")
	assert.Equal(t, int32(2), nextId, "下一个周期ID应该是2")
}

func TestActivityCycle_IsActive(t *testing.T) {
	now := time.Now().Unix()
	
	// 测试活跃的周期
	activeCycle := &model.ActivityCycle{
		CycleId:   1,
		StartTime: now - 3600, // 1小时前开始
		EndTime:   now + 3600, // 1小时后结束
		Status:    1,          // 活跃状态
	}
	
	assert.True(t, activeCycle.IsActive(), "周期应该是活跃的")
	
	// 测试未开始的周期
	futureCycle := &model.ActivityCycle{
		CycleId:   2,
		StartTime: now + 3600, // 1小时后开始
		EndTime:   now + 7200, // 2小时后结束
		Status:    1,          // 活跃状态
	}
	
	assert.False(t, futureCycle.IsActive(), "周期应该是未开始的")
	
	// 测试已结束的周期
	pastCycle := &model.ActivityCycle{
		CycleId:   3,
		StartTime: now - 7200, // 2小时前开始
		EndTime:   now - 3600, // 1小时前结束
		Status:    1,          // 活跃状态
	}
	
	assert.False(t, pastCycle.IsActive(), "周期应该是已结束的")
	
	// 测试非活跃状态的周期
	inactiveCycle := &model.ActivityCycle{
		CycleId:   4,
		StartTime: now - 3600, // 1小时前开始
		EndTime:   now + 3600, // 1小时后结束
		Status:    2,          // 非活跃状态
	}
	
	assert.False(t, inactiveCycle.IsActive(), "非活跃状态的周期应该不是活跃的")
}

func TestActivityCycle_IsExpired(t *testing.T) {
	now := time.Now().Unix()
	
	// 测试未过期的周期
	activeCycle := &model.ActivityCycle{
		EndTime: now + 3600, // 1小时后结束
	}
	
	assert.False(t, activeCycle.IsExpired(), "周期应该未过期")
	
	// 测试已过期的周期
	expiredCycle := &model.ActivityCycle{
		EndTime: now - 3600, // 1小时前结束
	}
	
	assert.True(t, expiredCycle.IsExpired(), "周期应该已过期")
	
	// 测试刚好过期的周期
	justExpiredCycle := &model.ActivityCycle{
		EndTime: now, // 现在结束
	}
	
	assert.True(t, justExpiredCycle.IsExpired(), "刚好过期的周期应该是已过期的")
}

func TestNewActivityCycle(t *testing.T) {
	cycleId := int32(1)
	startTime := time.Now().Unix()
	endTime := startTime + 7*24*3600 // 7天后
	
	cycle := model.NewActivityCycle(cycleId, startTime, endTime)
	
	assert.NotNil(t, cycle, "创建的周期不应为空")
	assert.Equal(t, cycleId, cycle.CycleId, "周期ID应该正确")
	assert.Equal(t, startTime, cycle.StartTime, "开始时间应该正确")
	assert.Equal(t, endTime, cycle.EndTime, "结束时间应该正确")
	assert.Equal(t, int32(1), cycle.Status, "状态应该是活跃的")
	assert.True(t, cycle.CreatedAt > 0, "创建时间应该被设置")
}
