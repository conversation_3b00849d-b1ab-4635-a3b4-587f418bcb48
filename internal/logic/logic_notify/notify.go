package logicNotify

import (
	"context"
	"errors"
	"fmt"
	daoKeepnet "spotsrv/internal/dao/dao_keepnet"
	daoPlayer "spotsrv/internal/dao/dao_player"
	modelKeepnet "spotsrv/internal/model/model_keepnet"
	rpcMsg "spotsrv/internal/repo/rpc_msg"
	rpcTrip "spotsrv/internal/repo/rpc_trip"
	rpcUser "spotsrv/internal/repo/rpc_user"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/operate"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"github.com/spf13/cast"

	spotPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/spot"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/sender"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"google.golang.org/protobuf/proto"
)

// SendRoomBroadcastNtf 房间广播消息
func SendRoomBroadcastNtf(ctx context.Context, playerId uint64, roomId string, msgID commonPB.MsgID, body proto.Message) error {
	// 广播给房间所有玩家
	playerList, err := rpcTrip.GetRoomPlayerList(ctx, roomId)

	if err != nil {
		return nil
	}

	// 如果只有玩家自己在房间 不发送
	if playerId > 0 && len(playerList) == 1 && playerList[0] == playerId {
		return nil
	}

	return sender.NotifyMultiPlayerMessage(ctx, playerList, msgID, body)
}

// SendPlayerRoomBroadcastNtf 根据玩家广播房间消息
func SendPlayerRoomBroadcastNtf(ctx context.Context, playerId uint64, msgID commonPB.MsgID, body proto.Message) error {
	// 广播给房间所有玩家
	playerList, err := rpcTrip.GetPlayerRoomAllPlayers(ctx, playerId)

	if err != nil {
		return nil
	}

	return sender.NotifyMultiPlayerMessage(ctx, playerList, msgID, body)
}

// SendPlayerEnterNtf 广播玩家进入房间信息
func SendPlayerEnterNtf(ctx context.Context, playerId uint64, roomId string, pondId int64, spotId int32) error {
	entry := logx.NewLogEntry(ctx)
	// 查询玩家鱼护数据
	keepnet, err := daoKeepnet.GetPlayerKeepnetFish(ctx, playerId)
	if err != nil {
		entry.Errorf("query player:%d keepnet info error:%v", playerId, err)
	}

	playerPondInfo, _ := daoPlayer.QuerySpotPlayerInfo(ctx, playerId)

	playerKeepnet := modelKeepnet.NewFishKeepnetFromRedisHash(ctx, keepnet)

	rickInfo, _ := rpcUser.RpcGetPlayerInfo(ctx, interceptor.GetRPCOptions(ctx).ProductId, playerId)

	playerInfo := &commonPB.FisheryPlayerInfo{
		UserInfo: &commonPB.BriefUserInfo{
			PlayerId: playerId,
		},
		SpotId:     spotId,
		FishWeight: playerKeepnet.TotalWeight,
	}

	// 全量信息
	if rickInfo != nil {
		playerInfo.UserInfo = rickInfo.GetBriefUserInfo()
	}

	// 赋值玩家基础信息
	if playerPondInfo.IsValid() {
		playerInfo.ExpLevel = playerPondInfo.ExpLevel
		playerInfo.Energy = playerPondInfo.Energy
		playerInfo.SpotId = playerPondInfo.SpotId
	}

	// 广播给其他玩家
	ntfBody := &spotPB.EnterRoomBroadcastNtf{
		PlayerInfo: playerInfo,
	}

	entry.Debugf("send player:%d enter room:%s broadcast ntf, pondId:%d, spotId:%d, body:%s", playerId, roomId, pondId, spotId, ntfBody.String())

	return SendRoomBroadcastNtf(ctx, playerId, roomId, commonPB.MsgID_CMD_ENTER_ROOM_BS_NTF, ntfBody)
}

// SendPlayerEnergyChange 玩家体力变化通知
func SendPlayerEnergyChange(ctx context.Context, playerId uint64, curEnergy int32) error {
	entry := logx.NewLogEntry(ctx)
	if playerId <= 0 || curEnergy < 0 {
		return fmt.Errorf("invalid params")
	}

	ntfBody := &spotPB.PlayerEenrgyChangeNtf{
		CurEnergy: curEnergy,
	}

	entry.Debugf("send player:%d energy change ntf, curEnergy:%d, body:%s", playerId, curEnergy, ntfBody.String())

	return sender.NotifyToPlayer(ctx, playerId, commonPB.MsgID_CMD_ENERGY_CHANGE_NTF, ntfBody)
}

// SendMsgBroadcastByFish 中鱼播报
func SendMsgBroadcastByFish(ctx context.Context, playerId uint64, eventId commonPB.MSG_BROADCAST_EVENT, pondId int64, fish *commonPB.FishInfo, weather int64) error {
	entry := logx.NewLogEntry(ctx)

	entry.Debugf("send msg broadcast pondId:%d, fish:%s", pondId, fish.String())
	eventData := modelKeepnet.ToEventMap(fish, pondId, weather)

	// 获取事件对应的模板
	eventMap := cmodel.GetAllEventTempMap()
	for key, val := range eventMap {
		if !val.Open || !(val.EventId == int64(eventId)) {
			delete(eventMap, key)
			continue
		}
		// 判断事件条件
		for _, cond := range val.Details {
			val, ok := eventData[cond.Label]
			if !ok {
				delete(eventMap, key)
				break
			}
			// 未达标
			if !operate.CheckVal(commonPB.VAL_OPERATE(cond.Operate), val, cond.Value) {
				entry.Debugf("broadcast condition check not yet, pondId:%d, fish:%s, cond:%+v",
					pondId, fish.String(), cond)
				delete(eventMap, key)
				break
			}
		}
	}

	if len(eventMap) == 0 {
		entry.Debugf("broadcast condition check not yet, pondId:%d, fish:%s", pondId, fish.String())
		return nil
	}

	// 获取玩家姓名
	opts := interceptor.GetRPCOptions(ctx)
	userRsp, err := rpcUser.RpcGetPlayerInfo(ctx, opts.ProductId, playerId)
	if err != nil {
		return err
	}
	userName := transform.Uint642Str(playerId)
	if userRsp != nil && userName != "" {
		userName = userRsp.GetBriefUserInfo().GetName()
	}

	// todo 当前只支持固定参数-第二期优化
	for _, v := range eventMap {
		params := []string{userName, cast.ToString(pondId), cast.ToString(fish.GetFishId()), cast.ToString(fish.GetWeight())}
		err := SendMsgBroadcast(ctx, v.TempId, params)
		if err != nil {
			entry.Errorf("send broadcast failed, pondId:%d, fish:%s, tempId:%d, err:%s",
				pondId, fish.String(), v.TempId, err.Error())
		}
	}

	return nil
}

// SendMsgBroadcast 广播消息
func SendMsgBroadcast(ctx context.Context, tempId int64, params []string) error {

	rpcReq := &commonPB.MsgBroadcastDetailInfo{
		BcType:     commonPB.MSG_BROADCAST_TYPE_MB_BROADCAST,
		TemplateId: tempId,
		Params:     params,
		CreateTime: timex.Now().Unix(),
	}

	resp, errCode := rpcMsg.SendBroadcastByTemp(ctx, rpcReq)
	if errCode != commonPB.ErrCode_ERR_SUCCESS {
		return errors.New(errCode.String())
	}

	if resp.GetCode() != commonPB.ErrCode_ERR_SUCCESS {
		return errors.New(resp.GetCode().String() + resp.GetDesc())
	}

	return nil
}

// SendFishingEventBsNtf 发送钓鱼事件广播
func SendFishingEventBsNtf(ctx context.Context, sender uint64, eventInfo *commonPB.FishingEventInfo) error {
	ntfMsg := spotPB.FishingEventBroadcastNtf{
		EventInfo: eventInfo,
		SenderId:  sender,
	}

	return SendPlayerRoomBroadcastNtf(ctx, sender, commonPB.MsgID_CMD_SPOT_FISHING_EVENT_BS_NTF, &ntfMsg)
}
