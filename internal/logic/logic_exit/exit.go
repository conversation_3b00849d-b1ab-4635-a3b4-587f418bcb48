package logicExit

import (
	"context"
	modelPlayer "spotsrv/internal/model/model_player"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_stats"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"

	daoKeepnet "spotsrv/internal/dao/dao_keepnet"
	logicNotify "spotsrv/internal/logic/logic_notify"
	"spotsrv/internal/pubsub/publish"

	daoPlayer "spotsrv/internal/dao/dao_player"

	"spotsrv/internal/repo/rpc_rank"
	rpcTrip "spotsrv/internal/repo/rpc_trip"

	modelKeepnet "spotsrv/internal/model/model_keepnet"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	spotPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/spot"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/item_kit"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
)

func PlayerExitRoom(ctx context.Context, playerId uint64) (settleInfo *spotPB.ExitRoomSettleInfo, err error) {
	// 通知trip服离开房间
	entry := logx.NewLogEntry(ctx)
	settleInfo = &spotPB.ExitRoomSettleInfo{}

	roomId, err := rpcTrip.PlayerExitRoom(ctx, playerId)
	if err != nil || roomId == "" {
		entry.Errorf("player:%d exit trip room:%s, error:%v", playerId, roomId, err)
		return
	}

	// 获取房间内玩家信息
	spotPlayerInfo, err := daoPlayer.QuerySpotPlayerInfo(ctx, playerId)
	if err != nil {
		entry.Errorf("player:%d exit spot room:%s, del rds error:%v", playerId, roomId, err)
		return
	}

	// 删除房间同步的玩家信息
	err = daoPlayer.DelSpotPlayerRds(ctx, playerId)
	if err != nil {
		entry.Errorf("player:%d exit spot room:%s, del rds error:%v", playerId, roomId, err)
		return
	}

	// 广播给房间玩家有人离开房间
	logicNotify.SendRoomBroadcastNtf(ctx, playerId, roomId, commonPB.MsgID_CMD_EXIT_ROOM_BS_NTF, &spotPB.ExitRoomBroadcastNtf{PlayerId: playerId})

	// 查询玩家鱼护信息
	keepnetInfoMap, err := daoKeepnet.GetPlayerKeepnetFish(ctx, playerId)
	if err != nil {
		entry.Errorf("query player:%d keepnet info error:%v", playerId, err)
		return
	}

	// 玩家离开 发送奖励
	SendPlayerKeepnetAward(ctx, playerId, keepnetInfoMap)

	// 获取结算信息,避免客户端收不到消息直接返回
	settleInfo = SendSettleInfoAfterExit(ctx, ExitInfo{
		playerId:       playerId,
		keepnetInfo:    keepnetInfoMap,
		pondPlayerInfo: spotPlayerInfo,
	})

	// 对排行榜提交鱼护数据
	rpc_rank.SubmitTripSettle(ctx, spotPlayerInfo, keepnetInfoMap)

	// 清空玩家鱼护信息
	daoKeepnet.ClearPlayerKeepnetFish(ctx, playerId)

	// 离开房间消息发布 TODO 需要钓场和钓点id需要查询后传入
	publish.PublishSpotLeaveSpot(ctx, playerId, roomId)

	return
}

// SendPlayerKeepnetAward 鱼护加入资产信息
func SendPlayerKeepnetAward(ctx context.Context, playerId uint64, keepnetInfo map[string]*modelKeepnet.FishDetailInfo) error {
	entry := logx.NewLogEntry(ctx)
	if len(keepnetInfo) <= 0 {
		entry.Debugf("add player:%d fish award not fish", playerId)
		return nil
	}

	// 合并奖励
	itemList := make([]*commonPB.ItemBase, 0)
	// 奖励加入玩家资产
	for _, fishDetail := range keepnetInfo {
		// 跳过放生标记的鱼
		if fishDetail.OptType == commonPB.FISH_ENTRY_OPT_TYPE_FEOT_RELEASE {
			continue
		}
		oneList := fishDetail.ToItemBase(ctx)
		if len(oneList) <= 0 {
			continue
		}
		itemList = append(itemList, oneList...)
	}

	if len(itemList) <= 0 {
		entry.Debugf("add player:%d fish award not item", playerId)
		return nil
	}

	// 发奖
	rewardInfo, err := item_kit.SendReward(ctx, playerId, itemList, commonPB.ITEM_SOURCE_TYPE_IST_FISH_CATCH_ADD, false)

	entry.Debugf("add player:%d fish award, fish size:%d, itemList size:%d, award:%s", playerId, len(keepnetInfo), len(itemList), rewardInfo.String())

	return err
}

// ExitInfo 玩家离开房间信息
type ExitInfo struct {
	playerId       uint64
	keepnetInfo    map[string]*modelKeepnet.FishDetailInfo
	pondPlayerInfo *modelPlayer.PondPlayer
}

// SendSettleInfoAfterExit 结算信息
func SendSettleInfoAfterExit(ctx context.Context, req ExitInfo) (settleInfo *spotPB.ExitRoomSettleInfo) {
	var totalExp int32
	// 合并奖励
	itemList := make([]*commonPB.ItemBase, 0)
	// 奖励加入玩家资产
	for _, fishDetail := range req.keepnetInfo {
		// 统计经验
		totalExp += fishDetail.StatsTotalExp(ctx)

		// 跳过放生标记的鱼
		if fishDetail.OptType == commonPB.FISH_ENTRY_OPT_TYPE_FEOT_RELEASE {
			continue
		}
		oneList := fishDetail.ToItemBase(ctx)
		if len(oneList) <= 0 {
			continue
		}
		itemList = append(itemList, oneList...)
	}

	// 获取金币记录
	newCoinRecord, _ := checkCoinRecord(ctx, req.playerId, itemList)

	// 发送结算信息
	settleInfo = &spotPB.ExitRoomSettleInfo{
		TotalExp:      totalExp,
		RewardInfo:    itemList,
		NewCoinRecord: newCoinRecord,
		EnterTime:     req.pondPlayerInfo.EnterTime,
	}
	return
}

func checkCoinRecord(ctx context.Context, playerId uint64, itemList []*commonPB.ItemBase) (bool, error) {
	var coins, coinsRecord int64

	for _, item := range itemList {
		if item.ItemId == int64(commonPB.ITEM_TYPE_IT_CURRENCY_COIN) {
			coins += item.GetItemCount()
		}
	}

	productId := interceptor.GetRPCOptions(ctx).ProductId
	stats, err := crpc_stats.RpcGetStatsList(ctx, productId, playerId)
	if err != nil {
		return false, err
	}

	for _, v := range stats {
		if v.Id == int64(commonPB.EVENT_UNIQUE_KEY_EUK_TRIP_COINS_RECORD) {
			coinsRecord = v.Val
			break
		}
	}

	// 更新 coins 记录
	if coins > coinsRecord {
		publish.PublishCoinsByTripSettle(ctx, playerId, coins)
	}

	return coins > coinsRecord, nil
}
