package logic_durability

import (
	"context"
	"fmt"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hallrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_hall"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
)

const percentMax = 10000

// 抛投产生磨损
func ThrowLossDurability(ctx context.Context, playerId uint64, ridId int32) {
	entry := logx.NewLogEntry(ctx)
	// 1. 鱼鳔在抛投时，随机扣除1%的耐久，扣除概率为30%

	// 查询钓组信息
	rodInfo, err := crpc_hall.RpcQueryPlayerRodInfo(ctx, playerId, int32(ridId))
	if err != nil {
		entry.Errorf("query rod info error, ridId:%d, err:%v", ridId, err)
		return
	}
	if rodInfo == nil {
		entry.Errorf("query rod info is nil ridId:%d", ridId)
		return
	}

	lossDurabilityMap := make(map[int32]int64)

	// 查询鱼鳔存在
	if rodItem, ok := rodInfo.Info[int32(commonPB.TRIP_ROD_SIT_TRS_BOBBER)]; ok {
		tackleCostCfg := cmodel.GetTackleDurabilityCost(consul_config.WithGrpcCtx(ctx))
		if tackleCostCfg == nil {
			entry.Errorf("query tackle cost config is nil")
			return
		}
		weight := random.Int32n(percentMax)
		// 满足扣除概率
		if weight <= tackleCostCfg.BobbersCostProb {
			// 扣除百分1%总耐久 (配置耐久)
			tackleCfg := cmodel.GetBobbers(rodItem.ItemId, consul_config.WithGrpcCtx(ctx))
			if tackleCfg == nil {
				entry.Errorf("query tackle config[%+v] is nil", rodItem.ItemId)
				return
			}
			loss := int64(tackleCfg.Durability) * int64(tackleCostCfg.BobbersCostPercent) / percentMax
			lossDurabilityMap[int32(commonPB.TRIP_ROD_SIT_TRS_BOBBER)] = int64(loss)
		}
	}
	client := crpc_hall.GetHallRpcInstance().GetHallRpcClient()
	rodLossReq := &hallRpc.LossRodDurabilityReq{
		PlayerId: playerId,
		RodId:    int32(ridId),
		Change:   lossDurabilityMap,
	}
	rsp, err := client.LossRodDurability(ctx, rodLossReq)
	if err != nil {
		entry.Errorf("lossRodDurability error, req:%v, err:%v", rodLossReq, err)
		return
	}
	if rsp.Ret.Code != 0 {
		entry.Errorf("lossRodDurability return error, req:%v, rsp:%v", rodLossReq, rsp)
		return
	}
	return
}

// 收杆产生磨损
func CatchLossDurability(ctx context.Context, playerId uint64, hookBait *commonPB.HookBait, isCatch bool) {
	// 2. 将子类型相同的饵记作同一个磨损项统一进行扣除
	// 1. 真饵，每次中鱼，扣除1个，若没有中鱼，则每次随机扣除10%的耐久，扣除概率为50%，耐久为0时，扣除当前正在使用的真饵。
	// 2. 拟饵，每次中鱼，扣除2%，若没有中鱼，则每次随机扣除5%的耐久，扣除概率为20%，耐久为0时，扣除当前正在使用的拟饵。

	entry := logx.NewLogEntry(ctx)
	if hookBait == nil {
		entry.Errorf("hookBait is nil")
		return
	}
	bitId := hookBait.GetBaitId()

	itemCfg := cmodel.GetItem(bitId, consul_config.WithGrpcCtx(ctx))
	if itemCfg == nil {
		entry.Errorf("query bait config[%+v] is nil", bitId)
		return
	}
	tackleCostCfg := cmodel.GetTackleDurabilityCost(consul_config.WithGrpcCtx(ctx))
	if tackleCostCfg == nil {
		entry.Errorf("query tackle cost config is nil")
		return
	}

	var lossVal int32 = 0

	switch commonPB.ITEM_TYPE(itemCfg.ItemType) {
	case commonPB.ITEM_TYPE_IT_TACKLE_BAIT:
		if isCatch {
			lossVal = tackleCostCfg.BaitCostPercent
		} else {

			weight := random.Int32n(percentMax)
			if weight < tackleCostCfg.BaitUncatchCostProb {
				lossVal = tackleCostCfg.BaitUncatchCostPercent
			}
		}
	case commonPB.ITEM_TYPE_IT_TACKLE_LURES:
		if isCatch {
			lossVal = tackleCostCfg.LureCostPercent
		} else {
			weight := random.Int32n(percentMax)
			if weight < tackleCostCfg.LureUncatchCostProb {
				lossVal = tackleCostCfg.LureUncatchCostPercent
			}
		}
	}
	if lossVal == 0 {
		return
	}
	client := crpc_hall.GetHallRpcInstance().GetHallRpcClient()
	source := commonPB.ITEM_SOURCE_TYPE_IST_SPOT_CATCH_FISH
	if !isCatch {
		source = commonPB.ITEM_SOURCE_TYPE_IST_SPOT_CATCH_NOT_FISH
	}
	rodLossReq := &hallRpc.LossItemHeapReq{
		PlayerId:   playerId,
		ItemId:     bitId,
		Durability: lossVal,
		SourceType: source,
	}
	rsp, err := client.LossItemHeap(ctx, rodLossReq)
	if err != nil {
		entry.Errorf("lossItemHeap error, req:%v, err:%v", rodLossReq, err)
		return
	}
	if rsp.Ret.Code != 0 {
		entry.Errorf("lossItemHeap return error, req:%v, rsp:%v", rodLossReq, rsp)
		return
	}
}

// 切线产生磨损
func KillLineLossDurability(ctx context.Context, playerId uint64, hookBait *commonPB.HookBait) error {
	entry := logx.NewLogEntry(ctx)
	if hookBait == nil {
		entry.Errorf("hookBait is nil")
		return protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "hookBait is nil")
	}
	source := commonPB.ITEM_SOURCE_TYPE_IST_SPOT_KILL_LINE
	// 尝试丢饵
	bitId := hookBait.GetBaitId()
	if bitId > 0 {
		var lossVal int32 = 10000
		baitLossReq := &hallRpc.LossItemHeapReq{
			PlayerId:   playerId,
			ItemId:     bitId,
			Durability: lossVal,
			SourceType: source,
		}
		client := crpc_hall.GetHallRpcInstance().GetHallRpcClient()
		rsp, err := client.LossItemHeap(ctx, baitLossReq)
		if err != nil {
			entry.Errorf("lossItemHeap fail, req:%v, fail:%v", baitLossReq, err)
			return err
		}
		if rsp.Ret.Code != 0 {
			entry.Infof("lossItemHeap return fail, req:%v, rsp:%v", baitLossReq, rsp)
			return protox.CodeError(commonPB.ErrCode_ERR_FAIL, fmt.Sprintf("lossItemHeap return error, req:%v, rsp:%v", baitLossReq, rsp))
		}
	}

	// 尝试丢钩
	hookId := hookBait.GetHookId()
	if hookId > 0 {
		var lossVal int32 = 10000
		hookLossReq := &hallRpc.LossItemHeapReq{
			PlayerId:   playerId,
			ItemId:     hookId,
			Durability: lossVal,
			SourceType: source,
		}
		client := crpc_hall.GetHallRpcInstance().GetHallRpcClient()
		rsp, err := client.LossItemHeap(ctx, hookLossReq)
		if err != nil {
			entry.Errorf("lossItemHeap fail, req:%v, fail:%v", hookLossReq, err)
			return err
		}
		if rsp.Ret.Code != 0 {
			entry.Infof("losshookItemHeap return fail, req:%v, rsp:%v", hookLossReq, rsp)
			return protox.CodeError(commonPB.ErrCode_ERR_FAIL, fmt.Sprintf("lossItemHeap return error, req:%v, rsp:%v", hookLossReq, rsp))
		}
	}

	return nil
}
