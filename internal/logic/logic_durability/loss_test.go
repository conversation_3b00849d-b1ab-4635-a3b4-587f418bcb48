package logic_durability

import (
	test_init "spotsrv/internal/test"
	"testing"
)

func TestLossDurability(t *testing.T) {

}

func TestItemHeap(t *testing.T) {
	test_init.Init()
	var playerId = uint64(9000079)
	ctx := test_init.NewCtxWithPlayerId(playerId)
	CatchLossDurability(ctx, playerId, 309201001, true)

}

func TestKillLineLossDurability(t *testing.T) {
	test_init.Init()
	var playerId = uint64(2)
	ctx := test_init.NewCtxWithPlayerId(playerId)
	err := KillLineLossDurability(ctx, playerId, 305109004)
	if err != nil {
		t.<PERSON><PERSON>("KillLineLossDurability error:%v", err)
	}
}
