package l_explosive_protection

import (
	"activitysrv/internal/model"
	"context"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"testing"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/testx"
	"github.com/stretchr/testify/assert"
)

func TestExplosiveProtectionLogic_HandleWeightUpdateEvent(t *testing.T) {
	// 初始化测试环境
	testx.Init()
	ctx := testx.TestCtx(1, 1)

	logic := NewExplosiveProtectionLogic()

	// 模拟入护事件
	event := &commonPB.EventCommon{
		EventType: commonPB.EVENT_TYPE_ET_FISH_GET,
		PlayerId:  12345,
		ProductId: 1,
		IntData: map[int32]int64{
			int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT): 1500, // 鱼重量1.5kg
		},
	}

	// 处理事件
	err := logic.HandleWeightUpdateEvent(ctx, event.PlayerId, event)

	// 验证结果
	assert.NoError(t, err, "处理入护事件应该成功")
}

func TestExplosiveProtectionLogic_GetActivityProgress(t *testing.T) {
	// 初始化测试环境
	testx.Init()
	ctx := testx.TestCtx(1, 1)

	logic := NewExplosiveProtectionLogic()
	playerId := uint64(12345)

	// 获取活动进度
	progress, err := logic.GetActivityProgress(ctx, playerId)

	// 验证结果
	assert.NoError(t, err, "获取活动进度应该成功")
	assert.NotNil(t, progress, "进度数据不应为空")
	assert.Equal(t, int64(model.ActivityIdExplosiveProtection), progress.ActivityId, "活动ID应该正确")
	assert.NotNil(t, progress.Metrics, "指标数据不应为空")
	assert.NotNil(t, progress.ClaimedRecords, "已领取记录不应为空")
}

func TestExplosiveProtectionLogic_ClaimReward(t *testing.T) {
	// 初始化测试环境
	testx.Init()
	ctx := testx.TestCtx(1, 1)

	logic := NewExplosiveProtectionLogic()
	playerId := uint64(12345)

	// 先模拟一些数据，让用户满足领取条件
	// 这里需要先处理一些入护事件来积累指标
	event := &commonPB.EventCommon{
		EventType: commonPB.EVENT_TYPE_ET_FISH_GET,
		PlayerId:  playerId,
		ProductId: 1,
		IntData: map[int32]int64{
			int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT): 2000, // 2kg鱼重量
		},
	}

	// 处理事件以积累指标
	err := logic.HandleWeightUpdateEvent(ctx, playerId, event)
	assert.NoError(t, err, "处理入护事件应该成功")

	// 获取当前周期
	currentCycle, err := logic.cycleDAO.CheckAndCreateCycleIfNeeded(ctx, model.ActivityIdExplosiveProtection, 7)
	assert.NoError(t, err, "获取当前周期应该成功")
	assert.NotNil(t, currentCycle, "当前周期不应为空")

	// 尝试领取第一阶段奖励
	err = logic.ClaimReward(ctx, playerId, currentCycle.CycleId, 1)

	// 验证结果
	assert.NoError(t, err, "领取奖励应该成功")
}

func TestExplosiveProtectionLogic_CheckRedDot(t *testing.T) {
	// 初始化测试环境
	testx.Init()
	ctx := testx.TestCtx(1, 1)

	logic := NewExplosiveProtectionLogic()
	playerId := uint64(12345)

	// 检查红点状态
	hasRedDot, err := logic.CheckRedDot(ctx, playerId)

	// 验证结果
	assert.NoError(t, err, "检查红点状态应该成功")
	assert.IsType(t, false, hasRedDot, "红点状态应该是布尔值")
}

func TestExplosiveProtectionLogic_ExtractMetricsFromEvent(t *testing.T) {
	logic := NewExplosiveProtectionLogic()

	// 测试入护事件
	event := &commonPB.EventCommon{
		EventType: commonPB.EVENT_TYPE_ET_FISH_GET,
		IntData: map[int32]int64{
			int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT): 1500, // 1.5kg
		},
	}

	metrics, err := logic.extractMetricsFromEvent(event, nil)

	// 验证结果
	assert.NoError(t, err, "提取指标应该成功")
	assert.Equal(t, int64(1500), metrics[model.MetricTypeFishWeight], "鱼重量指标应该正确")
	assert.Equal(t, int64(1), metrics[model.MetricTypeFishCount], "鱼数量指标应该正确")
	assert.Equal(t, int64(1500), metrics[model.MetricTypeMaxSingleWeight], "最大单次重量指标应该正确")
}

func TestExplosiveProtectionLogic_GetStageConfig(t *testing.T) {
	logic := NewExplosiveProtectionLogic()

	// 测试获取阶段配置
	stageCfg, err := logic.getStageConfig(1)

	// 验证结果
	assert.NoError(t, err, "获取阶段配置应该成功")
	assert.NotNil(t, stageCfg, "阶段配置不应为空")
	assert.Equal(t, int32(1), stageCfg.StageId, "阶段ID应该正确")
	assert.NotEmpty(t, stageCfg.Conditions, "阶段条件不应为空")
	assert.NotEmpty(t, stageCfg.Rewards, "阶段奖励不应为空")

	// 测试不存在的阶段
	_, err = logic.getStageConfig(999)
	assert.Error(t, err, "获取不存在的阶段配置应该失败")
}

func TestExplosiveProtectionLogic_CheckStageCondition(t *testing.T) {
	logic := NewExplosiveProtectionLogic()

	// 创建用户数据
	userData := model.NewUserActivityData()
	userData.UpdateMetric(model.MetricTypeFishWeight, 2000, model.MetricOperationAdd) // 2kg

	// 获取阶段配置
	stageCfg, err := logic.getStageConfig(1) // 第一阶段需要1kg
	assert.NoError(t, err, "获取阶段配置应该成功")

	// 检查条件
	canClaim := logic.checkStageCondition(userData, stageCfg)
	assert.True(t, canClaim, "应该满足领取条件")

	// 测试不满足条件的情况
	userData2 := model.NewUserActivityData()
	userData2.UpdateMetric(model.MetricTypeFishWeight, 500, model.MetricOperationAdd) // 0.5kg

	canClaim2 := logic.checkStageCondition(userData2, stageCfg)
	assert.False(t, canClaim2, "不应该满足领取条件")
}

func TestExplosiveProtectionLogic_CheckAvailableRewards(t *testing.T) {
	logic := NewExplosiveProtectionLogic()

	// 获取所有阶段配置
	stageConfigs, err := logic.getAllStageConfigs()
	assert.NoError(t, err, "获取所有阶段配置应该成功")

	// 测试有可领取奖励的情况
	metrics := map[int32]int64{
		model.MetricTypeFishWeight: 2000, // 2kg，满足第一阶段条件
	}
	claimedRecords := []int32{} // 没有领取过任何奖励

	hasReward := logic.checkAvailableRewards(metrics, claimedRecords, stageConfigs)
	assert.True(t, hasReward, "应该有可领取的奖励")

	// 测试已经领取过的情况
	claimedRecords2 := []int32{1} // 已经领取过第一阶段
	hasReward2 := logic.checkAvailableRewards(metrics, claimedRecords2, stageConfigs)
	// 这里的结果取决于是否还有其他满足条件的阶段
	assert.IsType(t, false, hasReward2, "返回值应该是布尔类型")
}

func TestExplosiveProtectionLogic_IsActivityActive(t *testing.T) {
	logic := NewExplosiveProtectionLogic()

	// 测试活跃的活动
	activeCfg := &cmodel.Activity{
		Status:    1,
		StartTime: time.Now().Unix() - 3600, // 1小时前开始
		EndTime:   time.Now().Unix() + 3600, // 1小时后结束
	}

	isActive := logic.isActivityActive(activeCfg)
	assert.True(t, isActive, "活动应该是活跃的")

	// 测试未开始的活动
	futureCfg := &cmodel.Activity{
		Status:    1,
		StartTime: time.Now().Unix() + 3600, // 1小时后开始
		EndTime:   time.Now().Unix() + 7200, // 2小时后结束
	}

	isActive2 := logic.isActivityActive(futureCfg)
	assert.False(t, isActive2, "活动应该是未开始的")

	// 测试已结束的活动
	pastCfg := &cmodel.Activity{
		Status:    1,
		StartTime: time.Now().Unix() - 7200, // 2小时前开始
		EndTime:   time.Now().Unix() - 3600, // 1小时前结束
	}

	isActive3 := logic.isActivityActive(pastCfg)
	assert.False(t, isActive3, "活动应该是已结束的")
}

func TestExplosiveProtectionLogic_HasActivityMetrics(t *testing.T) {
	logic := NewExplosiveProtectionLogic()

	// 测试包含活动指标的事件
	event1 := &commonPB.EventCommon{
		EventType: commonPB.EVENT_TYPE_ET_FISH_GET,
		IntData: map[int32]int64{
			int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT): 1500,
		},
	}

	hasMetrics1 := logic.hasActivityMetrics(event1, nil)
	assert.True(t, hasMetrics1, "应该包含活动指标")

	// 测试不包含活动指标的事件
	event2 := &commonPB.EventCommon{
		EventType: commonPB.EVENT_TYPE_ET_LOGIN,
		IntData:   map[int32]int64{},
	}

	hasMetrics2 := logic.hasActivityMetrics(event2, nil)
	assert.False(t, hasMetrics2, "不应该包含活动指标")
}
