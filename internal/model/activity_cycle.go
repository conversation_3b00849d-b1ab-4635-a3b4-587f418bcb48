package model

import (
	"encoding/json"
	"time"
)

// ActivityCycle 活动周期数据结构
type ActivityCycle struct {
	CycleId   int32 `json:"cycle_id"`   // 周期ID，从1开始递增
	StartTime int64 `json:"start_time"` // 周期开始时间戳
	EndTime   int64 `json:"end_time"`   // 周期结束时间戳
	Status    int32 `json:"status"`     // 周期状态，1表示活跃，2表示结束
	CreatedAt int64 `json:"created_at"` // 创建时间戳
}

// ToJSON 转换为JSON字符串
func (ac *ActivityCycle) ToJSON() (string, error) {
	data, err := json.Marshal(ac)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析
func (ac *ActivityCycle) FromJSON(data string) error {
	return json.Unmarshal([]byte(data), ac)
}

// NewActivityCycle 创建新的活动周期
func NewActivityCycle(cycleId int32, startTime, endTime int64) *ActivityCycle {
	return &ActivityCycle{
		CycleId:   cycleId,
		StartTime: startTime,
		EndTime:   endTime,
		Status:    1, // 活跃状态
		CreatedAt: time.Now().Unix(),
	}
}

// IsExpired 检查周期是否已过期
func (ac *ActivityCycle) IsExpired() bool {
	return time.Now().Unix() > ac.EndTime
}

// IsActive 检查周期是否活跃
func (ac *ActivityCycle) IsActive() bool {
	now := time.Now().Unix()
	return now >= ac.StartTime && now <= ac.EndTime && ac.Status == 1
}

// GetRemainingTime 获取剩余时间（秒）
func (ac *ActivityCycle) GetRemainingTime() int64 {
	remaining := ac.EndTime - time.Now().Unix()
	if remaining < 0 {
		return 0
	}
	return remaining
}
