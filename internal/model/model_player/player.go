package modelPlayer

import (
	"encoding/json"

	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/sirupsen/logrus"
)

// PondPlayer 钓点玩家信息
type PondPlayer struct {
	PondId    int64 `json:"pond_id"`    //钓场id
	SpotId    int32 `json:"spot_id"`    //钓点id
	ExpLevel  int32 `json:"exp_level"`  //经验等级
	Energy    int32 `json:"energy"`     //体力
	RigId     int32 `json:"rig_id"`     //钓组id
	EnterTime int64 `json:"enter_time"` //进入时间戳-秒
}

func (p *PondPlayer) IsValid() bool {
	if p == nil {
		return false
	}

	if p.PondId <= 0 || p.SpotId <= 0 || p.ExpLevel < 0 {
		return false
	}
	return true
}

func NewPlayerFromJson(jsonStr string) *PondPlayer {
	p := &PondPlayer{}
	err := json.Unmarshal([]byte(jsonStr), p)
	if err != nil {
		return nil
	}

	return p
}

// NewPlayerInfoFormRdsField 从redis中获取玩家信息
func NewPlayerInfoFormRdsField(rdsHash map[string]string) *PondPlayer {
	if rdsHash == nil {
		return nil
	}

	playerInfo := &PondPlayer{}

	err := transform.Map2Struct(rdsHash, playerInfo)
	if err != nil {
		logrus.Errorf("transform map:%+v to struct error:%v", rdsHash, err)
		return nil
	}

	return playerInfo
}

// BuildRedisHMSetMap 从 PondPlayer 结构体实例中构建一个适用于 Redis HMSET 的 map
func (p *PondPlayer) BuildRedisHMSetMap(fieldNames []string) map[string]interface{} {
	if p == nil || len(fieldNames) <= 0 {
		return nil
	}

	// 构建 map 用于 Redis 的 HMSET
	hashMap := make(map[string]interface{})

	// 遍历字段名称
	for _, fieldName := range fieldNames {
		switch fieldName {
		case "pond_id":
			hashMap[fieldName] = p.PondId
		case "spot_id":
			hashMap[fieldName] = p.SpotId
		case "exp_level":
			hashMap[fieldName] = p.ExpLevel
		case "energy":
			hashMap[fieldName] = p.Energy
		case "rig_id":
			hashMap[fieldName] = p.RigId
		default:
			logrus.Warnf("Field '%s' not found in PondPlayer.", fieldName)
		}
	}

	return hashMap
}

// ToRedisHashField 转化成redis hash
func (p *PondPlayer) ToRedisHashField() map[string]interface{} {
	if p == nil {
		return nil
	}

	rdsHash := make(map[string]interface{})
	err := transform.Struct2Map(p, rdsHash)

	if err != nil {
		logrus.Errorf("transform struct:%+v to map error:%v", p, err)
		return nil
	}
	return rdsHash
}

// ToJson 序列化成json
func (p *PondPlayer) ToJson() string {
	jsonByte, err := json.Marshal(p)
	if err != nil {
		return ""
	}
	return string(jsonByte)
}
