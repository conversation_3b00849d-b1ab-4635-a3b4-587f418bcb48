package model

import (
	"encoding/json"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

const (
	TableActivity = "t_activity"
)

// TActivity 活动数据表模型
type TActivity struct {
	Id           int64                     `xorm:"id pk autoincr" json:"id"`
	PlayerId     uint64                    `xorm:"player_id" json:"player_id"`
	ActivityId   int64                     `xorm:"activity_id" json:"activity_id"`
	ActivityType commonPB.ACTIVITY_TYPE    `xorm:"activity_type" json:"activity_type"`
	Status       int32                     `xorm:"status" json:"status"`
	Progress     string                    `xorm:"progress" json:"progress"`     // JSON格式的进度数据
	ExtraData    string                    `xorm:"extra_data" json:"extra_data"` // 额外数据
	CreatedAt    time.Time                 `xorm:"created_at" json:"created_at"`
	UpdatedAt    time.Time                 `xorm:"updated_at" json:"updated_at"`
}

// ToJson 转换为JSON字符串
func (t *TActivity) ToJson() string {
	data, _ := json.Marshal(t)
	return string(data)
}

// FromJson 从JSON字符串解析
func (t *TActivity) FromJson(data []byte) error {
	return json.Unmarshal(data, t)
}

// ToProto 转换为protobuf结构 (简化实现)
func (t *TActivity) ToProto() map[string]interface{} {
	return map[string]interface{}{
		"activity_id":   t.ActivityId,
		"activity_type": t.ActivityType,
		"status":        t.Status,
		"progress":      t.Progress,
		"extra_data":    t.ExtraData,
	}
}

// ActivityMetric 活动指标数据
type ActivityMetric struct {
	MetricType int32 `json:"metric_type"` // 指标类型
	Value      int64 `json:"value"`       // 指标值
	MaxValue   int64 `json:"max_value"`   // 最大值（用于最大值类型指标）
}

// ActivityRewardRecord 活动奖励领取记录
type ActivityRewardRecord struct {
	ActivityId int64 `json:"activity_id"` // 活动ID
	CycleId    int32 `json:"cycle_id"`    // 周期ID
	StageId    int32 `json:"stage_id"`    // 阶段ID
	ClaimedAt  int64 `json:"claimed_at"`  // 领取时间戳
}

// ExplosiveProtectionConstants 爆护之路活动常量
const (
	// 活动ID - 爆护之路
	ActivityIdExplosiveProtection = 1001
	
	// 指标类型
	MetricTypeFishWeight      = 1 // 鱼重量
	MetricTypeFishCount       = 2 // 鱼数量
	MetricTypeMaxSingleWeight = 3 // 单次最大重量
	
	// 活动状态
	ActivityStatusActive   = 1 // 活跃
	ActivityStatusInactive = 2 // 非活跃
	ActivityStatusExpired  = 3 // 已过期
)

// ExplosiveProtectionMetricUpdate 爆护之路指标更新请求
type ExplosiveProtectionMetricUpdate struct {
	ActivityId int64                   `json:"activity_id"` // 活动ID
	UserId     uint64                  `json:"user_id"`     // 用户ID
	Metrics    []ActivityMetric        `json:"metrics"`     // 指标列表
	Operation  MetricOperationType     `json:"operation"`   // 操作类型
}

// ExplosiveProtectionProgress 爆护之路进度数据
type ExplosiveProtectionProgress struct {
	ActivityId       int64                     `json:"activity_id"`        // 活动ID
	CurrentCycleId   int32                     `json:"current_cycle_id"`   // 当前周期ID
	CycleEndTime     int64                     `json:"cycle_end_time"`     // 当前周期结束时间戳
	Metrics          map[int32]int64           `json:"metrics"`            // 玩家指标列表
	ClaimedRecords   []int32                   `json:"claimed_records"`    // 已领取阶段列表
	PreviousCycle    *ExplosiveProtectionProgress `json:"previous_cycle,omitempty"` // 上个周期数据（可选）
}

// ToProtoActivityProgress 转换为protobuf的ActivityProgress (简化实现)
func (epp *ExplosiveProtectionProgress) ToProtoActivityProgress() map[string]interface{} {
	return map[string]interface{}{
		"activity_id":       epp.ActivityId,
		"current_cycle_id":  epp.CurrentCycleId,
		"cycle_end_time":    epp.CycleEndTime,
		"metrics":           epp.Metrics,
		"claimed_records":   epp.ClaimedRecords,
	}
}
