package config

import (
	"github.com/prometheus/client_golang/prometheus"
)

// 压测变量
var (
	// 钓鱼人数(抛竿增加 收杆减少)
	HookPlayerCount = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "hook_player_count",
			Help: "钓鱼人数",
		},
		[]string{"pondId"},
	)

	// 中鱼数据(鱼的数据)
	HookFishCount = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "hook_fish_count",
			Help:    "中鱼的数量",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"fishId"},
	)
)
