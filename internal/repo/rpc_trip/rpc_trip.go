package rpcTrip

import (
	"context"
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	tripRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/triprpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_trip"
)

// GetPlayerRoomInfo 查询玩家房间信息
func GetPlayerRoomInfo(ctx context.Context, playerId uint64) (*commonPB.RoomInfo, error) {
	entry := logx.NewLogEntry(ctx)
	// 先查询玩家信息 trip服务
	rpcRsp, err := crpc_trip.RpcGetPlayerRoomInfo(ctx, playerId)
	if err != nil {
		entry.Errorf("rpc GetPlayerRoomInfo player:%d, err:%v", playerId, err)
		return nil, err
	}

	return rpcRsp.RoomInfo, nil
}

// GetPlayerRoomId 查询玩家所在房间id
func GetPlayerRoomId(ctx context.Context, playerId uint64) (string, error) {
	// 先查询玩家信息 trip服务
	entry := logx.NewLogEntry(ctx)
	rpcRsp, err := crpc_trip.RpcGetPlayerRoomInfo(ctx, playerId)
	if err != nil {
		entry.Errorf("rpc GetPlayerRoomId player:%d, err:%v", playerId, err)
		return "", err
	}
	if rpcRsp.RoomInfo == nil {
		return "", fmt.Errorf("player:%d not in room", playerId)
	}

	return rpcRsp.RoomInfo.RoomId, nil
}

// GetPlayerRoomAllPlayers 查询玩家房间玩家列表
func GetPlayerRoomAllPlayers(ctx context.Context, playerId uint64) ([]uint64, error) {
	// 先查询玩家信息 trip服务
	entry := logx.NewLogEntry(ctx)
	rpcRsp, err := crpc_trip.RpcGetPlayerRoomInfo(ctx, playerId)
	if err != nil {
		entry.Errorf("rpc GetPlayerRoomId player:%d, err:%v", playerId, err)
		return nil, err
	}

	return GetRoomPlayerList(ctx, rpcRsp.RoomInfo.RoomId)
}

// GetRoomPlayerList 查询房间玩家列表
func GetRoomPlayerList(ctx context.Context, roomId string) ([]uint64, error) {

	entry := logx.NewLogEntry(ctx)
	tripRpcClient := crpc_trip.GetTripRpcInstance().GetTripRpcClient()

	if tripRpcClient == nil {
		entry.Errorf("rpc GetRoomPlayerList room:%s, err: trip rpc client is nil", roomId)
		return nil, fmt.Errorf("get trip rpc client failed")
	}

	reqRpc := &tripRpc.QueryRoomPlayersReq{RoomId: roomId}

	hRet, errRet := tripRpcClient.QueryRoomPlayers(ctx, reqRpc)
	if errRet != nil {
		entry.Errorf("rpc GetRoomPlayerList room:%s, err:%v", roomId, errRet)
		return nil, errRet
	}

	var playerIds []uint64

	// 遍历出玩家id
	for _, v := range hRet.Fishers {
		if v.BriefUserInfo == nil {
			continue
		}
		playerIds = append(playerIds, v.BriefUserInfo.PlayerId)
	}

	return playerIds, nil
}

// PlayerExitRoom 玩家离开房间
func PlayerExitRoom(ctx context.Context, playerId uint64) (string, error) {
	entry := logx.NewLogEntry(ctx)
	// 先查询玩家信息 trip服务
	rpcRsp, err := crpc_trip.RpcGetPlayerRoomInfo(ctx, playerId)
	if rpcRsp == nil || err != nil {
		entry.Errorf("rpc PlayerExitRoom player:%d, err : %v", playerId, err)
		return "", err
	}

	if rpcRsp.GetRoomInfo() == nil {
		entry.Errorf("rpc PlayerExitRoom player:%d, err: room info is nil", playerId)
		return "", fmt.Errorf("player:%d not in room", playerId)
	}

	tripRpcClient := crpc_trip.GetTripRpcInstance().GetTripRpcClient()

	if tripRpcClient == nil {
		entry.Errorf("rpc GetRoomPlayerList player:%d, err: trip rpc client is nil", playerId)
		return "", fmt.Errorf("get trip rpc client failed")
	}

	var fishers []*commonPB.Fisher
	fisher := &commonPB.Fisher{
		BriefUserInfo: &commonPB.BriefUserInfo{
			PlayerId: playerId,
		},
	}

	fishers = append(fishers, fisher)

	// 调用trip服务器rpc接口 离开房间
	rpcReq := &tripRpc.ExitTripReq{
		PondId:   rpcRsp.GetRoomInfo().GetPondId(),
		GameType: commonPB.GAME_TYPE(rpcRsp.RoomInfo.RoomType),
		Fishers:  fishers,
	}

	_, errRet := tripRpcClient.ExitTripRoom(ctx, rpcReq)

	return rpcRsp.RoomInfo.RoomId, errRet
}

// OfflineExitTripRoom 离线离开房间
func OfflineExitTripRoom(ctx context.Context, playerId uint64) error {
	tripRpcClient := crpc_trip.GetTripRpcInstance().GetTripRpcClient()
	entry := logx.NewLogEntry(ctx)

	if tripRpcClient == nil {
		entry.Errorf("rpc player:%d, err: trip rpc client is nil", playerId)
		return fmt.Errorf("get trip rpc client failed")
	}

	rpcReq := &tripRpc.OfflineExitReq{PlayerId: playerId}
	rsp, err := tripRpcClient.OfflineExit(ctx, rpcReq)
	if err != nil || rsp == nil {
		entry.Errorf("rpc player:%d, err:%v", playerId, err)
		return err
	}

	return nil
}
