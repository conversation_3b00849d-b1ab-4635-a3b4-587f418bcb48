package rpcUser

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_user"
)

// RpcGetPlayerInfo 查询玩家信息
func RpcGetPlayerInfo(ctx context.Context, productId int32, playerId uint64) (*commonPB.RichUserInfo, error) {
	entry := logx.NewLogEntry(ctx)
	rpcRsp, err := crpc_user.RpcGetPlayerInfo(ctx, productId, playerId)
	if err != nil {
		entry.Errorf("rpc GetPlayerInfo player:%d, err:%v", playerId, err)
		return nil, err
	}

	return rpcRsp.RichUserInfo, nil
}

// RpcGetPlayerInfoBatch 批量查询玩家信息
func RpcGetPlayerInfoBatch(ctx context.Context, productId int32, playerIds []uint64) (map[uint64]*commonPB.RichUserInfo, error) {
	entry := logx.NewLogEntry(ctx)
	rpcRsp, err := crpc_user.RpcQueryPlayerInfoMulti(ctx, productId, playerIds)
	
	if err != nil {
		entry.Errorf("rpc GetPlayerInfoBatch player:%d,err:%v", playerIds, err)
		return nil, err
	}

	return rpcRsp, nil
}