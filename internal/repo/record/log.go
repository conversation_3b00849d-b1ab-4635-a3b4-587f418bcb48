package record

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/recordx"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/sirupsen/logrus"
)

var rHookFishSender event.Sender

// Init 初始化发送者
func Init() {
	var err error
	topic := new(RHookFish).GetTableName()
	rHookFishSender, err = event.NewKafkaSender(event.WithTopic(topic))
	if err != nil {
		logrus.Fatalf("创建 Kafka 发送者失败 [%s]: %v", topic, err)
	}
}

type LogAgencyRecord struct {
}

func NewLogAgencyInstance() *LogAgencyRecord {
	return &LogAgencyRecord{}
}

// HookFishRecord 中鱼日志记录
func (l *LogAgencyRecord) HookFishRecord(record *HookFishRecordParam) {

	ctx := record.Ctx
	req := record.Req
	fishInfo := record.FishInfo

	entry := logx.NewLogEntry(ctx)

	if req == nil || fishInfo == nil {
		entry.Errorf("player:%d hook fish record req:%+v or fishInfo:%+v is nil", record.PlayerId, req.String(), fishInfo)
		return
	}

	header := recordx.NewDefaultHeaderFromCtx(ctx)

	repObj := &RHookFish{
		PlayerId:   record.PlayerId,
		PondId:     req.GetPondId(),
		FishId:     fishInfo.FishId,
		FishLength: fishInfo.Length,
		FishWeight: fishInfo.Weight,
		FishExp:    fishInfo.Exp,
	}

	body := recordx.SerializeData(header, repObj)
	rHookFishSender.Send(ctx, event.NewMessage(transform.Uint642Str(record.PlayerId), []byte(body)))
}
