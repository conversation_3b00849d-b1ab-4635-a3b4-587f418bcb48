package record

import (
	"github.com/panjf2000/ants"
	"github.com/sirupsen/logrus"
)

var DefaultLogging *LogWorkPool

const workNums = 100

type LogWorkPool struct {
	workerPoolSize     int
	workerHookFishPool *ants.PoolWithFunc

	loggAgency *LogAgencyRecord
}

func NewSpotRecordWorkPool() *LogWorkPool {
	return &LogWorkPool{
		workerPoolSize: workNums,
		loggAgency:     NewLogAgencyInstance(),
	}
}

// HookFishRecord 发布中鱼流水日志记录任务
func (l *LogWorkPool) PubHookFishRecord(record *HookFishRecordParam) {
	if err := l.workerHookFishPool.Invoke(record); err != nil {
		logrus.Errorf("log wrok job invoke login failed err : %v", err)
	}
}

// StartHookFishRecordWorkerPool 开启中鱼流水日志作业Pool
func (l *LogWorkPool) StartHookFishRecordWorkerPool() {
	l.workerHookFishPool, _ = ants.NewPoolWithFunc(l.workerPoolSize, func(i interface{}) {
		l.loggAgency.HookFishRecord(i.(*HookFishRecordParam))
	})
}