package rpcHook

import (
	"context"
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	spotPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/spot"
	hookRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hookrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_hook"
)

// PlayerThrowRod 抛竿请求 rpc到hook服
func PlayerThrowRod(ctx context.Context, playerId uint64, req *spotPB.ThrowRodReq) (*commonPB.FishSyncControl, error) {
	if playerId == 0 || req == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM)
	}

	// 请求hook服
	rpcReq := &hookRpc.ThrowRodRouteReq{
		PlayerId:  playerId,
		PondId:    req.GetPondId(),
		RigId:     req.GetRigId(),
		GridInfo:  req.GetGridInfo(),
		HookHabit: req.GetHookHabit(),
		HookBait:  req.GetHookBait(),
	}

	hookRpcCli := crpc_hook.GetHookRpcInstance().GetHookRpcClient()
	if hookRpcCli == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION)
	}

	rpcRsp, err := hookRpcCli.GetThrowRodReq(ctx, rpcReq)
	if err != nil || rpcRsp == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION)
	}

	if rpcRsp.GetRet().GetCode() != commonPB.ErrCode_ERR_SUCCESS {
		return nil, protox.CodeError(rpcRsp.GetRet().GetCode(), rpcRsp.GetRet().GetDesc())
	}

	return rpcRsp.SyncControl, nil
}

// PlayerFishHook 中鱼请求 rpc到hook服
func PlayerFishHook(ctx context.Context, playerId uint64, req *spotPB.FishHookReq) (*commonPB.FishInfo, int64, int64, error) {
	if playerId == 0 || req == nil {
		return nil, 0, 0, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM)
	}

	rpcReq := &hookRpc.FishHookRouteReq{
		PlayerId:  playerId,
		PondId:    req.GetPondId(),
		RigId:     req.GetRigId(),
		GridInfo:  req.GetGridInfo(),
		HookHabit: req.GetHookHabit(),
	}

	hookRpcCli := crpc_hook.GetHookRpcInstance().GetHookRpcClient()
	if hookRpcCli == nil {
		return nil, 0, 0, protox.CodeError(commonPB.ErrCode_ERR_OPERATION)
	}

	rpcRsp, err := hookRpcCli.GetFishHookReq(ctx, rpcReq)
	if err != nil || rpcRsp == nil {
		return nil, 0, 0, protox.CodeError(commonPB.ErrCode_ERR_OPERATION)
	}

	if rpcRsp.GetRet().GetCode() != commonPB.ErrCode_ERR_SUCCESS {
		return nil, 0, 0, protox.CodeError(rpcRsp.GetRet().GetCode(), rpcRsp.GetRet().GetDesc())
	}

	return rpcRsp.GetFishInfo(), rpcRsp.GetNextReqTime(), rpcRsp.GetFakeFishId(), nil
}

// PlayerCatchRod 收杆请求
func PlayerCatchRod(ctx context.Context, playerId uint64, req *spotPB.CatchRodReq) (*commonPB.HookBait, commonPB.FISH_RESULT, *commonPB.FishInfo) {
	entry := logx.NewLogEntry(ctx)

	if playerId == 0 || req == nil {
		entry.Errorf("param invalid playerId:%d, req:%+v", playerId, req)
		return nil, commonPB.FISH_RESULT_FR_NOTHING, nil
	}

	rpcReq := &hookRpc.CatchRodRouteReq{
		PlayerId:  playerId,
		PondId:    req.GetPondId(),
		RigId:     req.GetRigId(),
		HookHabit: req.GetHookHabit(),
	}

	hookRpcCli := crpc_hook.GetHookRpcInstance().GetHookRpcClient()
	if hookRpcCli == nil {
		return nil, commonPB.FISH_RESULT_FR_NOTHING, nil
	}

	rpcRsp, err := hookRpcCli.GetCatchRodReq(ctx, rpcReq)
	if err != nil || rpcRsp == nil {
		return rpcRsp.GetHookBait(), commonPB.FISH_RESULT_FR_NOTHING, nil
	}

	if rpcRsp.GetRet().GetCode() != commonPB.ErrCode_ERR_SUCCESS {
		return rpcRsp.GetHookBait(), commonPB.FISH_RESULT_FR_NOTHING, nil
	}

	if rpcRsp.GetFishInfo() == nil {
		return rpcRsp.GetHookBait(), commonPB.FISH_RESULT_FR_NOTHING, nil
	}

	return rpcRsp.GetHookBait(), rpcRsp.GetFishResult(), rpcRsp.GetFishInfo()
}

// PlayerBattleFish 玩家搏鱼
func PlayerBattleFish(ctx context.Context, playerId uint64, rigId int32, status commonPB.FISH_RESULT) (*hookRpc.FishBattleRouteRsp, error) {
	entry := logx.NewLogEntry(ctx)
	if playerId == 0 || rigId == 0 {
		entry.Errorf("param invalid playerId:%d, rigId:%d", playerId, rigId)
		return &hookRpc.FishBattleRouteRsp{}, fmt.Errorf("param invalid playerId:%d, rigId:%d", playerId, rigId)
	}

	rpcReq := &hookRpc.FishBattleRouteReq{
		PlayerId:   playerId,
		RigId:      rigId,
		FishResult: status,
	}
	hookRpcCli := crpc_hook.GetHookRpcInstance().GetHookRpcClient()
	if hookRpcCli == nil {
		return &hookRpc.FishBattleRouteRsp{}, fmt.Errorf("get hook rpc client failed")
	}

	rpcRsp, err := hookRpcCli.GetFishBattleReq(ctx, rpcReq)
	if err != nil || rpcRsp == nil {
		entry.Errorf("fish battle rpc failed player:%d, rigId:%d, status:%d, err:%v", playerId, rigId, status, err)
		return &hookRpc.FishBattleRouteRsp{}, fmt.Errorf("fish battle rpc failed err:%v", err)
	}

	if rpcRsp.GetRet().GetCode() != commonPB.ErrCode_ERR_SUCCESS {
		entry.Errorf("fish battle rpc failed player:%d, rigId:%d, status:%d, err:%v", playerId, rigId, status, rpcRsp.GetRet().GetDesc())
		return &hookRpc.FishBattleRouteRsp{}, fmt.Errorf("fish battle rpc failed err:%v", rpcRsp.GetRet().GetDesc())
	}

	entry.Debugf("fish battle rpc success player:%d, rigId:%d, status:%d, rsp:%v", playerId, rigId, status, rpcRsp)

	return rpcRsp, nil
}

// RpcHookStart 中鱼开始请求
func RpcHookStart(ctx context.Context, playerId uint64, req *spotPB.HookStartReq) (*commonPB.FishSyncControl, error) {
	if playerId == 0 || req == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM)
	}

	// 请求hook服
	rpcReq := &hookRpc.HookStartRouteReq{
		PlayerId:  playerId,
		PondId:    req.GetPondId(),
		RigId:     req.GetRigId(),
		HookBait:  req.GetHookBait(),
		HookHabit: req.GetHookHabit(),
		CalcType:  req.GetCalcType(),
	}

	hookRpcCli := crpc_hook.GetHookRpcInstance().GetHookRpcClient()
	if hookRpcCli == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION)
	}

	rpcRsp, err := hookRpcCli.GetHookStartReq(ctx, rpcReq)
	if err != nil || rpcRsp == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_OPERATION)
	}

	if rpcRsp.GetRet().GetCode() != commonPB.ErrCode_ERR_SUCCESS {
		return nil, protox.CodeError(rpcRsp.GetRet().GetCode(), rpcRsp.GetRet().GetDesc())
	}

	return rpcRsp.SyncControl, nil
}
