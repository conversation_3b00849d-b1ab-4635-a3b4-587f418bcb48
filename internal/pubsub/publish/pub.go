package publish

import (
	"context"
	modelKeepnet "spotsrv/internal/model/model_keepnet"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/mq"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"google.golang.org/protobuf/proto"
)

func PublishCatchRod(ctx context.Context, playerId uint64, fish *commonPB.FishInfo, pondId int64, weather int64, rog int64) {

	{
		opts := interceptor.GetRPCOptions(ctx)
		eventData := modelKeepnet.ToEventMap(fish, pondId, weather)

		// TODO
		// eventData[int32(commonPB.EVENT_KEY_EK_FISH_WEATHER)] = int64(pondId)

		data := &commonPB.EventCommon{
			PlayerId:  playerId,
			ProductId: opts.ProductId,
			ChannelId: int32(opts.ChannelType),
			EventType: commonPB.EVENT_TYPE_ET_FISH_GET,
			IntData:   eventData,
		}
		js, _ := proto.Marshal(data)
		err := mq.PublishEvent(commonPB.EVENT_TYPE_ET_FISH_GET.String(), js)
		if err != nil {
			logx.NewLogEntry(ctx).Errorf("push event [%+v] failed:%+v", data, err)
			return
		}
	}
}

// PublishSpotEnterSpot 进入钓点
func PublishSpotEnterSpot(ctx context.Context, playerId uint64, pondId int64, roomId string, spotId int32) {

	opts := interceptor.GetRPCOptions(ctx)
	intData := make(map[int32]int64)
	intData[int32(commonPB.EVENT_INT_KEY_EIK_POND_ID)] = pondId
	intData[int32(commonPB.EVENT_INT_KEY_EIK_SPOT_ID)] = int64(spotId)

	strData := make(map[int32]string)
	strData[int32(commonPB.EVENT_STR_KEY_ESK_ROOM_ID)] = roomId

	data := &commonPB.EventCommon{
		PlayerId:  playerId,
		ProductId: opts.ProductId,
		ChannelId: int32(opts.ChannelType),
		EventType: commonPB.EVENT_TYPE_ET_ENTER_SPOT,
		IntData:   intData,
		StrData:   strData,
	}
	js, _ := proto.Marshal(data)
	err := mq.PublishEvent(commonPB.EVENT_TYPE_ET_ENTER_SPOT.String(), js)
	if err != nil {
		logx.NewLogEntry(ctx).Errorf("push event [%+v] failed:%+v", data, err)
		return
	}
}

// PublishSpotLeaveSpot 离开钓点
func PublishSpotLeaveSpot(ctx context.Context, playerId uint64, roomId string) {

	opts := interceptor.GetRPCOptions(ctx)

	strData := make(map[int32]string)
	strData[int32(commonPB.EVENT_STR_KEY_ESK_ROOM_ID)] = roomId

	data := &commonPB.EventCommon{
		PlayerId:  playerId,
		ProductId: opts.ProductId,
		ChannelId: int32(opts.ChannelType),
		EventType: commonPB.EVENT_TYPE_ET_LEAVE_SPOT,
		//IntData:       intData,
		StrData: strData,
	}
	js, _ := proto.Marshal(data)
	err := mq.PublishEvent(commonPB.EVENT_TYPE_ET_LEAVE_SPOT.String(), js)
	if err != nil {
		logx.NewLogEntry(ctx).Errorf("push event [%+v] failed:%+v", data, err)
		return
	}
}

// PublishCoinsByTripSettle 旅行结算
func PublishCoinsByTripSettle(ctx context.Context, playerId uint64, coins int64) {

	opts := interceptor.GetRPCOptions(ctx)

	intData := make(map[int32]int64)
	intData[int32(commonPB.EVENT_INT_KEY_EIK_TRIP_COINS_RECORD)] = coins

	data := &commonPB.EventCommon{
		PlayerId:  playerId,
		ProductId: opts.ProductId,
		ChannelId: opts.ChannelType,
		EventType: commonPB.EVENT_TYPE_ET_TRIP_SETTLE,
		IntData:   intData,
	}
	js, _ := proto.Marshal(data)
	err := mq.PublishEvent(commonPB.EVENT_TYPE_ET_TRIP_SETTLE.String(), js)
	if err != nil {
		logx.NewLogEntry(ctx).Errorf("push event [%+v] failed:%+v", data, err)
		return
	}
}
