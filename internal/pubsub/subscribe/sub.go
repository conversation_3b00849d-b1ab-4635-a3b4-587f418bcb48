package subscribe

import (
	"activitysrv/internal/services"
	"context"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/mq"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"google.golang.org/protobuf/proto"
)

func InitSubscribe() {
	serverName := viper.GetString(dict.ConfigRpcServerName)

	// 订阅相关事件
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_LOGIN.String(), serverName, HandleEvent))
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_FISH_GET.String(), serverName, HandleEvent))
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_ROLE_LEVEL_UP.String(), serverName, HandleEvent))
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_ITEM_ADD.String(), serverName, HandleEvent))
	initPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_ITEM_REDUCE.String(), serverName, HandleEvent))
}

// 根据配置加载事件监听器
func initPanic(err error) {
	if err != nil {
		panic(err)
	}
}

func EventPlayerCtx(event *commonPB.EventCommon) context.Context {
	return interceptor.NewRpcClientCtx(
		interceptor.WithPlayerId(event.PlayerId),
		interceptor.WithProductId(event.ProductId),
		interceptor.WithChannelType(event.ChannelId),
	)
}

// HandleEvent 事件处理
func HandleEvent(body []byte) {
	event := &commonPB.EventCommon{}
	if err := proto.Unmarshal(body, event); err != nil {
		logrus.Errorf("活动服务事件解析失败: %s", err)
		return
	}

	logrus.Debugf("处理活动事件: %+v", event)
	ctx := EventPlayerCtx(event)

	if len(event.IntData) == 0 {
		logrus.Errorf("事件数据为空: %+v", event)
		return
	}

	// TODO 暂时固定productid = 1
	// GM 加道具获取不到productID
	if event.ProductId == 0 {
		event.ProductId = 1
	}

	services.GetActivityServiceInstance().EventUpdate(ctx, event.PlayerId, event)
}
