package subscribe

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/mq"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/lib/unusual"
	"github.com/spf13/viper"
)


func InitSubScribe() {
	// 尽量用统计服统合好的数据
	serverName := viper.GetString(dict.ConfigRpcServerName)

	//gate
	unusual.InitPanic(mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_LOGOUT.String(), serverName, CommonEvent))
}