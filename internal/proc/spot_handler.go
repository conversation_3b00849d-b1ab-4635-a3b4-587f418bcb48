package proc

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	spotPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/spot"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/transport"
	intranetGrpc "git.keepfancy.xyz/back-end/frameworks/kit/transport/gaterpc"
)

type SpotHandler struct {
}

// GetSpotSceneReq 钓点场景请求
func (s *SpotHandler) GetSpotSceneReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.GetSpotSceneReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().GetSpotSceneReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_SPOT_SCENE_RSP, rsp)
}

// SyncSpotInfoReq 同步钓点信息请求
func (s *SpotHandler) SyncSpotInfoReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.SyncSpotInfoReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().SyncSpotInfoReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_SYNC_SPOT_INFO_BS_NTF, rsp)
}

// ThrowRodReq 抛竿请求
func (s *SpotHandler) ThrowRodReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.ThrowRodReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().ThrowRodReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_THROW_ROD_RSP, rsp)
}

// FishHookReq 中鱼请求
func (s *SpotHandler) FishHookReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.FishHookReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().FishHookReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_FISH_HOOK_RSP, rsp)
}

// CatchRodReq 收杆请求
func (s *SpotHandler) CatchRodReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.CatchRodReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().CatchRodReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_CATCH_ROD_RSP, rsp)
}

// ExitRoomReq 离开房间请求
func (s *SpotHandler) ExitRoomReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.ExitRoomReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().ExitRoomReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_EXIT_ROOM_RSP, rsp)
}

// FishEntryOptReq 鱼入护操作请求
func (s *SpotHandler) FishEntryOptReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.FishEntryOptReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().FishEntryOptReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_FISH_ENTRY_OPT_RSP, rsp)
}

// FishKeepnetOptReq 鱼护操作请求
func (s *SpotHandler) FishKeepnetOptReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.FishKeepnetOptReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().FishKeepnetOptReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_FISH_KEEPNET_OPT_RSP, rsp)
}

// KeepnetFishInfoReq 鱼护中鱼详细信息
func (s *SpotHandler) KeepnetFishInfoReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.KeepnetFishInfoReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().KeepnetFishInfoReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_KEEPNET_FISH_INFO_RSP, rsp)
}

// GetRoomPlayerInfoReq 获取房间玩家信息请求
func (s *SpotHandler) GetRoomAllPlayerInfoReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.GetRoomAllPlayerInfoReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().GetRoomAllPlayerInfoReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_GET_ROOM_ALL_PLAYER_INFO_RSP, rsp)
}

// ChooseSpotReq 选择钓点请求
func (s *SpotHandler) ChooseSpotReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.ChooseSpotReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().ChooseSpotReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_CHOOSE_SPOT_RSP, rsp)
}

// FishBattleReq 搏鱼请求
func (s *SpotHandler) FishBattleReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.FishBattleReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().FishBattleReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_FISH_BATTLE_FISH_RSP, rsp)
}

// SwitchRodRigReq 切换竿组
func (s *SpotHandler) SwitchRodRigReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.SwitchRodRigReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().SwitchRodRigReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_SWITCH_ROD_RIG_RSP, rsp)
}

// PlayerEnergyCostReq 能量消耗
func (s *SpotHandler) PlayerEnergyCostReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.PlayerEnergyCostReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().PlayerEnergyCostReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_PLAYER_ENERGY_COST_RSP, rsp)
}

// FishEventReq 钓鱼事件请求
func (s *SpotHandler) FishEventReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.FishingEventReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().FishEventReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_SPOT_FISHING_EVENT_RSP, rsp)
}

// HookStartReq 中鱼开始请求
func (s *SpotHandler) HookStartReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.HookStartReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().HookStartReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_SPOT_HOOK_START_RSP, rsp)
}

// KillLineReq 切线请求
func (s *SpotHandler) KillLineReq(ctx context.Context, header *intranetGrpc.Header, req *spotPB.KillLineReq) *transport.ResponseMsg {
	rsp := GetSpotServiceInstance().KillLineReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_SPOT_KILL_LINE_RSP, rsp)
}
