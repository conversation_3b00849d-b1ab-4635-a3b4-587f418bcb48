@startuml charge_flow
'https://plantuml.com/sequence-diagram
title tc支付时序图

autonumber
boundary Client as "终端"
participant GameSrv as "游戏服"
participant PaySrv as "支付服"
control Cache as "Cache"
database Mysql as "数据库"
participant thirdPlatform as "SDK服务器"

== 商城阶段 ==

alt 创建订单
Client -> GameSrv : 点击购买
return 返回订单信息
note right: 客户端本地缓存订单信息
PaySrv -> Mysql : [Status:新订单]新增订单
PaySrv --> Cache : [Add]缓存
PaySrv --> GameSrv : [RPC]GAME_CMD_RPC_CREATE_ORDER_REQ

end



== 支付阶段 ==
Client [#0000FF]-> thirdPlatform : 拉起SDK支付
return : 支付结果

activate GameSrv #DarkSalmon

alt 支付状态 - 失败
Client -> GameSrv : 上报失败结果
activate GameSrv
GameSrv -> GameSrv : 更新订单[DeleteFlg:1]
GameSrv --> Cache : 缓存[删除]
deactivate GameSrv
end

alt 支付状态 - 成功
Client -> GameSrv : 上报成功结果
activate GameSrv
GameSrv -> GameSrv : 更新订单状态[Status:已支付]
GameSrv --> Cache : 缓存[更新]
deactivate GameSrv
end

== 发货阶段 ==
alt 查询订单
GameSrv [#0000FF]-> thirdPlatform : 校验支付状态
GameSrv <--[#0000FF] thirdPlatform : 返回支付结果
    alt 支付成功
    GameSrv -> Client : 发货到账
    GameSrv -> Cache : 更新玩家货币
    GameSrv --> Cache : 缓存[删除]
    end

    alt 校验失败
    GameSrv -> GameSrv : 重试（指数退避算法：1-5-20秒）
    end

end

deactivate GameSrv

== 补单阶段 ==
Client -> Client : 启动[有缓存订单]
Client -> GameSrv : 玩家登录
GameSrv [#red]->o Cache : 查询缓存
return 【有未完成订单】
alt 查询订单
GameSrv [#0000FF]-> thirdPlatform : 校验订单状态
return thirdPlatform : 返回订单状态
    alt 支付成功
    GameSrv -> Client : 发货到账
    GameSrv --> Cache : 缓存[删除]
    end

    alt 校验失败
    GameSrv -> GameSrv : 重试（指数退避算法：1-5-20秒）
    end

end

@enduml