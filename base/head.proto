syntax = "proto3";

package base;
option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/head;headPB";

// MsgEncode 协议编码
enum MsgEncode {
    ME_PLAIN    = 0x0;       // 明文编码
    ME_GZIP     = 0x1;       // GZIP压缩编码
    ME_ENCRYPT  = 0x2;       // 加密
}

message Header {
    uint32 msg_id          = 1;         // 消息ID
    uint64 send_seq        = 2;         // 发送序号
    uint64 recv_seq        = 3;         // 最后一次收到的消息序号
    uint64 stamp_time      = 4;         // 最后一次收到消息的时间戳
    uint32 body_length     = 5;         // 消息体长度
    uint64 rsp_seq         = 6;         // 回复序号
    string version         = 7;         // 发送方的版本号
    uint32 routine         = 8;         // 路由
    MsgEncode encode_type  = 9;         // 协议编码
}