package config

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/config"
)

// InitConfig 初始化配置信息
func InitConfig() error {
	serviceConfig := config.NewServiceConfig()

	serviceConfig.Register("InitActivityCfg", cmodel.InitActivityCfg)           // 初始化活动配置
	serviceConfig.Register("InitActivityConstCfg", cmodel.InitActivityConstCfg) // 初始化活动类型配置
	serviceConfig.Register("InitStageRewardsCfg", cmodel.InitStageRewardsCfg)   // 初始化活动奖励配置
	serviceConfig.Register("InitStagesCfg", cmodel.InitStagesCfg)               // 初始化活动条件配置

	return serviceConfig.ExecuteAll()
}
