// 支付模块协议
syntax = "proto3";

package PB;
option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/payment;paymentPB";

import "common.proto";
import "enum.proto";
import "errors.proto";

// 支付下单
message CreatePurchaseOrderReq {
    common.PRODUCT_ID              ProductID    = 1;       // 产品ID
    int32                          PurchaseID   = 2;       // 支付ID(对应配置文件)
    string                         CommodityID  = 3;       // 支付渠道商品ID
    common.PURCHASE_ENTRANCE_TYPE  EntranceType = 4;       // 支付下单入口类型
    common.PURCHASE_TRIGGER_TYPE   TriggerType  = 5;       // 触发支付入口原因
}

message CreatePurchaseOrderRsp {
    common.Result            ret = 1;
    common.PurchaseOrderInfo PurchaseOrderInfo = 2;        // 支付订单信息
}

// 支付成功，请求奖励发货
message DeliverPurchaseRewardReq {
    common.PRODUCT_ID ProductID         = 1;            // 产品ID
    string CommodityID                  = 2;            // 渠道商品ID
    int64  OrderID                      = 3;            // 订单ID
    int32  PurchaseID                   = 4;            // 支付ID(对应配置文件)
    common.PAY_TYPE  PayType            = 5;            // 支付渠道类型
    string Receipt                      = 6;            // 第三方返回的收据凭证
    string TransactionID                = 7;            // 第三方生成的交易ID
    string IsoCurrencyCode              = 8;            // 货币码
    int64  LocalizedPrice               = 9;            // 产品本地价格
}

message DeliverPurchaseRewardRsp {
    common.Result                   ret         = 1;
    repeated common.Reward          Reward      = 2;
}

// 支付失败，关闭订单
message ClosePurchaseOrderReq {
    common.PRODUCT_ID           ProductID    = 1;            // 产品ID
    string                      CommodityID  = 2;            // 第三方商品ID
    int64                       OrderID      = 3;            // 订单ID
    int32                       PurchaseID   = 4;            // 支付ID(对应配置文件)
    common.PURCHASE_FAILED_TYPE FailedType   = 5;            // 支付失败原因
}

message ClosePurchaseOrderRsp {
    common.Result  ret = 1;
}