// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.27.0
// source: spotrpc/spotrpc.proto

package spotRpc

import (
	context "context"
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SpotService_PondChangeEventNotify_FullMethodName = "/spotRpc.SpotService/PondChangeEventNotify"
)

// SpotServiceClient is the client API for SpotService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SpotServiceClient interface {
	// 操作道具请求
	PondChangeEventNotify(ctx context.Context, in *PondChangeEventNtf, opts ...grpc.CallOption) (*common.Result, error)
}

type spotServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSpotServiceClient(cc grpc.ClientConnInterface) SpotServiceClient {
	return &spotServiceClient{cc}
}

func (c *spotServiceClient) PondChangeEventNotify(ctx context.Context, in *PondChangeEventNtf, opts ...grpc.CallOption) (*common.Result, error) {
	out := new(common.Result)
	err := c.cc.Invoke(ctx, SpotService_PondChangeEventNotify_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SpotServiceServer is the server API for SpotService service.
// All implementations should embed UnimplementedSpotServiceServer
// for forward compatibility
type SpotServiceServer interface {
	// 操作道具请求
	PondChangeEventNotify(context.Context, *PondChangeEventNtf) (*common.Result, error)
}

// UnimplementedSpotServiceServer should be embedded to have forward compatible implementations.
type UnimplementedSpotServiceServer struct {
}

func (UnimplementedSpotServiceServer) PondChangeEventNotify(context.Context, *PondChangeEventNtf) (*common.Result, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PondChangeEventNotify not implemented")
}

// UnsafeSpotServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SpotServiceServer will
// result in compilation errors.
type UnsafeSpotServiceServer interface {
	mustEmbedUnimplementedSpotServiceServer()
}

func RegisterSpotServiceServer(s grpc.ServiceRegistrar, srv SpotServiceServer) {
	s.RegisterService(&SpotService_ServiceDesc, srv)
}

func _SpotService_PondChangeEventNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PondChangeEventNtf)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpotServiceServer).PondChangeEventNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SpotService_PondChangeEventNotify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpotServiceServer).PondChangeEventNotify(ctx, req.(*PondChangeEventNtf))
	}
	return interceptor(ctx, in, info, handler)
}

// SpotService_ServiceDesc is the grpc.ServiceDesc for SpotService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SpotService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "spotRpc.SpotService",
	HandlerType: (*SpotServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PondChangeEventNotify",
			Handler:    _SpotService_PondChangeEventNotify_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "spotrpc/spotrpc.proto",
}
