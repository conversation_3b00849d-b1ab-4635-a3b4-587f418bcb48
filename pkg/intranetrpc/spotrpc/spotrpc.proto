syntax = "proto3";

package spotRpc;
option go_package = ".;spotRpc";

import "common.proto";
import "errors.proto";

// PondChangeEventNtf 钓场事件变化通知
message PondChangeEventNtf {
    uint64                              player_id  = 1; // 玩家id
    common.RoomInfo                     room_info  = 2; // 房间信息
    repeated common.PondEventChangeInfo event_list = 3; // 事件列表
}


// RPC服务
service SpotService {
    // 操作道具请求
    rpc PondChangeEventNotify(PondChangeEventNtf) returns(common.Result);
}