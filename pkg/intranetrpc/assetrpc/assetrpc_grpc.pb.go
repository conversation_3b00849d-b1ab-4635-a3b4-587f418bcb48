// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.27.0
// source: assetrpc/assetrpc.proto

package assetRpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AssetService_OperateItem_FullMethodName   = "/assetRpc.AssetService/OperateItem"
	AssetService_QueryItemNum_FullMethodName  = "/assetRpc.AssetService/QueryItemNum"
	AssetService_QueryCategory_FullMethodName = "/assetRpc.AssetService/QueryCategory"
	AssetService_MoveItem_FullMethodName      = "/assetRpc.AssetService/MoveItem"
)

// AssetServiceClient is the client API for AssetService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AssetServiceClient interface {
	// 操作玩家Item数量
	OperateItem(ctx context.Context, in *OperateItemReq, opts ...grpc.CallOption) (*OperateItemRsp, error)
	// 获取当前Item数量
	QueryItemNum(ctx context.Context, in *QueryItemNumReq, opts ...grpc.CallOption) (*QueryItemNumRsp, error)
	// 查询当前category 类所有道具
	QueryCategory(ctx context.Context, in *QueryCategoryReq, opts ...grpc.CallOption) (*QueryCategoryRsp, error)
	// 从不同仓库转移
	MoveItem(ctx context.Context, in *MoveItemReq, opts ...grpc.CallOption) (*MoveItemRsp, error)
}

type assetServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAssetServiceClient(cc grpc.ClientConnInterface) AssetServiceClient {
	return &assetServiceClient{cc}
}

func (c *assetServiceClient) OperateItem(ctx context.Context, in *OperateItemReq, opts ...grpc.CallOption) (*OperateItemRsp, error) {
	out := new(OperateItemRsp)
	err := c.cc.Invoke(ctx, AssetService_OperateItem_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetServiceClient) QueryItemNum(ctx context.Context, in *QueryItemNumReq, opts ...grpc.CallOption) (*QueryItemNumRsp, error) {
	out := new(QueryItemNumRsp)
	err := c.cc.Invoke(ctx, AssetService_QueryItemNum_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetServiceClient) QueryCategory(ctx context.Context, in *QueryCategoryReq, opts ...grpc.CallOption) (*QueryCategoryRsp, error) {
	out := new(QueryCategoryRsp)
	err := c.cc.Invoke(ctx, AssetService_QueryCategory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetServiceClient) MoveItem(ctx context.Context, in *MoveItemReq, opts ...grpc.CallOption) (*MoveItemRsp, error) {
	out := new(MoveItemRsp)
	err := c.cc.Invoke(ctx, AssetService_MoveItem_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AssetServiceServer is the server API for AssetService service.
// All implementations should embed UnimplementedAssetServiceServer
// for forward compatibility
type AssetServiceServer interface {
	// 操作玩家Item数量
	OperateItem(context.Context, *OperateItemReq) (*OperateItemRsp, error)
	// 获取当前Item数量
	QueryItemNum(context.Context, *QueryItemNumReq) (*QueryItemNumRsp, error)
	// 查询当前category 类所有道具
	QueryCategory(context.Context, *QueryCategoryReq) (*QueryCategoryRsp, error)
	// 从不同仓库转移
	MoveItem(context.Context, *MoveItemReq) (*MoveItemRsp, error)
}

// UnimplementedAssetServiceServer should be embedded to have forward compatible implementations.
type UnimplementedAssetServiceServer struct {
}

func (UnimplementedAssetServiceServer) OperateItem(context.Context, *OperateItemReq) (*OperateItemRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OperateItem not implemented")
}
func (UnimplementedAssetServiceServer) QueryItemNum(context.Context, *QueryItemNumReq) (*QueryItemNumRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryItemNum not implemented")
}
func (UnimplementedAssetServiceServer) QueryCategory(context.Context, *QueryCategoryReq) (*QueryCategoryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryCategory not implemented")
}
func (UnimplementedAssetServiceServer) MoveItem(context.Context, *MoveItemReq) (*MoveItemRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MoveItem not implemented")
}

// UnsafeAssetServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AssetServiceServer will
// result in compilation errors.
type UnsafeAssetServiceServer interface {
	mustEmbedUnimplementedAssetServiceServer()
}

func RegisterAssetServiceServer(s grpc.ServiceRegistrar, srv AssetServiceServer) {
	s.RegisterService(&AssetService_ServiceDesc, srv)
}

func _AssetService_OperateItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OperateItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).OperateItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssetService_OperateItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).OperateItem(ctx, req.(*OperateItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetService_QueryItemNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryItemNumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).QueryItemNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssetService_QueryItemNum_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).QueryItemNum(ctx, req.(*QueryItemNumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetService_QueryCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).QueryCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssetService_QueryCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).QueryCategory(ctx, req.(*QueryCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetService_MoveItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MoveItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServiceServer).MoveItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssetService_MoveItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServiceServer).MoveItem(ctx, req.(*MoveItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AssetService_ServiceDesc is the grpc.ServiceDesc for AssetService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AssetService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "assetRpc.AssetService",
	HandlerType: (*AssetServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OperateItem",
			Handler:    _AssetService_OperateItem_Handler,
		},
		{
			MethodName: "QueryItemNum",
			Handler:    _AssetService_QueryItemNum_Handler,
		},
		{
			MethodName: "QueryCategory",
			Handler:    _AssetService_QueryCategory_Handler,
		},
		{
			MethodName: "MoveItem",
			Handler:    _AssetService_MoveItem_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "assetrpc/assetrpc.proto",
}
