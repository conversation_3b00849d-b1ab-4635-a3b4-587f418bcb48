// 天气系统模块

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.27.0
// source: worldrpc/worldrpc.proto

package worldRpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	WorldService_GetWeather_FullMethodName = "/worldRpc.WorldService/GetWeather"
)

// WorldServiceClient is the client API for WorldService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WorldServiceClient interface {
	// 查询天气
	GetWeather(ctx context.Context, in *WeatherReq, opts ...grpc.CallOption) (*WeatherRsp, error)
}

type worldServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWorldServiceClient(cc grpc.ClientConnInterface) WorldServiceClient {
	return &worldServiceClient{cc}
}

func (c *worldServiceClient) GetWeather(ctx context.Context, in *WeatherReq, opts ...grpc.CallOption) (*WeatherRsp, error) {
	out := new(WeatherRsp)
	err := c.cc.Invoke(ctx, WorldService_GetWeather_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WorldServiceServer is the server API for WorldService service.
// All implementations should embed UnimplementedWorldServiceServer
// for forward compatibility
type WorldServiceServer interface {
	// 查询天气
	GetWeather(context.Context, *WeatherReq) (*WeatherRsp, error)
}

// UnimplementedWorldServiceServer should be embedded to have forward compatible implementations.
type UnimplementedWorldServiceServer struct {
}

func (UnimplementedWorldServiceServer) GetWeather(context.Context, *WeatherReq) (*WeatherRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWeather not implemented")
}

// UnsafeWorldServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WorldServiceServer will
// result in compilation errors.
type UnsafeWorldServiceServer interface {
	mustEmbedUnimplementedWorldServiceServer()
}

func RegisterWorldServiceServer(s grpc.ServiceRegistrar, srv WorldServiceServer) {
	s.RegisterService(&WorldService_ServiceDesc, srv)
}

func _WorldService_GetWeather_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeatherReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorldServiceServer).GetWeather(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorldService_GetWeather_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorldServiceServer).GetWeather(ctx, req.(*WeatherReq))
	}
	return interceptor(ctx, in, info, handler)
}

// WorldService_ServiceDesc is the grpc.ServiceDesc for WorldService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WorldService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "worldRpc.WorldService",
	HandlerType: (*WorldServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetWeather",
			Handler:    _WorldService_GetWeather_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "worldrpc/worldrpc.proto",
}
