// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.27.0
// source: gmrpc/gmrpc.proto

package gmRpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	GmService_Cmd_FullMethodName         = "/gmRpc.GmService/Cmd"
	GmService_SetTestTime_FullMethodName = "/gmRpc.GmService/SetTestTime"
)

// GmServiceClient is the client API for GmService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GmServiceClient interface {
	// Gm 请求处理
	Cmd(ctx context.Context, in *GmCmdReq, opts ...grpc.CallOption) (*GmCmdRsp, error)
	SetTestTime(ctx context.Context, in *GmCmdReq, opts ...grpc.CallOption) (*GmCmdRsp, error)
}

type gmServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGmServiceClient(cc grpc.ClientConnInterface) GmServiceClient {
	return &gmServiceClient{cc}
}

func (c *gmServiceClient) Cmd(ctx context.Context, in *GmCmdReq, opts ...grpc.CallOption) (*GmCmdRsp, error) {
	out := new(GmCmdRsp)
	err := c.cc.Invoke(ctx, GmService_Cmd_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gmServiceClient) SetTestTime(ctx context.Context, in *GmCmdReq, opts ...grpc.CallOption) (*GmCmdRsp, error) {
	out := new(GmCmdRsp)
	err := c.cc.Invoke(ctx, GmService_SetTestTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GmServiceServer is the server API for GmService service.
// All implementations should embed UnimplementedGmServiceServer
// for forward compatibility
type GmServiceServer interface {
	// Gm 请求处理
	Cmd(context.Context, *GmCmdReq) (*GmCmdRsp, error)
	SetTestTime(context.Context, *GmCmdReq) (*GmCmdRsp, error)
}

// UnimplementedGmServiceServer should be embedded to have forward compatible implementations.
type UnimplementedGmServiceServer struct {
}

func (UnimplementedGmServiceServer) Cmd(context.Context, *GmCmdReq) (*GmCmdRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Cmd not implemented")
}
func (UnimplementedGmServiceServer) SetTestTime(context.Context, *GmCmdReq) (*GmCmdRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetTestTime not implemented")
}

// UnsafeGmServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GmServiceServer will
// result in compilation errors.
type UnsafeGmServiceServer interface {
	mustEmbedUnimplementedGmServiceServer()
}

func RegisterGmServiceServer(s grpc.ServiceRegistrar, srv GmServiceServer) {
	s.RegisterService(&GmService_ServiceDesc, srv)
}

func _GmService_Cmd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GmCmdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GmServiceServer).Cmd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GmService_Cmd_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GmServiceServer).Cmd(ctx, req.(*GmCmdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GmService_SetTestTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GmCmdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GmServiceServer).SetTestTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GmService_SetTestTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GmServiceServer).SetTestTime(ctx, req.(*GmCmdReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GmService_ServiceDesc is the grpc.ServiceDesc for GmService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GmService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "gmRpc.GmService",
	HandlerType: (*GmServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Cmd",
			Handler:    _GmService_Cmd_Handler,
		},
		{
			MethodName: "SetTestTime",
			Handler:    _GmService_SetTestTime_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gmrpc/gmrpc.proto",
}
