// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: rankrpc/rankrpc.proto

package rankRpc

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 提交钓场结算数据
type SubmitTripSettleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId   uint64                 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`      // 玩家id
	PondId     int64                  `protobuf:"varint,2,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`            // 钓场id
	ValFish    *common.FishDetailInfo `protobuf:"bytes,3,opt,name=val_fish,json=valFish,proto3" json:"val_fish,omitempty"`          // 最有价值的鱼
	WeightFish *common.FishDetailInfo `protobuf:"bytes,4,opt,name=weight_fish,json=weightFish,proto3" json:"weight_fish,omitempty"` // 最重的鱼
}

func (x *SubmitTripSettleReq) Reset() {
	*x = SubmitTripSettleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rankrpc_rankrpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitTripSettleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitTripSettleReq) ProtoMessage() {}

func (x *SubmitTripSettleReq) ProtoReflect() protoreflect.Message {
	mi := &file_rankrpc_rankrpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitTripSettleReq.ProtoReflect.Descriptor instead.
func (*SubmitTripSettleReq) Descriptor() ([]byte, []int) {
	return file_rankrpc_rankrpc_proto_rawDescGZIP(), []int{0}
}

func (x *SubmitTripSettleReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *SubmitTripSettleReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *SubmitTripSettleReq) GetValFish() *common.FishDetailInfo {
	if x != nil {
		return x.ValFish
	}
	return nil
}

func (x *SubmitTripSettleReq) GetWeightFish() *common.FishDetailInfo {
	if x != nil {
		return x.WeightFish
	}
	return nil
}

type SubmitTipSettleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 返回结果
}

func (x *SubmitTipSettleRsp) Reset() {
	*x = SubmitTipSettleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rankrpc_rankrpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitTipSettleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitTipSettleRsp) ProtoMessage() {}

func (x *SubmitTipSettleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_rankrpc_rankrpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitTipSettleRsp.ProtoReflect.Descriptor instead.
func (*SubmitTipSettleRsp) Descriptor() ([]byte, []int) {
	return file_rankrpc_rankrpc_proto_rawDescGZIP(), []int{1}
}

func (x *SubmitTipSettleRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

var File_rankrpc_rankrpc_proto protoreflect.FileDescriptor

var file_rankrpc_rankrpc_proto_rawDesc = []byte{
	0x0a, 0x15, 0x72, 0x61, 0x6e, 0x6b, 0x72, 0x70, 0x63, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x72, 0x70,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x72, 0x61, 0x6e, 0x6b, 0x52, 0x70, 0x63,
	0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb7, 0x01, 0x0a,
	0x13, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x54, 0x72, 0x69, 0x70, 0x53, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x08, 0x76, 0x61,
	0x6c, 0x5f, 0x66, 0x69, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x76, 0x61, 0x6c, 0x46, 0x69, 0x73, 0x68, 0x12, 0x37, 0x0a,
	0x0b, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x66, 0x69, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73, 0x68,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x77, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x46, 0x69, 0x73, 0x68, 0x22, 0x36, 0x0a, 0x12, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x54, 0x69, 0x70, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03,
	0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x32, 0x5c,
	0x0a, 0x0b, 0x52, 0x61, 0x6e, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4d, 0x0a,
	0x10, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x54, 0x72, 0x69, 0x70, 0x53, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x12, 0x1c, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x52, 0x70, 0x63, 0x2e, 0x53, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x54, 0x72, 0x69, 0x70, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x1b, 0x2e, 0x72, 0x61, 0x6e, 0x6b, 0x52, 0x70, 0x63, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x54, 0x69, 0x70, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x42, 0x0b, 0x5a, 0x09,
	0x2e, 0x3b, 0x72, 0x61, 0x6e, 0x6b, 0x52, 0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_rankrpc_rankrpc_proto_rawDescOnce sync.Once
	file_rankrpc_rankrpc_proto_rawDescData = file_rankrpc_rankrpc_proto_rawDesc
)

func file_rankrpc_rankrpc_proto_rawDescGZIP() []byte {
	file_rankrpc_rankrpc_proto_rawDescOnce.Do(func() {
		file_rankrpc_rankrpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_rankrpc_rankrpc_proto_rawDescData)
	})
	return file_rankrpc_rankrpc_proto_rawDescData
}

var file_rankrpc_rankrpc_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_rankrpc_rankrpc_proto_goTypes = []interface{}{
	(*SubmitTripSettleReq)(nil),   // 0: rankRpc.SubmitTripSettleReq
	(*SubmitTipSettleRsp)(nil),    // 1: rankRpc.SubmitTipSettleRsp
	(*common.FishDetailInfo)(nil), // 2: common.FishDetailInfo
	(*common.Result)(nil),         // 3: common.Result
}
var file_rankrpc_rankrpc_proto_depIdxs = []int32{
	2, // 0: rankRpc.SubmitTripSettleReq.val_fish:type_name -> common.FishDetailInfo
	2, // 1: rankRpc.SubmitTripSettleReq.weight_fish:type_name -> common.FishDetailInfo
	3, // 2: rankRpc.SubmitTipSettleRsp.ret:type_name -> common.Result
	0, // 3: rankRpc.RankService.SubmitTripSettle:input_type -> rankRpc.SubmitTripSettleReq
	1, // 4: rankRpc.RankService.SubmitTripSettle:output_type -> rankRpc.SubmitTipSettleRsp
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_rankrpc_rankrpc_proto_init() }
func file_rankrpc_rankrpc_proto_init() {
	if File_rankrpc_rankrpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_rankrpc_rankrpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitTripSettleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rankrpc_rankrpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitTipSettleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_rankrpc_rankrpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rankrpc_rankrpc_proto_goTypes,
		DependencyIndexes: file_rankrpc_rankrpc_proto_depIdxs,
		MessageInfos:      file_rankrpc_rankrpc_proto_msgTypes,
	}.Build()
	File_rankrpc_rankrpc_proto = out.File
	file_rankrpc_rankrpc_proto_rawDesc = nil
	file_rankrpc_rankrpc_proto_goTypes = nil
	file_rankrpc_rankrpc_proto_depIdxs = nil
}
