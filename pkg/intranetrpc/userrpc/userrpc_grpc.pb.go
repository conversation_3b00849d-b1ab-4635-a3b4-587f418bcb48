// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.27.0
// source: userrpc/userrpc.proto

package userRpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserService_CreatePlayer_FullMethodName           = "/userRpc.UserService/CreatePlayer"
	UserService_GetPlayerInfo_FullMethodName          = "/userRpc.UserService/GetPlayerInfo"
	UserService_GetPlayerIdByDevice_FullMethodName    = "/userRpc.UserService/GetPlayerIdByDevice"
	UserService_UpdatePlayerInfo_FullMethodName       = "/userRpc.UserService/UpdatePlayerInfo"
	UserService_GetPlayerDeviceInfo_FullMethodName    = "/userRpc.UserService/GetPlayerDeviceInfo"
	UserService_GetPlayerIdByAccount_FullMethodName   = "/userRpc.UserService/GetPlayerIdByAccount"
	UserService_DeleteAccount_FullMethodName          = "/userRpc.UserService/DeleteAccount"
	UserService_RealNameAuthQuery_FullMethodName      = "/userRpc.UserService/RealNameAuthQuery"
	UserService_RealNameAuth_FullMethodName           = "/userRpc.UserService/RealNameAuth"
	UserService_PlayerAgeQuery_FullMethodName         = "/userRpc.UserService/PlayerAgeQuery"
	UserService_PlayerMultiQuery_FullMethodName       = "/userRpc.UserService/PlayerMultiQuery"
	UserService_UpdatePlayerLogin_FullMethodName      = "/userRpc.UserService/UpdatePlayerLogin"
	UserService_BatchPlayerInfo_FullMethodName        = "/userRpc.UserService/BatchPlayerInfo"
	UserService_GetPlayerIdByOpenId_FullMethodName    = "/userRpc.UserService/GetPlayerIdByOpenId"
	UserService_QueryPlayerExtendInfo_FullMethodName  = "/userRpc.UserService/QueryPlayerExtendInfo"
	UserService_UpdatePlayerExtendInfo_FullMethodName = "/userRpc.UserService/UpdatePlayerExtendInfo"
	UserService_BatchPlayerBriefInfo_FullMethodName   = "/userRpc.UserService/BatchPlayerBriefInfo"
)

// UserServiceClient is the client API for UserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserServiceClient interface {
	// 创建账号
	CreatePlayer(ctx context.Context, in *CreatePlayerReq, opts ...grpc.CallOption) (*CreatePlayerRsp, error)
	// 查询玩家信息
	GetPlayerInfo(ctx context.Context, in *GetPlayerInfoReq, opts ...grpc.CallOption) (*GetPlayerInfoRsp, error)
	// 根据设备码查询玩家id
	GetPlayerIdByDevice(ctx context.Context, in *GetPlayerIdByDeviceReq, opts ...grpc.CallOption) (*GetPlayerIdByDeviceRsp, error)
	// 更新玩家信息(json动态参数)
	UpdatePlayerInfo(ctx context.Context, in *UpdatePlayerInfoReq, opts ...grpc.CallOption) (*UpdatePlayerInfoRsp, error)
	// 查询玩家设备信息
	GetPlayerDeviceInfo(ctx context.Context, in *GetPlayerDeviceInfoReq, opts ...grpc.CallOption) (*GetPlayerDeviceInfoRsp, error)
	// 根据账号查询玩家id
	GetPlayerIdByAccount(ctx context.Context, in *GetPlayerIdByAccountReq, opts ...grpc.CallOption) (*GetPlayerIdByAccountRsp, error)
	// 注销账号
	DeleteAccount(ctx context.Context, in *DeleteAccountReq, opts ...grpc.CallOption) (*DeleteAccountRsp, error)
	// 查询实名认证信息
	RealNameAuthQuery(ctx context.Context, in *RealNameAuthQueryReq, opts ...grpc.CallOption) (*RealNameAuthQueryRsp, error)
	// 更新实名认证信息
	RealNameAuth(ctx context.Context, in *RealNameAuthReq, opts ...grpc.CallOption) (*RealNameAuthRsp, error)
	// 查询用户年龄段
	PlayerAgeQuery(ctx context.Context, in *PlayerAgeQueryReq, opts ...grpc.CallOption) (*PlayerAgeQueryRsp, error)
	// 批量查询用户信息
	PlayerMultiQuery(ctx context.Context, in *PlayerMultiQueryReq, opts ...grpc.CallOption) (*PlayerMultiQueryRsp, error)
	// 更新用户登录信息
	UpdatePlayerLogin(ctx context.Context, in *UpdateLoginReq, opts ...grpc.CallOption) (*UpdateLoginRsp, error)
	// 批量获取用户信息
	BatchPlayerInfo(ctx context.Context, in *BatchPlayerInfoReq, opts ...grpc.CallOption) (*BatchPlayerInfoRsp, error)
	// 根据openId查询玩家id
	GetPlayerIdByOpenId(ctx context.Context, in *GetPlayerIdByOpenIdReq, opts ...grpc.CallOption) (*GetPlayerIdByOpenIdRsp, error)
	// 查询玩家拓展信息
	QueryPlayerExtendInfo(ctx context.Context, in *QueryPlayerExtendInfoReq, opts ...grpc.CallOption) (*QueryPlayerExtendInfoRsp, error)
	// 更新玩家拓展信息
	UpdatePlayerExtendInfo(ctx context.Context, in *UpdatePlayerExtendInfoReq, opts ...grpc.CallOption) (*UpdatePlayerExtendInfoRsp, error)
	// 批量查询玩家简要信息
	BatchPlayerBriefInfo(ctx context.Context, in *BatchPlayerBriefInfoReq, opts ...grpc.CallOption) (*BatchPlayerBriefInfoRsp, error)
}

type userServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserServiceClient(cc grpc.ClientConnInterface) UserServiceClient {
	return &userServiceClient{cc}
}

func (c *userServiceClient) CreatePlayer(ctx context.Context, in *CreatePlayerReq, opts ...grpc.CallOption) (*CreatePlayerRsp, error) {
	out := new(CreatePlayerRsp)
	err := c.cc.Invoke(ctx, UserService_CreatePlayer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetPlayerInfo(ctx context.Context, in *GetPlayerInfoReq, opts ...grpc.CallOption) (*GetPlayerInfoRsp, error) {
	out := new(GetPlayerInfoRsp)
	err := c.cc.Invoke(ctx, UserService_GetPlayerInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetPlayerIdByDevice(ctx context.Context, in *GetPlayerIdByDeviceReq, opts ...grpc.CallOption) (*GetPlayerIdByDeviceRsp, error) {
	out := new(GetPlayerIdByDeviceRsp)
	err := c.cc.Invoke(ctx, UserService_GetPlayerIdByDevice_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdatePlayerInfo(ctx context.Context, in *UpdatePlayerInfoReq, opts ...grpc.CallOption) (*UpdatePlayerInfoRsp, error) {
	out := new(UpdatePlayerInfoRsp)
	err := c.cc.Invoke(ctx, UserService_UpdatePlayerInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetPlayerDeviceInfo(ctx context.Context, in *GetPlayerDeviceInfoReq, opts ...grpc.CallOption) (*GetPlayerDeviceInfoRsp, error) {
	out := new(GetPlayerDeviceInfoRsp)
	err := c.cc.Invoke(ctx, UserService_GetPlayerDeviceInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetPlayerIdByAccount(ctx context.Context, in *GetPlayerIdByAccountReq, opts ...grpc.CallOption) (*GetPlayerIdByAccountRsp, error) {
	out := new(GetPlayerIdByAccountRsp)
	err := c.cc.Invoke(ctx, UserService_GetPlayerIdByAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) DeleteAccount(ctx context.Context, in *DeleteAccountReq, opts ...grpc.CallOption) (*DeleteAccountRsp, error) {
	out := new(DeleteAccountRsp)
	err := c.cc.Invoke(ctx, UserService_DeleteAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) RealNameAuthQuery(ctx context.Context, in *RealNameAuthQueryReq, opts ...grpc.CallOption) (*RealNameAuthQueryRsp, error) {
	out := new(RealNameAuthQueryRsp)
	err := c.cc.Invoke(ctx, UserService_RealNameAuthQuery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) RealNameAuth(ctx context.Context, in *RealNameAuthReq, opts ...grpc.CallOption) (*RealNameAuthRsp, error) {
	out := new(RealNameAuthRsp)
	err := c.cc.Invoke(ctx, UserService_RealNameAuth_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) PlayerAgeQuery(ctx context.Context, in *PlayerAgeQueryReq, opts ...grpc.CallOption) (*PlayerAgeQueryRsp, error) {
	out := new(PlayerAgeQueryRsp)
	err := c.cc.Invoke(ctx, UserService_PlayerAgeQuery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) PlayerMultiQuery(ctx context.Context, in *PlayerMultiQueryReq, opts ...grpc.CallOption) (*PlayerMultiQueryRsp, error) {
	out := new(PlayerMultiQueryRsp)
	err := c.cc.Invoke(ctx, UserService_PlayerMultiQuery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdatePlayerLogin(ctx context.Context, in *UpdateLoginReq, opts ...grpc.CallOption) (*UpdateLoginRsp, error) {
	out := new(UpdateLoginRsp)
	err := c.cc.Invoke(ctx, UserService_UpdatePlayerLogin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) BatchPlayerInfo(ctx context.Context, in *BatchPlayerInfoReq, opts ...grpc.CallOption) (*BatchPlayerInfoRsp, error) {
	out := new(BatchPlayerInfoRsp)
	err := c.cc.Invoke(ctx, UserService_BatchPlayerInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetPlayerIdByOpenId(ctx context.Context, in *GetPlayerIdByOpenIdReq, opts ...grpc.CallOption) (*GetPlayerIdByOpenIdRsp, error) {
	out := new(GetPlayerIdByOpenIdRsp)
	err := c.cc.Invoke(ctx, UserService_GetPlayerIdByOpenId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) QueryPlayerExtendInfo(ctx context.Context, in *QueryPlayerExtendInfoReq, opts ...grpc.CallOption) (*QueryPlayerExtendInfoRsp, error) {
	out := new(QueryPlayerExtendInfoRsp)
	err := c.cc.Invoke(ctx, UserService_QueryPlayerExtendInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdatePlayerExtendInfo(ctx context.Context, in *UpdatePlayerExtendInfoReq, opts ...grpc.CallOption) (*UpdatePlayerExtendInfoRsp, error) {
	out := new(UpdatePlayerExtendInfoRsp)
	err := c.cc.Invoke(ctx, UserService_UpdatePlayerExtendInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) BatchPlayerBriefInfo(ctx context.Context, in *BatchPlayerBriefInfoReq, opts ...grpc.CallOption) (*BatchPlayerBriefInfoRsp, error) {
	out := new(BatchPlayerBriefInfoRsp)
	err := c.cc.Invoke(ctx, UserService_BatchPlayerBriefInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServiceServer is the server API for UserService service.
// All implementations should embed UnimplementedUserServiceServer
// for forward compatibility
type UserServiceServer interface {
	// 创建账号
	CreatePlayer(context.Context, *CreatePlayerReq) (*CreatePlayerRsp, error)
	// 查询玩家信息
	GetPlayerInfo(context.Context, *GetPlayerInfoReq) (*GetPlayerInfoRsp, error)
	// 根据设备码查询玩家id
	GetPlayerIdByDevice(context.Context, *GetPlayerIdByDeviceReq) (*GetPlayerIdByDeviceRsp, error)
	// 更新玩家信息(json动态参数)
	UpdatePlayerInfo(context.Context, *UpdatePlayerInfoReq) (*UpdatePlayerInfoRsp, error)
	// 查询玩家设备信息
	GetPlayerDeviceInfo(context.Context, *GetPlayerDeviceInfoReq) (*GetPlayerDeviceInfoRsp, error)
	// 根据账号查询玩家id
	GetPlayerIdByAccount(context.Context, *GetPlayerIdByAccountReq) (*GetPlayerIdByAccountRsp, error)
	// 注销账号
	DeleteAccount(context.Context, *DeleteAccountReq) (*DeleteAccountRsp, error)
	// 查询实名认证信息
	RealNameAuthQuery(context.Context, *RealNameAuthQueryReq) (*RealNameAuthQueryRsp, error)
	// 更新实名认证信息
	RealNameAuth(context.Context, *RealNameAuthReq) (*RealNameAuthRsp, error)
	// 查询用户年龄段
	PlayerAgeQuery(context.Context, *PlayerAgeQueryReq) (*PlayerAgeQueryRsp, error)
	// 批量查询用户信息
	PlayerMultiQuery(context.Context, *PlayerMultiQueryReq) (*PlayerMultiQueryRsp, error)
	// 更新用户登录信息
	UpdatePlayerLogin(context.Context, *UpdateLoginReq) (*UpdateLoginRsp, error)
	// 批量获取用户信息
	BatchPlayerInfo(context.Context, *BatchPlayerInfoReq) (*BatchPlayerInfoRsp, error)
	// 根据openId查询玩家id
	GetPlayerIdByOpenId(context.Context, *GetPlayerIdByOpenIdReq) (*GetPlayerIdByOpenIdRsp, error)
	// 查询玩家拓展信息
	QueryPlayerExtendInfo(context.Context, *QueryPlayerExtendInfoReq) (*QueryPlayerExtendInfoRsp, error)
	// 更新玩家拓展信息
	UpdatePlayerExtendInfo(context.Context, *UpdatePlayerExtendInfoReq) (*UpdatePlayerExtendInfoRsp, error)
	// 批量查询玩家简要信息
	BatchPlayerBriefInfo(context.Context, *BatchPlayerBriefInfoReq) (*BatchPlayerBriefInfoRsp, error)
}

// UnimplementedUserServiceServer should be embedded to have forward compatible implementations.
type UnimplementedUserServiceServer struct {
}

func (UnimplementedUserServiceServer) CreatePlayer(context.Context, *CreatePlayerReq) (*CreatePlayerRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePlayer not implemented")
}
func (UnimplementedUserServiceServer) GetPlayerInfo(context.Context, *GetPlayerInfoReq) (*GetPlayerInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlayerInfo not implemented")
}
func (UnimplementedUserServiceServer) GetPlayerIdByDevice(context.Context, *GetPlayerIdByDeviceReq) (*GetPlayerIdByDeviceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlayerIdByDevice not implemented")
}
func (UnimplementedUserServiceServer) UpdatePlayerInfo(context.Context, *UpdatePlayerInfoReq) (*UpdatePlayerInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePlayerInfo not implemented")
}
func (UnimplementedUserServiceServer) GetPlayerDeviceInfo(context.Context, *GetPlayerDeviceInfoReq) (*GetPlayerDeviceInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlayerDeviceInfo not implemented")
}
func (UnimplementedUserServiceServer) GetPlayerIdByAccount(context.Context, *GetPlayerIdByAccountReq) (*GetPlayerIdByAccountRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlayerIdByAccount not implemented")
}
func (UnimplementedUserServiceServer) DeleteAccount(context.Context, *DeleteAccountReq) (*DeleteAccountRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAccount not implemented")
}
func (UnimplementedUserServiceServer) RealNameAuthQuery(context.Context, *RealNameAuthQueryReq) (*RealNameAuthQueryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RealNameAuthQuery not implemented")
}
func (UnimplementedUserServiceServer) RealNameAuth(context.Context, *RealNameAuthReq) (*RealNameAuthRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RealNameAuth not implemented")
}
func (UnimplementedUserServiceServer) PlayerAgeQuery(context.Context, *PlayerAgeQueryReq) (*PlayerAgeQueryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlayerAgeQuery not implemented")
}
func (UnimplementedUserServiceServer) PlayerMultiQuery(context.Context, *PlayerMultiQueryReq) (*PlayerMultiQueryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlayerMultiQuery not implemented")
}
func (UnimplementedUserServiceServer) UpdatePlayerLogin(context.Context, *UpdateLoginReq) (*UpdateLoginRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePlayerLogin not implemented")
}
func (UnimplementedUserServiceServer) BatchPlayerInfo(context.Context, *BatchPlayerInfoReq) (*BatchPlayerInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchPlayerInfo not implemented")
}
func (UnimplementedUserServiceServer) GetPlayerIdByOpenId(context.Context, *GetPlayerIdByOpenIdReq) (*GetPlayerIdByOpenIdRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlayerIdByOpenId not implemented")
}
func (UnimplementedUserServiceServer) QueryPlayerExtendInfo(context.Context, *QueryPlayerExtendInfoReq) (*QueryPlayerExtendInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPlayerExtendInfo not implemented")
}
func (UnimplementedUserServiceServer) UpdatePlayerExtendInfo(context.Context, *UpdatePlayerExtendInfoReq) (*UpdatePlayerExtendInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePlayerExtendInfo not implemented")
}
func (UnimplementedUserServiceServer) BatchPlayerBriefInfo(context.Context, *BatchPlayerBriefInfoReq) (*BatchPlayerBriefInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchPlayerBriefInfo not implemented")
}

// UnsafeUserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserServiceServer will
// result in compilation errors.
type UnsafeUserServiceServer interface {
	mustEmbedUnimplementedUserServiceServer()
}

func RegisterUserServiceServer(s grpc.ServiceRegistrar, srv UserServiceServer) {
	s.RegisterService(&UserService_ServiceDesc, srv)
}

func _UserService_CreatePlayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePlayerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).CreatePlayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_CreatePlayer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).CreatePlayer(ctx, req.(*CreatePlayerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetPlayerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayerInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetPlayerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetPlayerInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetPlayerInfo(ctx, req.(*GetPlayerInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetPlayerIdByDevice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayerIdByDeviceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetPlayerIdByDevice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetPlayerIdByDevice_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetPlayerIdByDevice(ctx, req.(*GetPlayerIdByDeviceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdatePlayerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePlayerInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdatePlayerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdatePlayerInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdatePlayerInfo(ctx, req.(*UpdatePlayerInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetPlayerDeviceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayerDeviceInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetPlayerDeviceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetPlayerDeviceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetPlayerDeviceInfo(ctx, req.(*GetPlayerDeviceInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetPlayerIdByAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayerIdByAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetPlayerIdByAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetPlayerIdByAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetPlayerIdByAccount(ctx, req.(*GetPlayerIdByAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_DeleteAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).DeleteAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_DeleteAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).DeleteAccount(ctx, req.(*DeleteAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_RealNameAuthQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RealNameAuthQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).RealNameAuthQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_RealNameAuthQuery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).RealNameAuthQuery(ctx, req.(*RealNameAuthQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_RealNameAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RealNameAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).RealNameAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_RealNameAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).RealNameAuth(ctx, req.(*RealNameAuthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_PlayerAgeQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlayerAgeQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).PlayerAgeQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_PlayerAgeQuery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).PlayerAgeQuery(ctx, req.(*PlayerAgeQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_PlayerMultiQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlayerMultiQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).PlayerMultiQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_PlayerMultiQuery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).PlayerMultiQuery(ctx, req.(*PlayerMultiQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdatePlayerLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdatePlayerLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdatePlayerLogin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdatePlayerLogin(ctx, req.(*UpdateLoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_BatchPlayerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchPlayerInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).BatchPlayerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_BatchPlayerInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).BatchPlayerInfo(ctx, req.(*BatchPlayerInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetPlayerIdByOpenId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayerIdByOpenIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetPlayerIdByOpenId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetPlayerIdByOpenId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetPlayerIdByOpenId(ctx, req.(*GetPlayerIdByOpenIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_QueryPlayerExtendInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPlayerExtendInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).QueryPlayerExtendInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_QueryPlayerExtendInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).QueryPlayerExtendInfo(ctx, req.(*QueryPlayerExtendInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdatePlayerExtendInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePlayerExtendInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdatePlayerExtendInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdatePlayerExtendInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdatePlayerExtendInfo(ctx, req.(*UpdatePlayerExtendInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_BatchPlayerBriefInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchPlayerBriefInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).BatchPlayerBriefInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_BatchPlayerBriefInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).BatchPlayerBriefInfo(ctx, req.(*BatchPlayerBriefInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

// UserService_ServiceDesc is the grpc.ServiceDesc for UserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "userRpc.UserService",
	HandlerType: (*UserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePlayer",
			Handler:    _UserService_CreatePlayer_Handler,
		},
		{
			MethodName: "GetPlayerInfo",
			Handler:    _UserService_GetPlayerInfo_Handler,
		},
		{
			MethodName: "GetPlayerIdByDevice",
			Handler:    _UserService_GetPlayerIdByDevice_Handler,
		},
		{
			MethodName: "UpdatePlayerInfo",
			Handler:    _UserService_UpdatePlayerInfo_Handler,
		},
		{
			MethodName: "GetPlayerDeviceInfo",
			Handler:    _UserService_GetPlayerDeviceInfo_Handler,
		},
		{
			MethodName: "GetPlayerIdByAccount",
			Handler:    _UserService_GetPlayerIdByAccount_Handler,
		},
		{
			MethodName: "DeleteAccount",
			Handler:    _UserService_DeleteAccount_Handler,
		},
		{
			MethodName: "RealNameAuthQuery",
			Handler:    _UserService_RealNameAuthQuery_Handler,
		},
		{
			MethodName: "RealNameAuth",
			Handler:    _UserService_RealNameAuth_Handler,
		},
		{
			MethodName: "PlayerAgeQuery",
			Handler:    _UserService_PlayerAgeQuery_Handler,
		},
		{
			MethodName: "PlayerMultiQuery",
			Handler:    _UserService_PlayerMultiQuery_Handler,
		},
		{
			MethodName: "UpdatePlayerLogin",
			Handler:    _UserService_UpdatePlayerLogin_Handler,
		},
		{
			MethodName: "BatchPlayerInfo",
			Handler:    _UserService_BatchPlayerInfo_Handler,
		},
		{
			MethodName: "GetPlayerIdByOpenId",
			Handler:    _UserService_GetPlayerIdByOpenId_Handler,
		},
		{
			MethodName: "QueryPlayerExtendInfo",
			Handler:    _UserService_QueryPlayerExtendInfo_Handler,
		},
		{
			MethodName: "UpdatePlayerExtendInfo",
			Handler:    _UserService_UpdatePlayerExtendInfo_Handler,
		},
		{
			MethodName: "BatchPlayerBriefInfo",
			Handler:    _UserService_BatchPlayerBriefInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "userrpc/userrpc.proto",
}
