syntax = "proto3";

package userRpc;
option go_package = ".;userRpc";

import "common.proto";
import "enum.proto";
import "errors.proto";
import "internal.proto";

// 创建玩家请求
message CreatePlayerReq {
    string                client_version = 1;  // 客户端版本号
    int32                 product_id     = 2;  // 产品 ID
    common.CHANNEL_TYPE   channel_id     = 3;  // 渠道 ID
    common.DeviceInfo     device_info    = 4;  // 设备信息
    string                third_token    = 5;  // 三方Token，根据具体SDK
    // string                adjust_id         = 6;  // adjust id (废弃， device_info 中包含)
    common.NETWORK_TYPE   network        = 7;  // 网络类型
    string                bundle_name    = 8;  // 包名
    common.PLATFORM_TYPE  platform       = 9;  // 平台
    common.AccountInfo    account_info   = 10; // 账号信息
    common.LOGIN_TYPE     create_type    = 11; // 创建方式
    common.ThirdLoginInfo third_info     = 12; // 三方信息
    common.RegionInfo region             = 13; // 地区信息
}

// 创建玩家返回
message CreatePlayerRsp {
    uint64 player_id                     = 1;  //玩家id
    common.RichUserInfo   rich_user_info = 2;  //全量用户信息
}

// 获取玩家信息请求
message GetPlayerInfoReq {
    int32  product_id = 1; //产品id
    uint64 player_id  = 2; //玩家id
}

// 获取玩家信息返回
message GetPlayerInfoRsp {
    uint64                player_id      = 1; //玩家id
    common.RichUserInfo   rich_user_info = 2; //全量用户信息
}

// 根据deviceId获取玩家id请求
message GetPlayerIdByDeviceReq {
    int32  product_id  = 1; //产品id
    string device_code = 2; //设备码
}

// 根据deviceId获取玩家id返回
message GetPlayerIdByDeviceRsp {
    uint64 player_id = 1; //玩家id
}

// 更新玩家信息请求
message UpdatePlayerInfoReq {
    int32 product_id   = 1; //产品id
    uint64 player_id   = 2; //玩家id
    // string update_json = 2; //更新参数json
    map<string, string> update_map = 3;
}

// 更新玩家信息返回
message UpdatePlayerInfoRsp {
    common.Result    ret         = 1; //更新结果
    uint64           player_id   = 2; //玩家id
    map<string, string> update_map = 3;
}

// 查询玩家deviceInfo
message GetPlayerDeviceInfoReq {
    int32  product_id   = 1; //产品id
    uint64 player_id    = 2; //玩家id
}

// 查询玩家deviceInfo返回
message GetPlayerDeviceInfoRsp {
    uint64             player_id  = 1; //玩家id
    common.DeviceInfo device_info = 2; //玩家设备信息
}

// 根据账号查询玩家id
message GetPlayerIdByAccountReq {
    int32              product_id   = 1; //产品id
    common.AccountInfo account_info = 2; //账号信息
}

// 根据账号查询玩家id返回
message GetPlayerIdByAccountRsp {
    uint64 player_id = 1; //玩家id
}

// 注销账号
message DeleteAccountReq {
    int32  product_id               = 1; //产品id
    uint64 player_id                = 2; //玩家id
}

// 注销账号
message DeleteAccountRsp {
    common.Result ret = 1; //删除结果
}

// 查询实名认证信息请求
message RealNameAuthQueryReq {
    int32  product_id = 1; //产品id
    uint64 player_id  = 2; //玩家id
}

// 查询实名认证信息返回
message RealNameAuthQueryRsp {
    common.Result ret          = 1;  //实名认证结果
    bool          is_real_name = 2;  //是否实名认证
}

// 更新实名认证信息请求
message RealNameAuthReq {
    common.PlayerRealNameAuth auth_info = 1; //实名认证信息
}

// 更新实名认证信息返回
message RealNameAuthRsp {
    common.Result ret = 1; //实名认证结果
}

// 用户年龄短查询
message PlayerAgeQueryReq {
    int32  product_id = 1; //产品id
    uint64 player_id  = 2; //玩家id
}

// 用户年龄段查询
message PlayerAgeQueryRsp {
    common.Result ret = 1; //
    common.USER_AGE age = 2; // 年龄段定义
}

// 批量多个玩家查询
message PlayerMultiQueryReq {
    int32  product_id = 1;
    repeated uint64 player_id  = 2; //玩家id
}

// 批量多个玩家查询
message PlayerMultiQueryRsp {
    common.Result ret = 1; //
    map<uint64, common.RichUserInfo> player_info = 2; // 玩家信息
}

// 更新玩家登录请求
message UpdateLoginReq {
    int32 product_id   = 1; //产品id
    uint64 player_id   = 2; //玩家id
    common.DeviceInfo     device_info       = 3;  // 设备信息
}

// 更新玩家登录返回
message UpdateLoginRsp {
    common.Result    ret         = 1; //更新结果
}

// 批量查询玩家用户信息请求
message BatchPlayerInfoReq {
    int32                      product_id   = 1; //产品id
    uint64                     player_id    = 2 ; //玩家id
    common.PaginationReq       pagination   = 3;
}

// 批量查询玩家用户信息返回
message BatchPlayerInfoRsp {
  common.Result                ret         = 1;
  repeated common.RichUserInfo player_info = 2;
  int32                        count       = 3;
}

// 根据openId查询玩家id请求
message GetPlayerIdByOpenIdReq {
    int32             product_id = 1;  //产品id
    common.LOGIN_TYPE login_type = 2;  //登录类型
    string            open_id    = 3;  //open id
}

// 根据openId查询玩家id返回
message GetPlayerIdByOpenIdRsp {
    uint64 player_id = 1; //玩家id
}

// 查询玩家拓展信息请求
message QueryPlayerExtendInfoReq {
    int32 product_id = 1; //产品id
    uint64 player_id = 2; //玩家id
}

// 查询玩家拓展信息返回
message QueryPlayerExtendInfoRsp {
    common.Result         ret         = 1;  //查询结果
    common.ExtendUserInfo extend_info = 2;  //玩家拓展信息
}

// 更新玩家拓展信息
message UpdatePlayerExtendInfoReq {
    int32                 product_id  = 1;  //产品id
    uint64                player_id   = 2;  //玩家id
    common.ExtendUserInfo extend_info = 3;  //玩家拓展信息
}

// 更新玩家拓展信息返回
message UpdatePlayerExtendInfoRsp {
    common.Result ret = 1; //更新结果
}

// 批量查询玩家简要信息请求
message BatchPlayerBriefInfoReq {
    int32    product_id         = 1;   //产品id
    repeated uint64 player_list = 2 ;  //玩家id列表
}

// 批量查询玩家简要信息返回
message BatchPlayerBriefInfoRsp {
    common.Result ret                         = 1;  //查询结果
    repeated common.BriefUserInfo player_info = 2;  //玩家信息列表
}

// RPC服务
service UserService {
    // 创建账号
    rpc CreatePlayer(CreatePlayerReq) returns (CreatePlayerRsp) {}
    // 查询玩家信息
    rpc GetPlayerInfo(GetPlayerInfoReq) returns (GetPlayerInfoRsp) {}
    // 根据设备码查询玩家id
    rpc GetPlayerIdByDevice(GetPlayerIdByDeviceReq) returns (GetPlayerIdByDeviceRsp) {}
    // 更新玩家信息(json动态参数)
    rpc UpdatePlayerInfo(UpdatePlayerInfoReq) returns (UpdatePlayerInfoRsp) {}
    // 查询玩家设备信息
    rpc GetPlayerDeviceInfo(GetPlayerDeviceInfoReq) returns (GetPlayerDeviceInfoRsp) {}
    // 根据账号查询玩家id
    rpc GetPlayerIdByAccount(GetPlayerIdByAccountReq) returns (GetPlayerIdByAccountRsp) {}
    // 注销账号
    rpc DeleteAccount(DeleteAccountReq) returns (DeleteAccountRsp) {}
    // 查询实名认证信息
    rpc RealNameAuthQuery(RealNameAuthQueryReq) returns (RealNameAuthQueryRsp) {}
    // 更新实名认证信息
    rpc RealNameAuth(RealNameAuthReq) returns (RealNameAuthRsp) {}
    // 查询用户年龄段
    rpc PlayerAgeQuery(PlayerAgeQueryReq) returns (PlayerAgeQueryRsp) {}
    // 批量查询用户信息
    rpc PlayerMultiQuery(PlayerMultiQueryReq) returns (PlayerMultiQueryRsp) {}
    // 更新用户登录信息
    rpc UpdatePlayerLogin(UpdateLoginReq) returns (UpdateLoginRsp) {}

    // 批量获取用户信息
    rpc BatchPlayerInfo(BatchPlayerInfoReq) returns (BatchPlayerInfoRsp) {}

    // 根据openId查询玩家id
    rpc GetPlayerIdByOpenId(GetPlayerIdByOpenIdReq) returns (GetPlayerIdByOpenIdRsp) {}

    // 查询玩家拓展信息
    rpc QueryPlayerExtendInfo(QueryPlayerExtendInfoReq) returns (QueryPlayerExtendInfoRsp) {}

    // 更新玩家拓展信息
    rpc UpdatePlayerExtendInfo(UpdatePlayerExtendInfoReq) returns (UpdatePlayerExtendInfoRsp) {}

    // 批量查询玩家简要信息
    rpc BatchPlayerBriefInfo(BatchPlayerBriefInfoReq) returns (BatchPlayerBriefInfoRsp) {}
}
