// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.27.0
// source: triprpc/triprpc.proto

package tripRpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	TripService_JoinTripRoom_FullMethodName     = "/userRpc.TripService/JoinTripRoom"
	TripService_ExitTripRoom_FullMethodName     = "/userRpc.TripService/ExitTripRoom"
	TripService_QueryTrip_FullMethodName        = "/userRpc.TripService/QueryTrip"
	TripService_QueryRoomPlayers_FullMethodName = "/userRpc.TripService/QueryRoomPlayers"
	TripService_OfflineExit_FullMethodName      = "/userRpc.TripService/OfflineExit"
)

// TripServiceClient is the client API for TripService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TripServiceClient interface {
	// 开始Trip，加入房间
	JoinTripRoom(ctx context.Context, in *JoinTripReq, opts ...grpc.CallOption) (*JoinTripRsp, error)
	// 退出Trip，退出房间
	ExitTripRoom(ctx context.Context, in *ExitTripReq, opts ...grpc.CallOption) (*ExitTripRsp, error)
	// 查询玩家所在房间
	QueryTrip(ctx context.Context, in *QueryTripReq, opts ...grpc.CallOption) (*QueryTripRsp, error)
	// QueryRoomPlayers
	QueryRoomPlayers(ctx context.Context, in *QueryRoomPlayersReq, opts ...grpc.CallOption) (*QueryRoomPlayersRsp, error)
	// 玩家断线离开
	OfflineExit(ctx context.Context, in *OfflineExitReq, opts ...grpc.CallOption) (*OfflineExitRsp, error)
}

type tripServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTripServiceClient(cc grpc.ClientConnInterface) TripServiceClient {
	return &tripServiceClient{cc}
}

func (c *tripServiceClient) JoinTripRoom(ctx context.Context, in *JoinTripReq, opts ...grpc.CallOption) (*JoinTripRsp, error) {
	out := new(JoinTripRsp)
	err := c.cc.Invoke(ctx, TripService_JoinTripRoom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tripServiceClient) ExitTripRoom(ctx context.Context, in *ExitTripReq, opts ...grpc.CallOption) (*ExitTripRsp, error) {
	out := new(ExitTripRsp)
	err := c.cc.Invoke(ctx, TripService_ExitTripRoom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tripServiceClient) QueryTrip(ctx context.Context, in *QueryTripReq, opts ...grpc.CallOption) (*QueryTripRsp, error) {
	out := new(QueryTripRsp)
	err := c.cc.Invoke(ctx, TripService_QueryTrip_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tripServiceClient) QueryRoomPlayers(ctx context.Context, in *QueryRoomPlayersReq, opts ...grpc.CallOption) (*QueryRoomPlayersRsp, error) {
	out := new(QueryRoomPlayersRsp)
	err := c.cc.Invoke(ctx, TripService_QueryRoomPlayers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tripServiceClient) OfflineExit(ctx context.Context, in *OfflineExitReq, opts ...grpc.CallOption) (*OfflineExitRsp, error) {
	out := new(OfflineExitRsp)
	err := c.cc.Invoke(ctx, TripService_OfflineExit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TripServiceServer is the server API for TripService service.
// All implementations should embed UnimplementedTripServiceServer
// for forward compatibility
type TripServiceServer interface {
	// 开始Trip，加入房间
	JoinTripRoom(context.Context, *JoinTripReq) (*JoinTripRsp, error)
	// 退出Trip，退出房间
	ExitTripRoom(context.Context, *ExitTripReq) (*ExitTripRsp, error)
	// 查询玩家所在房间
	QueryTrip(context.Context, *QueryTripReq) (*QueryTripRsp, error)
	// QueryRoomPlayers
	QueryRoomPlayers(context.Context, *QueryRoomPlayersReq) (*QueryRoomPlayersRsp, error)
	// 玩家断线离开
	OfflineExit(context.Context, *OfflineExitReq) (*OfflineExitRsp, error)
}

// UnimplementedTripServiceServer should be embedded to have forward compatible implementations.
type UnimplementedTripServiceServer struct {
}

func (UnimplementedTripServiceServer) JoinTripRoom(context.Context, *JoinTripReq) (*JoinTripRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JoinTripRoom not implemented")
}
func (UnimplementedTripServiceServer) ExitTripRoom(context.Context, *ExitTripReq) (*ExitTripRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExitTripRoom not implemented")
}
func (UnimplementedTripServiceServer) QueryTrip(context.Context, *QueryTripReq) (*QueryTripRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryTrip not implemented")
}
func (UnimplementedTripServiceServer) QueryRoomPlayers(context.Context, *QueryRoomPlayersReq) (*QueryRoomPlayersRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRoomPlayers not implemented")
}
func (UnimplementedTripServiceServer) OfflineExit(context.Context, *OfflineExitReq) (*OfflineExitRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OfflineExit not implemented")
}

// UnsafeTripServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TripServiceServer will
// result in compilation errors.
type UnsafeTripServiceServer interface {
	mustEmbedUnimplementedTripServiceServer()
}

func RegisterTripServiceServer(s grpc.ServiceRegistrar, srv TripServiceServer) {
	s.RegisterService(&TripService_ServiceDesc, srv)
}

func _TripService_JoinTripRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinTripReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TripServiceServer).JoinTripRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TripService_JoinTripRoom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TripServiceServer).JoinTripRoom(ctx, req.(*JoinTripReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TripService_ExitTripRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExitTripReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TripServiceServer).ExitTripRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TripService_ExitTripRoom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TripServiceServer).ExitTripRoom(ctx, req.(*ExitTripReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TripService_QueryTrip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryTripReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TripServiceServer).QueryTrip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TripService_QueryTrip_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TripServiceServer).QueryTrip(ctx, req.(*QueryTripReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TripService_QueryRoomPlayers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRoomPlayersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TripServiceServer).QueryRoomPlayers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TripService_QueryRoomPlayers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TripServiceServer).QueryRoomPlayers(ctx, req.(*QueryRoomPlayersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TripService_OfflineExit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OfflineExitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TripServiceServer).OfflineExit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TripService_OfflineExit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TripServiceServer).OfflineExit(ctx, req.(*OfflineExitReq))
	}
	return interceptor(ctx, in, info, handler)
}

// TripService_ServiceDesc is the grpc.ServiceDesc for TripService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TripService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "userRpc.TripService",
	HandlerType: (*TripServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "JoinTripRoom",
			Handler:    _TripService_JoinTripRoom_Handler,
		},
		{
			MethodName: "ExitTripRoom",
			Handler:    _TripService_ExitTripRoom_Handler,
		},
		{
			MethodName: "QueryTrip",
			Handler:    _TripService_QueryTrip_Handler,
		},
		{
			MethodName: "QueryRoomPlayers",
			Handler:    _TripService_QueryRoomPlayers_Handler,
		},
		{
			MethodName: "OfflineExit",
			Handler:    _TripService_OfflineExit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "triprpc/triprpc.proto",
}
