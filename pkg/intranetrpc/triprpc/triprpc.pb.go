// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: triprpc/triprpc.proto

package tripRpc

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Join Trip
type JoinTripReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32            `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`                    // 产品 ID
	PondId    int64            `protobuf:"varint,2,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`                             // mapID
	SpotId    int32            `protobuf:"varint,3,opt,name=spot_id,json=spotId,proto3" json:"spot_id,omitempty"`                             // spotID
	GameType  common.GAME_TYPE `protobuf:"varint,4,opt,name=game_type,json=gameType,proto3,enum=common.GAME_TYPE" json:"game_type,omitempty"` // game type
	Fishers   []*common.Fisher `protobuf:"bytes,5,rep,name=fishers,proto3" json:"fishers,omitempty"`                                          // 钓鱼玩家
}

func (x *JoinTripReq) Reset() {
	*x = JoinTripReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_triprpc_triprpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JoinTripReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinTripReq) ProtoMessage() {}

func (x *JoinTripReq) ProtoReflect() protoreflect.Message {
	mi := &file_triprpc_triprpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinTripReq.ProtoReflect.Descriptor instead.
func (*JoinTripReq) Descriptor() ([]byte, []int) {
	return file_triprpc_triprpc_proto_rawDescGZIP(), []int{0}
}

func (x *JoinTripReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *JoinTripReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *JoinTripReq) GetSpotId() int32 {
	if x != nil {
		return x.SpotId
	}
	return 0
}

func (x *JoinTripReq) GetGameType() common.GAME_TYPE {
	if x != nil {
		return x.GameType
	}
	return common.GAME_TYPE(0)
}

func (x *JoinTripReq) GetFishers() []*common.Fisher {
	if x != nil {
		return x.Fishers
	}
	return nil
}

type JoinRoomRet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid      uint64           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`                          // Uid
	RoomInfo *common.RoomInfo `protobuf:"bytes,2,opt,name=room_info,json=roomInfo,proto3" json:"room_info,omitempty"` // 房间信息
}

func (x *JoinRoomRet) Reset() {
	*x = JoinRoomRet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_triprpc_triprpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JoinRoomRet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinRoomRet) ProtoMessage() {}

func (x *JoinRoomRet) ProtoReflect() protoreflect.Message {
	mi := &file_triprpc_triprpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinRoomRet.ProtoReflect.Descriptor instead.
func (*JoinRoomRet) Descriptor() ([]byte, []int) {
	return file_triprpc_triprpc_proto_rawDescGZIP(), []int{1}
}

func (x *JoinRoomRet) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *JoinRoomRet) GetRoomInfo() *common.RoomInfo {
	if x != nil {
		return x.RoomInfo
	}
	return nil
}

// Join Trip Rsp
type JoinTripRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret       *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	JoinInfos []*JoinRoomRet `protobuf:"bytes,2,rep,name=join_infos,json=joinInfos,proto3" json:"join_infos,omitempty"`
}

func (x *JoinTripRsp) Reset() {
	*x = JoinTripRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_triprpc_triprpc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JoinTripRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinTripRsp) ProtoMessage() {}

func (x *JoinTripRsp) ProtoReflect() protoreflect.Message {
	mi := &file_triprpc_triprpc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinTripRsp.ProtoReflect.Descriptor instead.
func (*JoinTripRsp) Descriptor() ([]byte, []int) {
	return file_triprpc_triprpc_proto_rawDescGZIP(), []int{2}
}

func (x *JoinTripRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *JoinTripRsp) GetJoinInfos() []*JoinRoomRet {
	if x != nil {
		return x.JoinInfos
	}
	return nil
}

// Exit Trip
type ExitTripReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32            `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`                    // 产品 ID
	PondId    int64            `protobuf:"varint,2,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`                             // 钓场id
	GameType  common.GAME_TYPE `protobuf:"varint,3,opt,name=game_type,json=gameType,proto3,enum=common.GAME_TYPE" json:"game_type,omitempty"` // game type
	Fishers   []*common.Fisher `protobuf:"bytes,4,rep,name=fishers,proto3" json:"fishers,omitempty"`                                          // 钓鱼玩家
}

func (x *ExitTripReq) Reset() {
	*x = ExitTripReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_triprpc_triprpc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExitTripReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExitTripReq) ProtoMessage() {}

func (x *ExitTripReq) ProtoReflect() protoreflect.Message {
	mi := &file_triprpc_triprpc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExitTripReq.ProtoReflect.Descriptor instead.
func (*ExitTripReq) Descriptor() ([]byte, []int) {
	return file_triprpc_triprpc_proto_rawDescGZIP(), []int{3}
}

func (x *ExitTripReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *ExitTripReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *ExitTripReq) GetGameType() common.GAME_TYPE {
	if x != nil {
		return x.GameType
	}
	return common.GAME_TYPE(0)
}

func (x *ExitTripReq) GetFishers() []*common.Fisher {
	if x != nil {
		return x.Fishers
	}
	return nil
}

// Exit Trip
type ExitTripRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
}

func (x *ExitTripRsp) Reset() {
	*x = ExitTripRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_triprpc_triprpc_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExitTripRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExitTripRsp) ProtoMessage() {}

func (x *ExitTripRsp) ProtoReflect() protoreflect.Message {
	mi := &file_triprpc_triprpc_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExitTripRsp.ProtoReflect.Descriptor instead.
func (*ExitTripRsp) Descriptor() ([]byte, []int) {
	return file_triprpc_triprpc_proto_rawDescGZIP(), []int{4}
}

func (x *ExitTripRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// Query Trip
type QueryTripReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` // 玩家id
}

func (x *QueryTripReq) Reset() {
	*x = QueryTripReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_triprpc_triprpc_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryTripReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryTripReq) ProtoMessage() {}

func (x *QueryTripReq) ProtoReflect() protoreflect.Message {
	mi := &file_triprpc_triprpc_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryTripReq.ProtoReflect.Descriptor instead.
func (*QueryTripReq) Descriptor() ([]byte, []int) {
	return file_triprpc_triprpc_proto_rawDescGZIP(), []int{5}
}

func (x *QueryTripReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// Query Trip
type QueryTripRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result   `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	RoomInfo *common.RoomInfo `protobuf:"bytes,2,opt,name=room_info,json=roomInfo,proto3" json:"room_info,omitempty"` // 房间信息
}

func (x *QueryTripRsp) Reset() {
	*x = QueryTripRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_triprpc_triprpc_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryTripRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryTripRsp) ProtoMessage() {}

func (x *QueryTripRsp) ProtoReflect() protoreflect.Message {
	mi := &file_triprpc_triprpc_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryTripRsp.ProtoReflect.Descriptor instead.
func (*QueryTripRsp) Descriptor() ([]byte, []int) {
	return file_triprpc_triprpc_proto_rawDescGZIP(), []int{6}
}

func (x *QueryTripRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *QueryTripRsp) GetRoomInfo() *common.RoomInfo {
	if x != nil {
		return x.RoomInfo
	}
	return nil
}

// Query Players
type QueryRoomPlayersReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"` // 房间id
}

func (x *QueryRoomPlayersReq) Reset() {
	*x = QueryRoomPlayersReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_triprpc_triprpc_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRoomPlayersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRoomPlayersReq) ProtoMessage() {}

func (x *QueryRoomPlayersReq) ProtoReflect() protoreflect.Message {
	mi := &file_triprpc_triprpc_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRoomPlayersReq.ProtoReflect.Descriptor instead.
func (*QueryRoomPlayersReq) Descriptor() ([]byte, []int) {
	return file_triprpc_triprpc_proto_rawDescGZIP(), []int{7}
}

func (x *QueryRoomPlayersReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

// Query Players
type QueryRoomPlayersRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret     *common.Result   `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Fishers []*common.Fisher `protobuf:"bytes,2,rep,name=fishers,proto3" json:"fishers,omitempty"` // 钓鱼玩家
}

func (x *QueryRoomPlayersRsp) Reset() {
	*x = QueryRoomPlayersRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_triprpc_triprpc_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRoomPlayersRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRoomPlayersRsp) ProtoMessage() {}

func (x *QueryRoomPlayersRsp) ProtoReflect() protoreflect.Message {
	mi := &file_triprpc_triprpc_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRoomPlayersRsp.ProtoReflect.Descriptor instead.
func (*QueryRoomPlayersRsp) Descriptor() ([]byte, []int) {
	return file_triprpc_triprpc_proto_rawDescGZIP(), []int{8}
}

func (x *QueryRoomPlayersRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *QueryRoomPlayersRsp) GetFishers() []*common.Fisher {
	if x != nil {
		return x.Fishers
	}
	return nil
}

// 玩家断线离开(只删除房间中玩家数据 保留玩家房间数据)
type OfflineExitReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` // 玩家id
}

func (x *OfflineExitReq) Reset() {
	*x = OfflineExitReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_triprpc_triprpc_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfflineExitReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineExitReq) ProtoMessage() {}

func (x *OfflineExitReq) ProtoReflect() protoreflect.Message {
	mi := &file_triprpc_triprpc_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineExitReq.ProtoReflect.Descriptor instead.
func (*OfflineExitReq) Descriptor() ([]byte, []int) {
	return file_triprpc_triprpc_proto_rawDescGZIP(), []int{9}
}

func (x *OfflineExitReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// 玩家断线离开(只删除房间中玩家数据 保留玩家房间数据)
type OfflineExitRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
}

func (x *OfflineExitRsp) Reset() {
	*x = OfflineExitRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_triprpc_triprpc_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfflineExitRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineExitRsp) ProtoMessage() {}

func (x *OfflineExitRsp) ProtoReflect() protoreflect.Message {
	mi := &file_triprpc_triprpc_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineExitRsp.ProtoReflect.Descriptor instead.
func (*OfflineExitRsp) Descriptor() ([]byte, []int) {
	return file_triprpc_triprpc_proto_rawDescGZIP(), []int{10}
}

func (x *OfflineExitRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

var File_triprpc_triprpc_proto protoreflect.FileDescriptor

var file_triprpc_triprpc_proto_rawDesc = []byte{
	0x0a, 0x15, 0x74, 0x72, 0x69, 0x70, 0x72, 0x70, 0x63, 0x2f, 0x74, 0x72, 0x69, 0x70, 0x72, 0x70,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63,
	0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a,
	0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb8, 0x01, 0x0a, 0x0b, 0x4a, 0x6f, 0x69,
	0x6e, 0x54, 0x72, 0x69, 0x70, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x73, 0x70, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x73, 0x70, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x09, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x07, 0x66, 0x69, 0x73,
	0x68, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x73, 0x68,
	0x65, 0x72, 0x73, 0x22, 0x4e, 0x0a, 0x0b, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x6f, 0x6f, 0x6d, 0x52,
	0x65, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x03, 0x75, 0x69, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x64, 0x0a, 0x0b, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x72, 0x69, 0x70, 0x52,
	0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x03, 0x72, 0x65, 0x74, 0x12, 0x33, 0x0a, 0x0a, 0x6a, 0x6f, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52,
	0x70, 0x63, 0x2e, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x74, 0x52, 0x09,
	0x6a, 0x6f, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x9f, 0x01, 0x0a, 0x0b, 0x45, 0x78,
	0x69, 0x74, 0x54, 0x72, 0x69, 0x70, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49,
	0x64, 0x12, 0x2e, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x41,
	0x4d, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x28, 0x0a, 0x07, 0x66, 0x69, 0x73, 0x68, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73, 0x68,
	0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x73, 0x68, 0x65, 0x72, 0x73, 0x22, 0x2f, 0x0a, 0x0b, 0x45,
	0x78, 0x69, 0x74, 0x54, 0x72, 0x69, 0x70, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x22, 0x2b, 0x0a, 0x0c,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x54, 0x72, 0x69, 0x70, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22, 0x5f, 0x0a, 0x0c, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x54, 0x72, 0x69, 0x70, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2d, 0x0a, 0x09, 0x72,
	0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2e, 0x0a, 0x13, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x6f, 0x6f, 0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x22, 0x61, 0x0a, 0x13, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x6f, 0x6f, 0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03,
	0x72, 0x65, 0x74, 0x12, 0x28, 0x0a, 0x07, 0x66, 0x69, 0x73, 0x68, 0x65, 0x72, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69,
	0x73, 0x68, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x73, 0x68, 0x65, 0x72, 0x73, 0x22, 0x2d, 0x0a,
	0x0e, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x69, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22, 0x32, 0x0a, 0x0e,
	0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x69, 0x74, 0x52, 0x73, 0x70, 0x12, 0x20,
	0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74,
	0x32, 0xdb, 0x02, 0x0a, 0x0b, 0x54, 0x72, 0x69, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x3c, 0x0a, 0x0c, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x72, 0x69, 0x70, 0x52, 0x6f, 0x6f, 0x6d,
	0x12, 0x14, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x4a, 0x6f, 0x69, 0x6e, 0x54,
	0x72, 0x69, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63,
	0x2e, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x72, 0x69, 0x70, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x3c,
	0x0a, 0x0c, 0x45, 0x78, 0x69, 0x74, 0x54, 0x72, 0x69, 0x70, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x14,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x45, 0x78, 0x69, 0x74, 0x54, 0x72, 0x69,
	0x70, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x45,
	0x78, 0x69, 0x74, 0x54, 0x72, 0x69, 0x70, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x3b, 0x0a, 0x09,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x54, 0x72, 0x69, 0x70, 0x12, 0x15, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x52, 0x70, 0x63, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x54, 0x72, 0x69, 0x70, 0x52, 0x65, 0x71,
	0x1a, 0x15, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x54, 0x72, 0x69, 0x70, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x50, 0x0a, 0x10, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x6f, 0x6f, 0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x1c, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x6f, 0x6f,
	0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x6f, 0x6f, 0x6d, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x41, 0x0a, 0x0b, 0x4f,
	0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x69, 0x74, 0x12, 0x17, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x52, 0x70, 0x63, 0x2e, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x69, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x4f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x69, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x0b,
	0x5a, 0x09, 0x2e, 0x3b, 0x74, 0x72, 0x69, 0x70, 0x52, 0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_triprpc_triprpc_proto_rawDescOnce sync.Once
	file_triprpc_triprpc_proto_rawDescData = file_triprpc_triprpc_proto_rawDesc
)

func file_triprpc_triprpc_proto_rawDescGZIP() []byte {
	file_triprpc_triprpc_proto_rawDescOnce.Do(func() {
		file_triprpc_triprpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_triprpc_triprpc_proto_rawDescData)
	})
	return file_triprpc_triprpc_proto_rawDescData
}

var file_triprpc_triprpc_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_triprpc_triprpc_proto_goTypes = []interface{}{
	(*JoinTripReq)(nil),         // 0: userRpc.JoinTripReq
	(*JoinRoomRet)(nil),         // 1: userRpc.JoinRoomRet
	(*JoinTripRsp)(nil),         // 2: userRpc.JoinTripRsp
	(*ExitTripReq)(nil),         // 3: userRpc.ExitTripReq
	(*ExitTripRsp)(nil),         // 4: userRpc.ExitTripRsp
	(*QueryTripReq)(nil),        // 5: userRpc.QueryTripReq
	(*QueryTripRsp)(nil),        // 6: userRpc.QueryTripRsp
	(*QueryRoomPlayersReq)(nil), // 7: userRpc.QueryRoomPlayersReq
	(*QueryRoomPlayersRsp)(nil), // 8: userRpc.QueryRoomPlayersRsp
	(*OfflineExitReq)(nil),      // 9: userRpc.OfflineExitReq
	(*OfflineExitRsp)(nil),      // 10: userRpc.OfflineExitRsp
	(common.GAME_TYPE)(0),       // 11: common.GAME_TYPE
	(*common.Fisher)(nil),       // 12: common.Fisher
	(*common.RoomInfo)(nil),     // 13: common.RoomInfo
	(*common.Result)(nil),       // 14: common.Result
}
var file_triprpc_triprpc_proto_depIdxs = []int32{
	11, // 0: userRpc.JoinTripReq.game_type:type_name -> common.GAME_TYPE
	12, // 1: userRpc.JoinTripReq.fishers:type_name -> common.Fisher
	13, // 2: userRpc.JoinRoomRet.room_info:type_name -> common.RoomInfo
	14, // 3: userRpc.JoinTripRsp.ret:type_name -> common.Result
	1,  // 4: userRpc.JoinTripRsp.join_infos:type_name -> userRpc.JoinRoomRet
	11, // 5: userRpc.ExitTripReq.game_type:type_name -> common.GAME_TYPE
	12, // 6: userRpc.ExitTripReq.fishers:type_name -> common.Fisher
	14, // 7: userRpc.ExitTripRsp.ret:type_name -> common.Result
	14, // 8: userRpc.QueryTripRsp.ret:type_name -> common.Result
	13, // 9: userRpc.QueryTripRsp.room_info:type_name -> common.RoomInfo
	14, // 10: userRpc.QueryRoomPlayersRsp.ret:type_name -> common.Result
	12, // 11: userRpc.QueryRoomPlayersRsp.fishers:type_name -> common.Fisher
	14, // 12: userRpc.OfflineExitRsp.ret:type_name -> common.Result
	0,  // 13: userRpc.TripService.JoinTripRoom:input_type -> userRpc.JoinTripReq
	3,  // 14: userRpc.TripService.ExitTripRoom:input_type -> userRpc.ExitTripReq
	5,  // 15: userRpc.TripService.QueryTrip:input_type -> userRpc.QueryTripReq
	7,  // 16: userRpc.TripService.QueryRoomPlayers:input_type -> userRpc.QueryRoomPlayersReq
	9,  // 17: userRpc.TripService.OfflineExit:input_type -> userRpc.OfflineExitReq
	2,  // 18: userRpc.TripService.JoinTripRoom:output_type -> userRpc.JoinTripRsp
	4,  // 19: userRpc.TripService.ExitTripRoom:output_type -> userRpc.ExitTripRsp
	6,  // 20: userRpc.TripService.QueryTrip:output_type -> userRpc.QueryTripRsp
	8,  // 21: userRpc.TripService.QueryRoomPlayers:output_type -> userRpc.QueryRoomPlayersRsp
	10, // 22: userRpc.TripService.OfflineExit:output_type -> userRpc.OfflineExitRsp
	18, // [18:23] is the sub-list for method output_type
	13, // [13:18] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_triprpc_triprpc_proto_init() }
func file_triprpc_triprpc_proto_init() {
	if File_triprpc_triprpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_triprpc_triprpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JoinTripReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_triprpc_triprpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JoinRoomRet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_triprpc_triprpc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JoinTripRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_triprpc_triprpc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExitTripReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_triprpc_triprpc_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExitTripRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_triprpc_triprpc_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryTripReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_triprpc_triprpc_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryTripRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_triprpc_triprpc_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRoomPlayersReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_triprpc_triprpc_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRoomPlayersRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_triprpc_triprpc_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfflineExitReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_triprpc_triprpc_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfflineExitRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_triprpc_triprpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_triprpc_triprpc_proto_goTypes,
		DependencyIndexes: file_triprpc_triprpc_proto_depIdxs,
		MessageInfos:      file_triprpc_triprpc_proto_msgTypes,
	}.Build()
	File_triprpc_triprpc_proto = out.File
	file_triprpc_triprpc_proto_rawDesc = nil
	file_triprpc_triprpc_proto_goTypes = nil
	file_triprpc_triprpc_proto_depIdxs = nil
}
