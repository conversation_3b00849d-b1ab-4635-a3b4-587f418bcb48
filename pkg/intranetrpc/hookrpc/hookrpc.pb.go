// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: hookrpc/hookrpc.proto

package hookRpc

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 抛竿请求
type ThrowRodRouteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId  uint64                 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`   // 玩家id
	PondId    int64                  `protobuf:"varint,2,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`         // 钓场id
	RigId     int32                  `protobuf:"varint,3,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`            // 钓组id
	HookBait  *common.HookBait       `protobuf:"bytes,4,opt,name=hook_bait,json=hookBait,proto3" json:"hook_bait,omitempty"`    // 钩饵组合
	GridInfo  *common.ThrowGridInfo  `protobuf:"bytes,5,opt,name=grid_info,json=gridInfo,proto3" json:"grid_info,omitempty"`    // 抛鱼格信息
	HookHabit *common.HookHabitParam `protobuf:"bytes,6,opt,name=hook_habit,json=hookHabit,proto3" json:"hook_habit,omitempty"` // 中鱼习性参数
}

func (x *ThrowRodRouteReq) Reset() {
	*x = ThrowRodRouteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hookrpc_hookrpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThrowRodRouteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThrowRodRouteReq) ProtoMessage() {}

func (x *ThrowRodRouteReq) ProtoReflect() protoreflect.Message {
	mi := &file_hookrpc_hookrpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThrowRodRouteReq.ProtoReflect.Descriptor instead.
func (*ThrowRodRouteReq) Descriptor() ([]byte, []int) {
	return file_hookrpc_hookrpc_proto_rawDescGZIP(), []int{0}
}

func (x *ThrowRodRouteReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *ThrowRodRouteReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *ThrowRodRouteReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *ThrowRodRouteReq) GetHookBait() *common.HookBait {
	if x != nil {
		return x.HookBait
	}
	return nil
}

func (x *ThrowRodRouteReq) GetGridInfo() *common.ThrowGridInfo {
	if x != nil {
		return x.GridInfo
	}
	return nil
}

func (x *ThrowRodRouteReq) GetHookHabit() *common.HookHabitParam {
	if x != nil {
		return x.HookHabit
	}
	return nil
}

// 抛竿响应
type ThrowRodRouteRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret         *common.Result          `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                    // 返回结果
	SyncControl *common.FishSyncControl `protobuf:"bytes,2,opt,name=sync_control,json=syncControl,proto3" json:"sync_control,omitempty"` // 同步控制
}

func (x *ThrowRodRouteRsp) Reset() {
	*x = ThrowRodRouteRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hookrpc_hookrpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThrowRodRouteRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThrowRodRouteRsp) ProtoMessage() {}

func (x *ThrowRodRouteRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hookrpc_hookrpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThrowRodRouteRsp.ProtoReflect.Descriptor instead.
func (*ThrowRodRouteRsp) Descriptor() ([]byte, []int) {
	return file_hookrpc_hookrpc_proto_rawDescGZIP(), []int{1}
}

func (x *ThrowRodRouteRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ThrowRodRouteRsp) GetSyncControl() *common.FishSyncControl {
	if x != nil {
		return x.SyncControl
	}
	return nil
}

// 中鱼请求
type FishHookRouteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId  uint64                 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`   // 玩家id
	PondId    int64                  `protobuf:"varint,2,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`         // 钓场id
	RigId     int32                  `protobuf:"varint,3,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`            // 钓组id
	GridInfo  *common.ThrowGridInfo  `protobuf:"bytes,4,opt,name=grid_info,json=gridInfo,proto3" json:"grid_info,omitempty"`    // 抛鱼格信息
	HookHabit *common.HookHabitParam `protobuf:"bytes,5,opt,name=hook_habit,json=hookHabit,proto3" json:"hook_habit,omitempty"` // 中鱼习性参数
}

func (x *FishHookRouteReq) Reset() {
	*x = FishHookRouteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hookrpc_hookrpc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishHookRouteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishHookRouteReq) ProtoMessage() {}

func (x *FishHookRouteReq) ProtoReflect() protoreflect.Message {
	mi := &file_hookrpc_hookrpc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishHookRouteReq.ProtoReflect.Descriptor instead.
func (*FishHookRouteReq) Descriptor() ([]byte, []int) {
	return file_hookrpc_hookrpc_proto_rawDescGZIP(), []int{2}
}

func (x *FishHookRouteReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *FishHookRouteReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *FishHookRouteReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *FishHookRouteReq) GetGridInfo() *common.ThrowGridInfo {
	if x != nil {
		return x.GridInfo
	}
	return nil
}

func (x *FishHookRouteReq) GetHookHabit() *common.HookHabitParam {
	if x != nil {
		return x.HookHabit
	}
	return nil
}

// 中鱼响应
type FishHookRouteRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret         *common.Result   `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                       // 返回结果
	NextReqTime int64            `protobuf:"varint,2,opt,name=next_req_time,json=nextReqTime,proto3" json:"next_req_time,omitempty"` // 下一次请求时间(毫秒)
	FakeFishId  int64            `protobuf:"varint,3,opt,name=fake_fish_id,json=fakeFishId,proto3" json:"fake_fish_id,omitempty"`    // 假咬口鱼id
	FishInfo    *common.FishInfo `protobuf:"bytes,4,opt,name=fish_info,json=fishInfo,proto3" json:"fish_info,omitempty"`             // 中鱼的鱼信息
}

func (x *FishHookRouteRsp) Reset() {
	*x = FishHookRouteRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hookrpc_hookrpc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishHookRouteRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishHookRouteRsp) ProtoMessage() {}

func (x *FishHookRouteRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hookrpc_hookrpc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishHookRouteRsp.ProtoReflect.Descriptor instead.
func (*FishHookRouteRsp) Descriptor() ([]byte, []int) {
	return file_hookrpc_hookrpc_proto_rawDescGZIP(), []int{3}
}

func (x *FishHookRouteRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *FishHookRouteRsp) GetNextReqTime() int64 {
	if x != nil {
		return x.NextReqTime
	}
	return 0
}

func (x *FishHookRouteRsp) GetFakeFishId() int64 {
	if x != nil {
		return x.FakeFishId
	}
	return 0
}

func (x *FishHookRouteRsp) GetFishInfo() *common.FishInfo {
	if x != nil {
		return x.FishInfo
	}
	return nil
}

// 收竿请求
type CatchRodRouteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId  uint64                 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`   // 玩家id
	PondId    int64                  `protobuf:"varint,2,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`         // 池塘id
	RigId     int32                  `protobuf:"varint,3,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`            // 钓组id
	HookHabit *common.HookHabitParam `protobuf:"bytes,4,opt,name=hook_habit,json=hookHabit,proto3" json:"hook_habit,omitempty"` // 中鱼习性参数
}

func (x *CatchRodRouteReq) Reset() {
	*x = CatchRodRouteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hookrpc_hookrpc_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CatchRodRouteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CatchRodRouteReq) ProtoMessage() {}

func (x *CatchRodRouteReq) ProtoReflect() protoreflect.Message {
	mi := &file_hookrpc_hookrpc_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CatchRodRouteReq.ProtoReflect.Descriptor instead.
func (*CatchRodRouteReq) Descriptor() ([]byte, []int) {
	return file_hookrpc_hookrpc_proto_rawDescGZIP(), []int{4}
}

func (x *CatchRodRouteReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *CatchRodRouteReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *CatchRodRouteReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *CatchRodRouteReq) GetHookHabit() *common.HookHabitParam {
	if x != nil {
		return x.HookHabit
	}
	return nil
}

// 收竿响应
type CatchRodRouteRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                                          // 返回结果
	FishResult common.FISH_RESULT `protobuf:"varint,2,opt,name=fish_result,json=fishResult,proto3,enum=common.FISH_RESULT" json:"fish_result,omitempty"` // 捕获结果
	FishInfo   *common.FishInfo   `protobuf:"bytes,3,opt,name=fish_info,json=fishInfo,proto3" json:"fish_info,omitempty"`                                // 捕获的鱼信息
	HookBait   *common.HookBait   `protobuf:"bytes,4,opt,name=hook_bait,json=hookBait,proto3" json:"hook_bait,omitempty"`                                // 钩饵组合
}

func (x *CatchRodRouteRsp) Reset() {
	*x = CatchRodRouteRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hookrpc_hookrpc_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CatchRodRouteRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CatchRodRouteRsp) ProtoMessage() {}

func (x *CatchRodRouteRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hookrpc_hookrpc_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CatchRodRouteRsp.ProtoReflect.Descriptor instead.
func (*CatchRodRouteRsp) Descriptor() ([]byte, []int) {
	return file_hookrpc_hookrpc_proto_rawDescGZIP(), []int{5}
}

func (x *CatchRodRouteRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *CatchRodRouteRsp) GetFishResult() common.FISH_RESULT {
	if x != nil {
		return x.FishResult
	}
	return common.FISH_RESULT(0)
}

func (x *CatchRodRouteRsp) GetFishInfo() *common.FishInfo {
	if x != nil {
		return x.FishInfo
	}
	return nil
}

func (x *CatchRodRouteRsp) GetHookBait() *common.HookBait {
	if x != nil {
		return x.HookBait
	}
	return nil
}

// 搏鱼请求
type FishBattleRouteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId   uint64             `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                               // 玩家id
	RigId      int32              `protobuf:"varint,2,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`                                        // 钓组id
	FishResult common.FISH_RESULT `protobuf:"varint,3,opt,name=fish_result,json=fishResult,proto3,enum=common.FISH_RESULT" json:"fish_result,omitempty"` // 鱼状态
}

func (x *FishBattleRouteReq) Reset() {
	*x = FishBattleRouteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hookrpc_hookrpc_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishBattleRouteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishBattleRouteReq) ProtoMessage() {}

func (x *FishBattleRouteReq) ProtoReflect() protoreflect.Message {
	mi := &file_hookrpc_hookrpc_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishBattleRouteReq.ProtoReflect.Descriptor instead.
func (*FishBattleRouteReq) Descriptor() ([]byte, []int) {
	return file_hookrpc_hookrpc_proto_rawDescGZIP(), []int{6}
}

func (x *FishBattleRouteReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *FishBattleRouteReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *FishBattleRouteReq) GetFishResult() common.FISH_RESULT {
	if x != nil {
		return x.FishResult
	}
	return common.FISH_RESULT(0)
}

// 搏鱼响应
type FishBattleRouteRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result   `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                           // 返回结果
	FishInfo *common.FishInfo `protobuf:"bytes,2,opt,name=fish_info,json=fishInfo,proto3" json:"fish_info,omitempty"` // 捕获的鱼信息
}

func (x *FishBattleRouteRsp) Reset() {
	*x = FishBattleRouteRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hookrpc_hookrpc_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishBattleRouteRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishBattleRouteRsp) ProtoMessage() {}

func (x *FishBattleRouteRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hookrpc_hookrpc_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishBattleRouteRsp.ProtoReflect.Descriptor instead.
func (*FishBattleRouteRsp) Descriptor() ([]byte, []int) {
	return file_hookrpc_hookrpc_proto_rawDescGZIP(), []int{7}
}

func (x *FishBattleRouteRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *FishBattleRouteRsp) GetFishInfo() *common.FishInfo {
	if x != nil {
		return x.FishInfo
	}
	return nil
}

// 开始中鱼请求
type HookStartRouteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId  uint64                     `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                                 // 玩家id
	CalcType  common.HOOK_FISH_CALC_TYPE `protobuf:"varint,2,opt,name=calc_type,json=calcType,proto3,enum=common.HOOK_FISH_CALC_TYPE" json:"calc_type,omitempty"` // 中鱼计算类型
	PondId    int64                      `protobuf:"varint,3,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`                                       // 钓场id
	RigId     int32                      `protobuf:"varint,4,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`                                          // 钓组id
	HookBait  *common.HookBait           `protobuf:"bytes,5,opt,name=hook_bait,json=hookBait,proto3" json:"hook_bait,omitempty"`                                  // 钩饵组合
	HookHabit *common.HookHabitParam     `protobuf:"bytes,6,opt,name=hook_habit,json=hookHabit,proto3" json:"hook_habit,omitempty"`                               // 中鱼习性参数
}

func (x *HookStartRouteReq) Reset() {
	*x = HookStartRouteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hookrpc_hookrpc_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HookStartRouteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HookStartRouteReq) ProtoMessage() {}

func (x *HookStartRouteReq) ProtoReflect() protoreflect.Message {
	mi := &file_hookrpc_hookrpc_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HookStartRouteReq.ProtoReflect.Descriptor instead.
func (*HookStartRouteReq) Descriptor() ([]byte, []int) {
	return file_hookrpc_hookrpc_proto_rawDescGZIP(), []int{8}
}

func (x *HookStartRouteReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *HookStartRouteReq) GetCalcType() common.HOOK_FISH_CALC_TYPE {
	if x != nil {
		return x.CalcType
	}
	return common.HOOK_FISH_CALC_TYPE(0)
}

func (x *HookStartRouteReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *HookStartRouteReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *HookStartRouteReq) GetHookBait() *common.HookBait {
	if x != nil {
		return x.HookBait
	}
	return nil
}

func (x *HookStartRouteReq) GetHookHabit() *common.HookHabitParam {
	if x != nil {
		return x.HookHabit
	}
	return nil
}

// 开始中鱼响应
type HookStartRouteRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret         *common.Result          `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                    // 结果
	SyncControl *common.FishSyncControl `protobuf:"bytes,2,opt,name=sync_control,json=syncControl,proto3" json:"sync_control,omitempty"` // 请求同步控制
}

func (x *HookStartRouteRsp) Reset() {
	*x = HookStartRouteRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hookrpc_hookrpc_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HookStartRouteRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HookStartRouteRsp) ProtoMessage() {}

func (x *HookStartRouteRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hookrpc_hookrpc_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HookStartRouteRsp.ProtoReflect.Descriptor instead.
func (*HookStartRouteRsp) Descriptor() ([]byte, []int) {
	return file_hookrpc_hookrpc_proto_rawDescGZIP(), []int{9}
}

func (x *HookStartRouteRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *HookStartRouteRsp) GetSyncControl() *common.FishSyncControl {
	if x != nil {
		return x.SyncControl
	}
	return nil
}

var File_hookrpc_hookrpc_proto protoreflect.FileDescriptor

var file_hookrpc_hookrpc_proto_rawDesc = []byte{
	0x0a, 0x15, 0x68, 0x6f, 0x6f, 0x6b, 0x72, 0x70, 0x63, 0x2f, 0x68, 0x6f, 0x6f, 0x6b, 0x72, 0x70,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x70, 0x63,
	0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a,
	0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf9, 0x01, 0x0a, 0x10, 0x54, 0x68, 0x72,
	0x6f, 0x77, 0x52, 0x6f, 0x64, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f,
	0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e,
	0x64, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x68, 0x6f,
	0x6f, 0x6b, 0x5f, 0x62, 0x61, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74, 0x52,
	0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74, 0x12, 0x32, 0x0a, 0x09, 0x67, 0x72, 0x69,
	0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x68, 0x72, 0x6f, 0x77, 0x47, 0x72, 0x69, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x67, 0x72, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x35, 0x0a,
	0x0a, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x68, 0x61, 0x62, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x48,
	0x61, 0x62, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x09, 0x68, 0x6f, 0x6f, 0x6b, 0x48,
	0x61, 0x62, 0x69, 0x74, 0x22, 0x70, 0x0a, 0x10, 0x54, 0x68, 0x72, 0x6f, 0x77, 0x52, 0x6f, 0x64,
	0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x3a, 0x0a, 0x0c, 0x73, 0x79,
	0x6e, 0x63, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x53, 0x79,
	0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x0b, 0x73, 0x79, 0x6e, 0x63, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x22, 0xca, 0x01, 0x0a, 0x10, 0x46, 0x69, 0x73, 0x68, 0x48,
	0x6f, 0x6f, 0x6b, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08,
	0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49,
	0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x09, 0x67, 0x72, 0x69, 0x64,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x68, 0x72, 0x6f, 0x77, 0x47, 0x72, 0x69, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x08, 0x67, 0x72, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x35, 0x0a, 0x0a,
	0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x68, 0x61, 0x62, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x48, 0x61,
	0x62, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x09, 0x68, 0x6f, 0x6f, 0x6b, 0x48, 0x61,
	0x62, 0x69, 0x74, 0x22, 0xa9, 0x01, 0x0a, 0x10, 0x46, 0x69, 0x73, 0x68, 0x48, 0x6f, 0x6f, 0x6b,
	0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x65,
	0x78, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0c, 0x66, 0x61, 0x6b, 0x65, 0x5f, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x66, 0x61, 0x6b, 0x65, 0x46, 0x69, 0x73, 0x68, 0x49, 0x64,
	0x12, 0x2d, 0x0a, 0x09, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x66, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x96, 0x01, 0x0a, 0x10, 0x43, 0x61, 0x74, 0x63, 0x68, 0x52, 0x6f, 0x64, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49,
	0x64, 0x12, 0x35, 0x0a, 0x0a, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x68, 0x61, 0x62, 0x69, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48,
	0x6f, 0x6f, 0x6b, 0x48, 0x61, 0x62, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x09, 0x68,
	0x6f, 0x6f, 0x6b, 0x48, 0x61, 0x62, 0x69, 0x74, 0x22, 0xc8, 0x01, 0x0a, 0x10, 0x43, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x6f, 0x64, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a,
	0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12,
	0x34, 0x0a, 0x0b, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x49,
	0x53, 0x48, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x52, 0x0a, 0x66, 0x69, 0x73, 0x68, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2d, 0x0a, 0x09, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x66, 0x69, 0x73, 0x68,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2d, 0x0a, 0x09, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x62, 0x61, 0x69,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74, 0x52, 0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x42,
	0x61, 0x69, 0x74, 0x22, 0x7e, 0x0a, 0x12, 0x46, 0x69, 0x73, 0x68, 0x42, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x34, 0x0a,
	0x0b, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x49, 0x53, 0x48,
	0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x52, 0x0a, 0x66, 0x69, 0x73, 0x68, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x65, 0x0a, 0x12, 0x46, 0x69, 0x73, 0x68, 0x42, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2d, 0x0a, 0x09, 0x66,
	0x69, 0x73, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x66, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x80, 0x02, 0x0a, 0x11, 0x48,
	0x6f, 0x6f, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x38, 0x0a,
	0x09, 0x63, 0x61, 0x6c, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x4f, 0x4f, 0x4b, 0x5f, 0x46,
	0x49, 0x53, 0x48, 0x5f, 0x43, 0x41, 0x4c, 0x43, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x63,
	0x61, 0x6c, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64,
	0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x68, 0x6f, 0x6f, 0x6b, 0x5f,
	0x62, 0x61, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74, 0x52, 0x08, 0x68, 0x6f,
	0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74, 0x12, 0x35, 0x0a, 0x0a, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x68,
	0x61, 0x62, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x48, 0x61, 0x62, 0x69, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x52, 0x09, 0x68, 0x6f, 0x6f, 0x6b, 0x48, 0x61, 0x62, 0x69, 0x74, 0x22, 0x71, 0x0a,
	0x11, 0x48, 0x6f, 0x6f, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x03, 0x72, 0x65, 0x74, 0x12, 0x3a, 0x0a, 0x0c, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x52, 0x0b, 0x73, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x32, 0x88, 0x03, 0x0a, 0x0b, 0x48, 0x6f, 0x6f, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x48, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54, 0x68, 0x72, 0x6f, 0x77, 0x52, 0x6f, 0x64, 0x52,
	0x65, 0x71, 0x12, 0x19, 0x2e, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x70, 0x63, 0x2e, 0x54, 0x68, 0x72,
	0x6f, 0x77, 0x52, 0x6f, 0x64, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e,
	0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x70, 0x63, 0x2e, 0x54, 0x68, 0x72, 0x6f, 0x77, 0x52, 0x6f, 0x64,
	0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x0e, 0x47, 0x65,
	0x74, 0x46, 0x69, 0x73, 0x68, 0x48, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x19, 0x2e, 0x68,
	0x6f, 0x6f, 0x6b, 0x52, 0x70, 0x63, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x48, 0x6f, 0x6f, 0x6b, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x70,
	0x63, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x48, 0x6f, 0x6f, 0x6b, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x19, 0x2e, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x70, 0x63,
	0x2e, 0x43, 0x61, 0x74, 0x63, 0x68, 0x52, 0x6f, 0x64, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x19, 0x2e, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x70, 0x63, 0x2e, 0x43, 0x61, 0x74, 0x63,
	0x68, 0x52, 0x6f, 0x64, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4e,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x46, 0x69, 0x73, 0x68, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x1b, 0x2e, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x70, 0x63, 0x2e, 0x46, 0x69, 0x73,
	0x68, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x1b, 0x2e, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x70, 0x63, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x42, 0x61,
	0x74, 0x74, 0x6c, 0x65, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4b,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x6f, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x1a, 0x2e, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x70, 0x63, 0x2e, 0x48, 0x6f, 0x6f, 0x6b,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e,
	0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x70, 0x63, 0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x0b, 0x5a, 0x09, 0x2e,
	0x3b, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_hookrpc_hookrpc_proto_rawDescOnce sync.Once
	file_hookrpc_hookrpc_proto_rawDescData = file_hookrpc_hookrpc_proto_rawDesc
)

func file_hookrpc_hookrpc_proto_rawDescGZIP() []byte {
	file_hookrpc_hookrpc_proto_rawDescOnce.Do(func() {
		file_hookrpc_hookrpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_hookrpc_hookrpc_proto_rawDescData)
	})
	return file_hookrpc_hookrpc_proto_rawDescData
}

var file_hookrpc_hookrpc_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_hookrpc_hookrpc_proto_goTypes = []interface{}{
	(*ThrowRodRouteReq)(nil),        // 0: hookRpc.ThrowRodRouteReq
	(*ThrowRodRouteRsp)(nil),        // 1: hookRpc.ThrowRodRouteRsp
	(*FishHookRouteReq)(nil),        // 2: hookRpc.FishHookRouteReq
	(*FishHookRouteRsp)(nil),        // 3: hookRpc.FishHookRouteRsp
	(*CatchRodRouteReq)(nil),        // 4: hookRpc.CatchRodRouteReq
	(*CatchRodRouteRsp)(nil),        // 5: hookRpc.CatchRodRouteRsp
	(*FishBattleRouteReq)(nil),      // 6: hookRpc.FishBattleRouteReq
	(*FishBattleRouteRsp)(nil),      // 7: hookRpc.FishBattleRouteRsp
	(*HookStartRouteReq)(nil),       // 8: hookRpc.HookStartRouteReq
	(*HookStartRouteRsp)(nil),       // 9: hookRpc.HookStartRouteRsp
	(*common.HookBait)(nil),         // 10: common.HookBait
	(*common.ThrowGridInfo)(nil),    // 11: common.ThrowGridInfo
	(*common.HookHabitParam)(nil),   // 12: common.HookHabitParam
	(*common.Result)(nil),           // 13: common.Result
	(*common.FishSyncControl)(nil),  // 14: common.FishSyncControl
	(*common.FishInfo)(nil),         // 15: common.FishInfo
	(common.FISH_RESULT)(0),         // 16: common.FISH_RESULT
	(common.HOOK_FISH_CALC_TYPE)(0), // 17: common.HOOK_FISH_CALC_TYPE
}
var file_hookrpc_hookrpc_proto_depIdxs = []int32{
	10, // 0: hookRpc.ThrowRodRouteReq.hook_bait:type_name -> common.HookBait
	11, // 1: hookRpc.ThrowRodRouteReq.grid_info:type_name -> common.ThrowGridInfo
	12, // 2: hookRpc.ThrowRodRouteReq.hook_habit:type_name -> common.HookHabitParam
	13, // 3: hookRpc.ThrowRodRouteRsp.ret:type_name -> common.Result
	14, // 4: hookRpc.ThrowRodRouteRsp.sync_control:type_name -> common.FishSyncControl
	11, // 5: hookRpc.FishHookRouteReq.grid_info:type_name -> common.ThrowGridInfo
	12, // 6: hookRpc.FishHookRouteReq.hook_habit:type_name -> common.HookHabitParam
	13, // 7: hookRpc.FishHookRouteRsp.ret:type_name -> common.Result
	15, // 8: hookRpc.FishHookRouteRsp.fish_info:type_name -> common.FishInfo
	12, // 9: hookRpc.CatchRodRouteReq.hook_habit:type_name -> common.HookHabitParam
	13, // 10: hookRpc.CatchRodRouteRsp.ret:type_name -> common.Result
	16, // 11: hookRpc.CatchRodRouteRsp.fish_result:type_name -> common.FISH_RESULT
	15, // 12: hookRpc.CatchRodRouteRsp.fish_info:type_name -> common.FishInfo
	10, // 13: hookRpc.CatchRodRouteRsp.hook_bait:type_name -> common.HookBait
	16, // 14: hookRpc.FishBattleRouteReq.fish_result:type_name -> common.FISH_RESULT
	13, // 15: hookRpc.FishBattleRouteRsp.ret:type_name -> common.Result
	15, // 16: hookRpc.FishBattleRouteRsp.fish_info:type_name -> common.FishInfo
	17, // 17: hookRpc.HookStartRouteReq.calc_type:type_name -> common.HOOK_FISH_CALC_TYPE
	10, // 18: hookRpc.HookStartRouteReq.hook_bait:type_name -> common.HookBait
	12, // 19: hookRpc.HookStartRouteReq.hook_habit:type_name -> common.HookHabitParam
	13, // 20: hookRpc.HookStartRouteRsp.ret:type_name -> common.Result
	14, // 21: hookRpc.HookStartRouteRsp.sync_control:type_name -> common.FishSyncControl
	0,  // 22: hookRpc.HookService.GetThrowRodReq:input_type -> hookRpc.ThrowRodRouteReq
	2,  // 23: hookRpc.HookService.GetFishHookReq:input_type -> hookRpc.FishHookRouteReq
	4,  // 24: hookRpc.HookService.GetCatchRodReq:input_type -> hookRpc.CatchRodRouteReq
	6,  // 25: hookRpc.HookService.GetFishBattleReq:input_type -> hookRpc.FishBattleRouteReq
	8,  // 26: hookRpc.HookService.GetHookStartReq:input_type -> hookRpc.HookStartRouteReq
	1,  // 27: hookRpc.HookService.GetThrowRodReq:output_type -> hookRpc.ThrowRodRouteRsp
	3,  // 28: hookRpc.HookService.GetFishHookReq:output_type -> hookRpc.FishHookRouteRsp
	5,  // 29: hookRpc.HookService.GetCatchRodReq:output_type -> hookRpc.CatchRodRouteRsp
	7,  // 30: hookRpc.HookService.GetFishBattleReq:output_type -> hookRpc.FishBattleRouteRsp
	9,  // 31: hookRpc.HookService.GetHookStartReq:output_type -> hookRpc.HookStartRouteRsp
	27, // [27:32] is the sub-list for method output_type
	22, // [22:27] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_hookrpc_hookrpc_proto_init() }
func file_hookrpc_hookrpc_proto_init() {
	if File_hookrpc_hookrpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_hookrpc_hookrpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThrowRodRouteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hookrpc_hookrpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThrowRodRouteRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hookrpc_hookrpc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishHookRouteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hookrpc_hookrpc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishHookRouteRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hookrpc_hookrpc_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CatchRodRouteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hookrpc_hookrpc_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CatchRodRouteRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hookrpc_hookrpc_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishBattleRouteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hookrpc_hookrpc_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishBattleRouteRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hookrpc_hookrpc_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HookStartRouteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hookrpc_hookrpc_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HookStartRouteRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hookrpc_hookrpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hookrpc_hookrpc_proto_goTypes,
		DependencyIndexes: file_hookrpc_hookrpc_proto_depIdxs,
		MessageInfos:      file_hookrpc_hookrpc_proto_msgTypes,
	}.Build()
	File_hookrpc_hookrpc_proto = out.File
	file_hookrpc_hookrpc_proto_rawDesc = nil
	file_hookrpc_hookrpc_proto_goTypes = nil
	file_hookrpc_hookrpc_proto_depIdxs = nil
}
