// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.27.0
// source: hookrpc/hookrpc.proto

package hookRpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	HookService_GetThrowRodReq_FullMethodName   = "/hookRpc.HookService/GetThrowRodReq"
	HookService_GetFishHookReq_FullMethodName   = "/hookRpc.HookService/GetFishHookReq"
	HookService_GetCatchRodReq_FullMethodName   = "/hookRpc.HookService/GetCatchRodReq"
	HookService_GetFishBattleReq_FullMethodName = "/hookRpc.HookService/GetFishBattleReq"
	HookService_GetHookStartReq_FullMethodName  = "/hookRpc.HookService/GetHookStartReq"
)

// HookServiceClient is the client API for HookService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HookServiceClient interface {
	// 抛竿请求
	GetThrowRodReq(ctx context.Context, in *ThrowRodRouteReq, opts ...grpc.CallOption) (*ThrowRodRouteRsp, error)
	// 中鱼请求
	GetFishHookReq(ctx context.Context, in *FishHookRouteReq, opts ...grpc.CallOption) (*FishHookRouteRsp, error)
	// 收杆请求
	GetCatchRodReq(ctx context.Context, in *CatchRodRouteReq, opts ...grpc.CallOption) (*CatchRodRouteRsp, error)
	// 搏鱼请求
	GetFishBattleReq(ctx context.Context, in *FishBattleRouteReq, opts ...grpc.CallOption) (*FishBattleRouteRsp, error)
	// 开始中鱼请求
	GetHookStartReq(ctx context.Context, in *HookStartRouteReq, opts ...grpc.CallOption) (*HookStartRouteRsp, error)
}

type hookServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewHookServiceClient(cc grpc.ClientConnInterface) HookServiceClient {
	return &hookServiceClient{cc}
}

func (c *hookServiceClient) GetThrowRodReq(ctx context.Context, in *ThrowRodRouteReq, opts ...grpc.CallOption) (*ThrowRodRouteRsp, error) {
	out := new(ThrowRodRouteRsp)
	err := c.cc.Invoke(ctx, HookService_GetThrowRodReq_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hookServiceClient) GetFishHookReq(ctx context.Context, in *FishHookRouteReq, opts ...grpc.CallOption) (*FishHookRouteRsp, error) {
	out := new(FishHookRouteRsp)
	err := c.cc.Invoke(ctx, HookService_GetFishHookReq_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hookServiceClient) GetCatchRodReq(ctx context.Context, in *CatchRodRouteReq, opts ...grpc.CallOption) (*CatchRodRouteRsp, error) {
	out := new(CatchRodRouteRsp)
	err := c.cc.Invoke(ctx, HookService_GetCatchRodReq_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hookServiceClient) GetFishBattleReq(ctx context.Context, in *FishBattleRouteReq, opts ...grpc.CallOption) (*FishBattleRouteRsp, error) {
	out := new(FishBattleRouteRsp)
	err := c.cc.Invoke(ctx, HookService_GetFishBattleReq_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *hookServiceClient) GetHookStartReq(ctx context.Context, in *HookStartRouteReq, opts ...grpc.CallOption) (*HookStartRouteRsp, error) {
	out := new(HookStartRouteRsp)
	err := c.cc.Invoke(ctx, HookService_GetHookStartReq_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HookServiceServer is the server API for HookService service.
// All implementations should embed UnimplementedHookServiceServer
// for forward compatibility
type HookServiceServer interface {
	// 抛竿请求
	GetThrowRodReq(context.Context, *ThrowRodRouteReq) (*ThrowRodRouteRsp, error)
	// 中鱼请求
	GetFishHookReq(context.Context, *FishHookRouteReq) (*FishHookRouteRsp, error)
	// 收杆请求
	GetCatchRodReq(context.Context, *CatchRodRouteReq) (*CatchRodRouteRsp, error)
	// 搏鱼请求
	GetFishBattleReq(context.Context, *FishBattleRouteReq) (*FishBattleRouteRsp, error)
	// 开始中鱼请求
	GetHookStartReq(context.Context, *HookStartRouteReq) (*HookStartRouteRsp, error)
}

// UnimplementedHookServiceServer should be embedded to have forward compatible implementations.
type UnimplementedHookServiceServer struct {
}

func (UnimplementedHookServiceServer) GetThrowRodReq(context.Context, *ThrowRodRouteReq) (*ThrowRodRouteRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetThrowRodReq not implemented")
}
func (UnimplementedHookServiceServer) GetFishHookReq(context.Context, *FishHookRouteReq) (*FishHookRouteRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFishHookReq not implemented")
}
func (UnimplementedHookServiceServer) GetCatchRodReq(context.Context, *CatchRodRouteReq) (*CatchRodRouteRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCatchRodReq not implemented")
}
func (UnimplementedHookServiceServer) GetFishBattleReq(context.Context, *FishBattleRouteReq) (*FishBattleRouteRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFishBattleReq not implemented")
}
func (UnimplementedHookServiceServer) GetHookStartReq(context.Context, *HookStartRouteReq) (*HookStartRouteRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHookStartReq not implemented")
}

// UnsafeHookServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HookServiceServer will
// result in compilation errors.
type UnsafeHookServiceServer interface {
	mustEmbedUnimplementedHookServiceServer()
}

func RegisterHookServiceServer(s grpc.ServiceRegistrar, srv HookServiceServer) {
	s.RegisterService(&HookService_ServiceDesc, srv)
}

func _HookService_GetThrowRodReq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThrowRodRouteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HookServiceServer).GetThrowRodReq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HookService_GetThrowRodReq_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HookServiceServer).GetThrowRodReq(ctx, req.(*ThrowRodRouteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HookService_GetFishHookReq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FishHookRouteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HookServiceServer).GetFishHookReq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HookService_GetFishHookReq_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HookServiceServer).GetFishHookReq(ctx, req.(*FishHookRouteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HookService_GetCatchRodReq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CatchRodRouteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HookServiceServer).GetCatchRodReq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HookService_GetCatchRodReq_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HookServiceServer).GetCatchRodReq(ctx, req.(*CatchRodRouteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HookService_GetFishBattleReq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FishBattleRouteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HookServiceServer).GetFishBattleReq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HookService_GetFishBattleReq_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HookServiceServer).GetFishBattleReq(ctx, req.(*FishBattleRouteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _HookService_GetHookStartReq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HookStartRouteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HookServiceServer).GetHookStartReq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: HookService_GetHookStartReq_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HookServiceServer).GetHookStartReq(ctx, req.(*HookStartRouteReq))
	}
	return interceptor(ctx, in, info, handler)
}

// HookService_ServiceDesc is the grpc.ServiceDesc for HookService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HookService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "hookRpc.HookService",
	HandlerType: (*HookServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetThrowRodReq",
			Handler:    _HookService_GetThrowRodReq_Handler,
		},
		{
			MethodName: "GetFishHookReq",
			Handler:    _HookService_GetFishHookReq_Handler,
		},
		{
			MethodName: "GetCatchRodReq",
			Handler:    _HookService_GetCatchRodReq_Handler,
		},
		{
			MethodName: "GetFishBattleReq",
			Handler:    _HookService_GetFishBattleReq_Handler,
		},
		{
			MethodName: "GetHookStartReq",
			Handler:    _HookService_GetHookStartReq_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "hookrpc/hookrpc.proto",
}
