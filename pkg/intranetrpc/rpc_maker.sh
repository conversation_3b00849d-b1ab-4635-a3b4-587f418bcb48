#!/bin/bash

genRpc(){
    local dirName=$1
    protoc -I=. \
      -I="$GOPATH"/src/base \
      -I="$GOPATH"/src/base/fancy-common/protocols/common \
      -I="$GOPATH"/src/base/fancy-common/protocols \
      --go_out=. --go_opt=paths=source_relative \
      --go-grpc_out=require_unimplemented_servers=false:. --go-grpc_opt=paths=source_relative ./"$dirName"/*.proto
}

#genRpc gatewayrpc
#genRpc loginrpc
#genRpc triprpc
#genRpc userrpc
#genRpc hookrpc
#genRpc assetrpc

for dir in ./*/
do
    if [ -d "$dir" ]; then
      echo "${dir%*/}"
      genRpc "${dir%*/}"
    fi
done