// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type TaskCondDetails struct {
	Label   int32 `json:"label"`
	Operate int32 `json:"operate"`
	Value   int64 `json:"value"`
}

type TaskCond struct {
	Id           int64             `json:"id"`
	Name         string            `json:"name"`
	Mark         string            `json:"mark"`
	DescLanguage int64             `json:"descLanguage"`
	Event        int64             `json:"event"`
	Offset       float32           `json:"offset"`
	Target       int64             `json:"target"`
	Field        int32             `json:"field"`
	AddType      int32             `json:"addType"`
	Control      int32             `json:"control"`
	Details      []TaskCondDetails `json:"details"`
}

var lockTaskCond sync.RWMutex
var storeTaskCond sync.Map
var strTaskCond string = "task_cond"

func InitTaskCondCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strTaskCond, watchTaskCondFunc)
	return LoadAllTaskCondCfg()
}

func fixKeyTaskCond(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strTaskCond)
}
func watchTaskCondFunc(key string, js string) {
	mapTaskCond := make(map[int64]*TaskCond)
	errUnmarshal := json.Unmarshal([]byte(js), &mapTaskCond)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeTaskCond.Store(key, mapTaskCond)
}

func GetAllTaskCond(option ...consulconfig.Option) map[int64]*TaskCond {
	fitKey := fixKeyTaskCond(option...)
	store, ok := storeTaskCond.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskCond)
		if ok {
			return storeMap
		}
	}
	lockTaskCond.Lock()
	defer lockTaskCond.Unlock()
	store, ok = storeTaskCond.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskCond)
		if ok {
			return storeMap
		}
	}
	tblTaskCond := make(map[int64]*TaskCond)
	task_cond_str, err := consulconfig.GetInstance().GetConfig(strTaskCond, option...)
	if task_cond_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_cond_str), &tblTaskCond)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_cond", errUnmarshal)
		return nil
	}
	storeTaskCond.Store(fitKey, tblTaskCond)
	return tblTaskCond
}

func GetTaskCond(id int64, option ...consulconfig.Option) *TaskCond {
	fitKey := fixKeyTaskCond(option...)
	store, ok := storeTaskCond.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskCond)
		if ok {
			return storeMap[id]
		}
	}
	lockTaskCond.Lock()
	defer lockTaskCond.Unlock()
	store, ok = storeTaskCond.Load(fitKey)
	if ok {
		tblTaskCond, ok := store.(*TaskCond)
		if ok {
			return tblTaskCond
		}
	}
	tblTaskCond := make(map[int64]*TaskCond)
	task_cond_str, err := consulconfig.GetInstance().GetConfig(strTaskCond, option...)
	if task_cond_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_cond_str), &tblTaskCond)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_cond", errUnmarshal)
		return nil
	}
	storeTaskCond.Store(fitKey, tblTaskCond)
	return tblTaskCond[id]
}

func LoadAllTaskCondCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strTaskCond, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "TaskCond", successChannels)
	return nil
}
