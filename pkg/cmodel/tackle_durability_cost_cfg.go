// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type TackleDurabilityCost struct {
	Id                       int64   `json:"id"`
	CastDurabilityCostRange  []int32 `json:"castDurabilityCostRange"`
	CastDurabilityCostFactor []int32 `json:"castDurabilityCostFactor"`
	CastDurabilityCostValue  []int32 `json:"castDurabilityCostValue"`
	RodDurabilityCostRange   []int32 `json:"rodDurabilityCostRange"`
	RodDurabilityCostFactor  []int32 `json:"rodDurabilityCostFactor"`
	RodDurabilityCostValue   []int32 `json:"rodDurabilityCostValue"`
	ReelDurabilityCostRange  []int32 `json:"reelDurabilityCostRange"`
	ReelInFactor             int32   `json:"reelInFactor"`
	ReelOutFactor            int32   `json:"reelOutFactor"`
	ReelDurabilityCostFactor []int32 `json:"reelDurabilityCostFactor"`
	ReelDurabilityCostValue  []int32 `json:"reelDurabilityCostValue"`
	LineDurabilityCostRange  []int32 `json:"lineDurabilityCostRange"`
	LineDurabilityCostFactor []int32 `json:"lineDurabilityCostFactor"`
	LineDurabilityCostValue  []int32 `json:"lineDurabilityCostValue"`
	BobbersCostPercent       int32   `json:"bobbersCostPercent"`
	BobbersCostProb          int32   `json:"bobbersCostProb"`
	BaitCostPercent          int32   `json:"baitCostPercent"`
	BaitUncatchCostPercent   int32   `json:"baitUncatchCostPercent"`
	BaitUncatchCostProb      int32   `json:"baitUncatchCostProb"`
	LureCostPercent          int32   `json:"lureCostPercent"`
	LureUncatchCostPercent   int32   `json:"lureUncatchCostPercent"`
	LureUncatchCostProb      int32   `json:"lureUncatchCostProb"`
}

var lockTackleDurabilityCost sync.RWMutex
var storeTackleDurabilityCost sync.Map
var strTackleDurabilityCost string = "tackle_durability_cost"

func InitTackleDurabilityCostCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strTackleDurabilityCost, watchTackleDurabilityCostFunc)
	return LoadAllTackleDurabilityCostCfg()
}

func fixKeyTackleDurabilityCost(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strTackleDurabilityCost)
}
func watchTackleDurabilityCostFunc(key string, js string) {
	store, ok := storeTackleDurabilityCost.Load(key)
	if !ok {
		store = &TackleDurabilityCost{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeTackleDurabilityCost.Store(key, store)
}

func GetTackleDurabilityCost(option ...consulconfig.Option) *TackleDurabilityCost {
	fitKey := fixKeyTackleDurabilityCost(option...)
	store, ok := storeTackleDurabilityCost.Load(fitKey)
	if ok {
		tblTackleDurabilityCost, ok := store.(*TackleDurabilityCost)
		if ok {
			return tblTackleDurabilityCost
		}
	}
	lockTackleDurabilityCost.Lock()
	defer lockTackleDurabilityCost.Unlock()
	store, ok = storeTackleDurabilityCost.Load(fitKey)
	if ok {
		tblTackleDurabilityCost, ok := store.(*TackleDurabilityCost)
		if ok {
			return tblTackleDurabilityCost
		}
	}
	tblTackleDurabilityCost := &TackleDurabilityCost{}
	tackle_durability_cost_str, err := consulconfig.GetInstance().GetConfig(strTackleDurabilityCost, option...)
	if tackle_durability_cost_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(tackle_durability_cost_str), &tblTackleDurabilityCost)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strTackleDurabilityCost, errUnmarshal, tackle_durability_cost_str)
		return nil
	}
	storeTackleDurabilityCost.Store(fitKey, tblTackleDurabilityCost)
	return tblTackleDurabilityCost
}

func LoadAllTackleDurabilityCostCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strTackleDurabilityCost, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "TackleDurabilityCost", successChannels)
	return nil
}
