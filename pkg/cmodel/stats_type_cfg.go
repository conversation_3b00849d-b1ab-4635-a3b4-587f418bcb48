// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type StatsType struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
	Mark string `json:"mark"`
}

var lockStatsType sync.RWMutex
var storeStatsType sync.Map
var strStatsType string = "stats_type"

func InitStatsTypeCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strStatsType, watchStatsTypeFunc)
	return LoadAllStatsTypeCfg()
}

func fixKeyStatsType(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strStatsType)
}
func watchStatsTypeFunc(key string, js string) {
	mapStatsType := make(map[int64]*StatsType)
	errUnmarshal := json.Unmarshal([]byte(js), &mapStatsType)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeStatsType.Store(key, mapStatsType)
}

func GetAllStatsType(option ...consulconfig.Option) map[int64]*StatsType {
	fitKey := fixKeyStatsType(option...)
	store, ok := storeStatsType.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StatsType)
		if ok {
			return storeMap
		}
	}
	lockStatsType.Lock()
	defer lockStatsType.Unlock()
	store, ok = storeStatsType.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StatsType)
		if ok {
			return storeMap
		}
	}
	tblStatsType := make(map[int64]*StatsType)
	stats_type_str, err := consulconfig.GetInstance().GetConfig(strStatsType, option...)
	if stats_type_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stats_type_str), &tblStatsType)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stats_type", errUnmarshal)
		return nil
	}
	storeStatsType.Store(fitKey, tblStatsType)
	return tblStatsType
}

func GetStatsType(id int64, option ...consulconfig.Option) *StatsType {
	fitKey := fixKeyStatsType(option...)
	store, ok := storeStatsType.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StatsType)
		if ok {
			return storeMap[id]
		}
	}
	lockStatsType.Lock()
	defer lockStatsType.Unlock()
	store, ok = storeStatsType.Load(fitKey)
	if ok {
		tblStatsType, ok := store.(*StatsType)
		if ok {
			return tblStatsType
		}
	}
	tblStatsType := make(map[int64]*StatsType)
	stats_type_str, err := consulconfig.GetInstance().GetConfig(strStatsType, option...)
	if stats_type_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stats_type_str), &tblStatsType)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stats_type", errUnmarshal)
		return nil
	}
	storeStatsType.Store(fitKey, tblStatsType)
	return tblStatsType[id]
}

func LoadAllStatsTypeCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strStatsType, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "StatsType", successChannels)
	return nil
}
