// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type EnergyCost struct {
	Id               int64   `json:"id"`
	PitchEnergyCost  float32 `json:"pitchEnergyCost"`
	ThrowEnergyCost  float32 `json:"throwEnergyCost"`
	LiftEnergyCost   float32 `json:"liftEnergyCost"`
	ReelInEnergyCost float32 `json:"reelInEnergyCost"`
	PullEnergyCost   float32 `json:"pullEnergyCost"`
}

var lockEnergyCost sync.RWMutex
var storeEnergyCost sync.Map
var strEnergyCost string = "energy_cost"

func InitEnergyCostCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strEnergyCost, watchEnergyCostFunc)
	return LoadAllEnergyCostCfg()
}

func fixKeyEnergyCost(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strEnergyCost)
}
func watchEnergyCostFunc(key string, js string) {
	store, ok := storeEnergyCost.Load(key)
	if !ok {
		store = &EnergyCost{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeEnergyCost.Store(key, store)
}

func GetEnergyCost(option ...consulconfig.Option) *EnergyCost {
	fitKey := fixKeyEnergyCost(option...)
	store, ok := storeEnergyCost.Load(fitKey)
	if ok {
		tblEnergyCost, ok := store.(*EnergyCost)
		if ok {
			return tblEnergyCost
		}
	}
	lockEnergyCost.Lock()
	defer lockEnergyCost.Unlock()
	store, ok = storeEnergyCost.Load(fitKey)
	if ok {
		tblEnergyCost, ok := store.(*EnergyCost)
		if ok {
			return tblEnergyCost
		}
	}
	tblEnergyCost := &EnergyCost{}
	energy_cost_str, err := consulconfig.GetInstance().GetConfig(strEnergyCost, option...)
	if energy_cost_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(energy_cost_str), &tblEnergyCost)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strEnergyCost, errUnmarshal, energy_cost_str)
		return nil
	}
	storeEnergyCost.Store(fitKey, tblEnergyCost)
	return tblEnergyCost
}

func LoadAllEnergyCostCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strEnergyCost, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "EnergyCost", successChannels)
	return nil
}
