// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type LanguageAppUpdateInfo struct {
	Id                int64  `json:"id"`
	Name              string `json:"name"`
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

var lockLanguageAppUpdateInfo sync.RWMutex
var storeLanguageAppUpdateInfo sync.Map
var strLanguageAppUpdateInfo string = "language_app_update_info"

func InitLanguageAppUpdateInfoCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLanguageAppUpdateInfo, watchLanguageAppUpdateInfoFunc)
	return LoadAllLanguageAppUpdateInfoCfg()
}

func fixKeyLanguageAppUpdateInfo(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLanguageAppUpdateInfo)
}
func watchLanguageAppUpdateInfoFunc(key string, js string) {
	mapLanguageAppUpdateInfo := make(map[int64]*LanguageAppUpdateInfo)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLanguageAppUpdateInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLanguageAppUpdateInfo.Store(key, mapLanguageAppUpdateInfo)
}

func GetAllLanguageAppUpdateInfo(option ...consulconfig.Option) map[int64]*LanguageAppUpdateInfo {
	fitKey := fixKeyLanguageAppUpdateInfo(option...)
	store, ok := storeLanguageAppUpdateInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageAppUpdateInfo)
		if ok {
			return storeMap
		}
	}
	lockLanguageAppUpdateInfo.Lock()
	defer lockLanguageAppUpdateInfo.Unlock()
	store, ok = storeLanguageAppUpdateInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageAppUpdateInfo)
		if ok {
			return storeMap
		}
	}
	tblLanguageAppUpdateInfo := make(map[int64]*LanguageAppUpdateInfo)
	language_app_update_info_str, err := consulconfig.GetInstance().GetConfig(strLanguageAppUpdateInfo, option...)
	if language_app_update_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_app_update_info_str), &tblLanguageAppUpdateInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_app_update_info", errUnmarshal)
		return nil
	}
	storeLanguageAppUpdateInfo.Store(fitKey, tblLanguageAppUpdateInfo)
	return tblLanguageAppUpdateInfo
}

func GetLanguageAppUpdateInfo(id int64, option ...consulconfig.Option) *LanguageAppUpdateInfo {
	fitKey := fixKeyLanguageAppUpdateInfo(option...)
	store, ok := storeLanguageAppUpdateInfo.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageAppUpdateInfo)
		if ok {
			return storeMap[id]
		}
	}
	lockLanguageAppUpdateInfo.Lock()
	defer lockLanguageAppUpdateInfo.Unlock()
	store, ok = storeLanguageAppUpdateInfo.Load(fitKey)
	if ok {
		tblLanguageAppUpdateInfo, ok := store.(*LanguageAppUpdateInfo)
		if ok {
			return tblLanguageAppUpdateInfo
		}
	}
	tblLanguageAppUpdateInfo := make(map[int64]*LanguageAppUpdateInfo)
	language_app_update_info_str, err := consulconfig.GetInstance().GetConfig(strLanguageAppUpdateInfo, option...)
	if language_app_update_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_app_update_info_str), &tblLanguageAppUpdateInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_app_update_info", errUnmarshal)
		return nil
	}
	storeLanguageAppUpdateInfo.Store(fitKey, tblLanguageAppUpdateInfo)
	return tblLanguageAppUpdateInfo[id]
}

func LoadAllLanguageAppUpdateInfoCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strLanguageAppUpdateInfo, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "LanguageAppUpdateInfo", successChannels)
	return nil
}
