// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type LanguageSys struct {
	Id                int64  `json:"id"`
	Name              string `json:"name"`
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

var lockLanguageSys sync.RWMutex
var storeLanguageSys sync.Map
var strLanguageSys string = "language_sys"

func InitLanguageSysCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLanguageSys, watchLanguageSysFunc)
	return LoadAllLanguageSysCfg()
}

func fixKeyLanguageSys(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLanguageSys)
}
func watchLanguageSysFunc(key string, js string) {
	mapLanguageSys := make(map[int64]*LanguageSys)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLanguageSys)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLanguageSys.Store(key, mapLanguageSys)
}

func GetAllLanguageSys(option ...consulconfig.Option) map[int64]*LanguageSys {
	fitKey := fixKeyLanguageSys(option...)
	store, ok := storeLanguageSys.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageSys)
		if ok {
			return storeMap
		}
	}
	lockLanguageSys.Lock()
	defer lockLanguageSys.Unlock()
	store, ok = storeLanguageSys.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageSys)
		if ok {
			return storeMap
		}
	}
	tblLanguageSys := make(map[int64]*LanguageSys)
	language_sys_str, err := consulconfig.GetInstance().GetConfig(strLanguageSys, option...)
	if language_sys_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_sys_str), &tblLanguageSys)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_sys", errUnmarshal)
		return nil
	}
	storeLanguageSys.Store(fitKey, tblLanguageSys)
	return tblLanguageSys
}

func GetLanguageSys(id int64, option ...consulconfig.Option) *LanguageSys {
	fitKey := fixKeyLanguageSys(option...)
	store, ok := storeLanguageSys.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageSys)
		if ok {
			return storeMap[id]
		}
	}
	lockLanguageSys.Lock()
	defer lockLanguageSys.Unlock()
	store, ok = storeLanguageSys.Load(fitKey)
	if ok {
		tblLanguageSys, ok := store.(*LanguageSys)
		if ok {
			return tblLanguageSys
		}
	}
	tblLanguageSys := make(map[int64]*LanguageSys)
	language_sys_str, err := consulconfig.GetInstance().GetConfig(strLanguageSys, option...)
	if language_sys_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_sys_str), &tblLanguageSys)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_sys", errUnmarshal)
		return nil
	}
	storeLanguageSys.Store(fitKey, tblLanguageSys)
	return tblLanguageSys[id]
}

func LoadAllLanguageSysCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strLanguageSys, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "LanguageSys", successChannels)
	return nil
}
