// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishDistributeWeatherWeather struct {
	WeatherId int64 `json:"weatherId"`
	Weight    int32 `json:"weight"`
}

type FishDistributeWeather struct {
	Id      int64                          `json:"id"`
	Name    string                         `json:"name"`
	Weather []FishDistributeWeatherWeather `json:"weather"`
}

var lockFishDistributeWeather sync.RWMutex
var storeFishDistributeWeather sync.Map
var strFishDistributeWeather string = "fish_distribute_weather"

func InitFishDistributeWeatherCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishDistributeWeather, watchFishDistributeWeatherFunc)
	return LoadAllFishDistributeWeatherCfg()
}

func fixKeyFishDistributeWeather(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishDistributeWeather)
}
func watchFishDistributeWeatherFunc(key string, js string) {
	mapFishDistributeWeather := make(map[int64]*FishDistributeWeather)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFishDistributeWeather)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishDistributeWeather.Store(key, mapFishDistributeWeather)
}

func GetAllFishDistributeWeather(option ...consulconfig.Option) map[int64]*FishDistributeWeather {
	fitKey := fixKeyFishDistributeWeather(option...)
	store, ok := storeFishDistributeWeather.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeWeather)
		if ok {
			return storeMap
		}
	}
	lockFishDistributeWeather.Lock()
	defer lockFishDistributeWeather.Unlock()
	store, ok = storeFishDistributeWeather.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeWeather)
		if ok {
			return storeMap
		}
	}
	tblFishDistributeWeather := make(map[int64]*FishDistributeWeather)
	fish_distribute_weather_str, err := consulconfig.GetInstance().GetConfig(strFishDistributeWeather, option...)
	if fish_distribute_weather_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_weather_str), &tblFishDistributeWeather)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute_weather", errUnmarshal)
		return nil
	}
	storeFishDistributeWeather.Store(fitKey, tblFishDistributeWeather)
	return tblFishDistributeWeather
}

func GetFishDistributeWeather(id int64, option ...consulconfig.Option) *FishDistributeWeather {
	fitKey := fixKeyFishDistributeWeather(option...)
	store, ok := storeFishDistributeWeather.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeWeather)
		if ok {
			return storeMap[id]
		}
	}
	lockFishDistributeWeather.Lock()
	defer lockFishDistributeWeather.Unlock()
	store, ok = storeFishDistributeWeather.Load(fitKey)
	if ok {
		tblFishDistributeWeather, ok := store.(*FishDistributeWeather)
		if ok {
			return tblFishDistributeWeather
		}
	}
	tblFishDistributeWeather := make(map[int64]*FishDistributeWeather)
	fish_distribute_weather_str, err := consulconfig.GetInstance().GetConfig(strFishDistributeWeather, option...)
	if fish_distribute_weather_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_weather_str), &tblFishDistributeWeather)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute_weather", errUnmarshal)
		return nil
	}
	storeFishDistributeWeather.Store(fitKey, tblFishDistributeWeather)
	return tblFishDistributeWeather[id]
}

func LoadAllFishDistributeWeatherCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strFishDistributeWeather, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishDistributeWeather", successChannels)
	return nil
}
