// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RodHardness struct {
	Id            int32   `json:"id"`
	Desc          string  `json:"desc"`
	HardnessType  int32   `json:"hardnessType"`
	HooksetFactor float32 `json:"hooksetFactor"`
}

var lockRodHardness sync.RWMutex
var storeRodHardness sync.Map
var strRodHardness string = "rod_hardness"

func InitRodHardnessCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRodHardness, watchRodHardnessFunc)
	return LoadAllRodHardnessCfg()
}

func fixKeyRodHardness(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRodHardness)
}
func watchRodHardnessFunc(key string, js string) {
	mapRodHardness := make(map[int64]*RodHardness)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRodHardness)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRodHardness.Store(key, mapRodHardness)
}

func GetAllRodHardness(option ...consulconfig.Option) map[int64]*RodHardness {
	fitKey := fixKeyRodHardness(option...)
	store, ok := storeRodHardness.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RodHardness)
		if ok {
			return storeMap
		}
	}
	lockRodHardness.Lock()
	defer lockRodHardness.Unlock()
	store, ok = storeRodHardness.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RodHardness)
		if ok {
			return storeMap
		}
	}
	tblRodHardness := make(map[int64]*RodHardness)
	rod_hardness_str, err := consulconfig.GetInstance().GetConfig(strRodHardness, option...)
	if rod_hardness_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(rod_hardness_str), &tblRodHardness)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "rod_hardness", errUnmarshal)
		return nil
	}
	storeRodHardness.Store(fitKey, tblRodHardness)
	return tblRodHardness
}

func GetRodHardness(id int64, option ...consulconfig.Option) *RodHardness {
	fitKey := fixKeyRodHardness(option...)
	store, ok := storeRodHardness.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RodHardness)
		if ok {
			return storeMap[id]
		}
	}
	lockRodHardness.Lock()
	defer lockRodHardness.Unlock()
	store, ok = storeRodHardness.Load(fitKey)
	if ok {
		tblRodHardness, ok := store.(*RodHardness)
		if ok {
			return tblRodHardness
		}
	}
	tblRodHardness := make(map[int64]*RodHardness)
	rod_hardness_str, err := consulconfig.GetInstance().GetConfig(strRodHardness, option...)
	if rod_hardness_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(rod_hardness_str), &tblRodHardness)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "rod_hardness", errUnmarshal)
		return nil
	}
	storeRodHardness.Store(fitKey, tblRodHardness)
	return tblRodHardness[id]
}

func LoadAllRodHardnessCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strRodHardness, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RodHardness", successChannels)
	return nil
}
