// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BattlingFightRange struct {
	Min float32 `json:"min"`
	Max float32 `json:"max"`
}

type BattlingFight struct {
	Id                  int32              `json:"id"`
	Type                int32              `json:"type"`
	Range               BattlingFightRange `json:"range"`
	BattlingFightFactor float32            `json:"battlingFightFactor"`
}

var lockBattlingFight sync.RWMutex
var storeBattlingFight sync.Map
var strBattlingFight string = "battling_fight"

func InitBattlingFightCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBattlingFight, watchBattlingFightFunc)
	return LoadAllBattlingFightCfg()
}

func fixKeyBattlingFight(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBattlingFight)
}
func watchBattlingFightFunc(key string, js string) {
	mapBattlingFight := make(map[int64]*BattlingFight)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBattlingFight)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBattlingFight.Store(key, mapBattlingFight)
}

func GetAllBattlingFight(option ...consulconfig.Option) map[int64]*BattlingFight {
	fitKey := fixKeyBattlingFight(option...)
	store, ok := storeBattlingFight.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BattlingFight)
		if ok {
			return storeMap
		}
	}
	lockBattlingFight.Lock()
	defer lockBattlingFight.Unlock()
	store, ok = storeBattlingFight.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BattlingFight)
		if ok {
			return storeMap
		}
	}
	tblBattlingFight := make(map[int64]*BattlingFight)
	battling_fight_str, err := consulconfig.GetInstance().GetConfig(strBattlingFight, option...)
	if battling_fight_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(battling_fight_str), &tblBattlingFight)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "battling_fight", errUnmarshal)
		return nil
	}
	storeBattlingFight.Store(fitKey, tblBattlingFight)
	return tblBattlingFight
}

func GetBattlingFight(id int64, option ...consulconfig.Option) *BattlingFight {
	fitKey := fixKeyBattlingFight(option...)
	store, ok := storeBattlingFight.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BattlingFight)
		if ok {
			return storeMap[id]
		}
	}
	lockBattlingFight.Lock()
	defer lockBattlingFight.Unlock()
	store, ok = storeBattlingFight.Load(fitKey)
	if ok {
		tblBattlingFight, ok := store.(*BattlingFight)
		if ok {
			return tblBattlingFight
		}
	}
	tblBattlingFight := make(map[int64]*BattlingFight)
	battling_fight_str, err := consulconfig.GetInstance().GetConfig(strBattlingFight, option...)
	if battling_fight_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(battling_fight_str), &tblBattlingFight)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "battling_fight", errUnmarshal)
		return nil
	}
	storeBattlingFight.Store(fitKey, tblBattlingFight)
	return tblBattlingFight[id]
}

func LoadAllBattlingFightCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBattlingFight, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BattlingFight", successChannels)
	return nil
}
