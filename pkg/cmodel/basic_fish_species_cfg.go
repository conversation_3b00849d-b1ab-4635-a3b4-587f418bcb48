// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BasicFishSpecies struct {
	Id             int64   `json:"id"`
	Name           string  `json:"name"`
	Genus          int64   `json:"genus"`
	ScientificName string  `json:"scientificName"`
	ShowName       string  `json:"showName"`
	ResourceId     string  `json:"resourceId"`
	Legend         int32   `json:"legend"`
	Character      int32   `json:"character"`
	SpeedBlSlope   float32 `json:"speedBlSlope"`
	SpeedIntercept float32 `json:"speedIntercept"`
}

var lockBasicFishSpecies sync.RWMutex
var storeBasicFishSpecies sync.Map
var strBasicFishSpecies string = "basic_fish_species"

func InitBasicFishSpeciesCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBasicFishSpecies, watchBasicFishSpeciesFunc)
	return LoadAllBasicFishSpeciesCfg()
}

func fixKeyBasicFishSpecies(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBasicFishSpecies)
}
func watchBasicFishSpeciesFunc(key string, js string) {
	mapBasicFishSpecies := make(map[int64]*BasicFishSpecies)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBasicFishSpecies)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBasicFishSpecies.Store(key, mapBasicFishSpecies)
}

func GetAllBasicFishSpecies(option ...consulconfig.Option) map[int64]*BasicFishSpecies {
	fitKey := fixKeyBasicFishSpecies(option...)
	store, ok := storeBasicFishSpecies.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishSpecies)
		if ok {
			return storeMap
		}
	}
	lockBasicFishSpecies.Lock()
	defer lockBasicFishSpecies.Unlock()
	store, ok = storeBasicFishSpecies.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishSpecies)
		if ok {
			return storeMap
		}
	}
	tblBasicFishSpecies := make(map[int64]*BasicFishSpecies)
	basic_fish_species_str, err := consulconfig.GetInstance().GetConfig(strBasicFishSpecies, option...)
	if basic_fish_species_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_species_str), &tblBasicFishSpecies)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_species", errUnmarshal)
		return nil
	}
	storeBasicFishSpecies.Store(fitKey, tblBasicFishSpecies)
	return tblBasicFishSpecies
}

func GetBasicFishSpecies(id int64, option ...consulconfig.Option) *BasicFishSpecies {
	fitKey := fixKeyBasicFishSpecies(option...)
	store, ok := storeBasicFishSpecies.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishSpecies)
		if ok {
			return storeMap[id]
		}
	}
	lockBasicFishSpecies.Lock()
	defer lockBasicFishSpecies.Unlock()
	store, ok = storeBasicFishSpecies.Load(fitKey)
	if ok {
		tblBasicFishSpecies, ok := store.(*BasicFishSpecies)
		if ok {
			return tblBasicFishSpecies
		}
	}
	tblBasicFishSpecies := make(map[int64]*BasicFishSpecies)
	basic_fish_species_str, err := consulconfig.GetInstance().GetConfig(strBasicFishSpecies, option...)
	if basic_fish_species_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_species_str), &tblBasicFishSpecies)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_species", errUnmarshal)
		return nil
	}
	storeBasicFishSpecies.Store(fitKey, tblBasicFishSpecies)
	return tblBasicFishSpecies[id]
}

func LoadAllBasicFishSpeciesCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBasicFishSpecies, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BasicFishSpecies", successChannels)
	return nil
}
