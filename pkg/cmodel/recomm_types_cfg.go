// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RecommTypes struct {
	Id            int64   `json:"id"`
	Name          string  `json:"name"`
	RecommStructs []int64 `json:"recommStructs"`
	RecommBaits   []int64 `json:"recommBaits"`
	Mark          string  `json:"mark"`
}

var lockRecommTypes sync.RWMutex
var storeRecommTypes sync.Map
var strRecommTypes string = "recomm_types"

func InitRecommTypesCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRecommTypes, watchRecommTypesFunc)
	return LoadAllRecommTypesCfg()
}

func fixKeyRecommTypes(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRecommTypes)
}
func watchRecommTypesFunc(key string, js string) {
	mapRecommTypes := make(map[int64]*RecommTypes)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRecommTypes)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRecommTypes.Store(key, mapRecommTypes)
}

func GetAllRecommTypes(option ...consulconfig.Option) map[int64]*RecommTypes {
	fitKey := fixKeyRecommTypes(option...)
	store, ok := storeRecommTypes.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RecommTypes)
		if ok {
			return storeMap
		}
	}
	lockRecommTypes.Lock()
	defer lockRecommTypes.Unlock()
	store, ok = storeRecommTypes.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RecommTypes)
		if ok {
			return storeMap
		}
	}
	tblRecommTypes := make(map[int64]*RecommTypes)
	recomm_types_str, err := consulconfig.GetInstance().GetConfig(strRecommTypes, option...)
	if recomm_types_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(recomm_types_str), &tblRecommTypes)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "recomm_types", errUnmarshal)
		return nil
	}
	storeRecommTypes.Store(fitKey, tblRecommTypes)
	return tblRecommTypes
}

func GetRecommTypes(id int64, option ...consulconfig.Option) *RecommTypes {
	fitKey := fixKeyRecommTypes(option...)
	store, ok := storeRecommTypes.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RecommTypes)
		if ok {
			return storeMap[id]
		}
	}
	lockRecommTypes.Lock()
	defer lockRecommTypes.Unlock()
	store, ok = storeRecommTypes.Load(fitKey)
	if ok {
		tblRecommTypes, ok := store.(*RecommTypes)
		if ok {
			return tblRecommTypes
		}
	}
	tblRecommTypes := make(map[int64]*RecommTypes)
	recomm_types_str, err := consulconfig.GetInstance().GetConfig(strRecommTypes, option...)
	if recomm_types_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(recomm_types_str), &tblRecommTypes)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "recomm_types", errUnmarshal)
		return nil
	}
	storeRecommTypes.Store(fitKey, tblRecommTypes)
	return tblRecommTypes[id]
}

func LoadAllRecommTypesCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strRecommTypes, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RecommTypes", successChannels)
	return nil
}
