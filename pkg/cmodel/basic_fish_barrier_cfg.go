// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BasicFishBarrier struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	BarrierType int32  `json:"barrierType"`
	Description string `json:"description"`
}

var lockBasicFishBarrier sync.RWMutex
var storeBasicFishBarrier sync.Map
var strBasicFishBarrier string = "basic_fish_barrier"

func InitBasicFishBarrierCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBasicFishBarrier, watchBasicFishBarrierFunc)
	return LoadAllBasicFishBarrierCfg()
}

func fixKeyBasicFishBarrier(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBasicFishBarrier)
}
func watchBasicFishBarrierFunc(key string, js string) {
	mapBasicFishBarrier := make(map[int64]*BasicFishBarrier)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBasicFishBarrier)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBasicFishBarrier.Store(key, mapBasicFishBarrier)
}

func GetAllBasicFishBarrier(option ...consulconfig.Option) map[int64]*BasicFishBarrier {
	fitKey := fixKeyBasicFishBarrier(option...)
	store, ok := storeBasicFishBarrier.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishBarrier)
		if ok {
			return storeMap
		}
	}
	lockBasicFishBarrier.Lock()
	defer lockBasicFishBarrier.Unlock()
	store, ok = storeBasicFishBarrier.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishBarrier)
		if ok {
			return storeMap
		}
	}
	tblBasicFishBarrier := make(map[int64]*BasicFishBarrier)
	basic_fish_barrier_str, err := consulconfig.GetInstance().GetConfig(strBasicFishBarrier, option...)
	if basic_fish_barrier_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_barrier_str), &tblBasicFishBarrier)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_barrier", errUnmarshal)
		return nil
	}
	storeBasicFishBarrier.Store(fitKey, tblBasicFishBarrier)
	return tblBasicFishBarrier
}

func GetBasicFishBarrier(id int64, option ...consulconfig.Option) *BasicFishBarrier {
	fitKey := fixKeyBasicFishBarrier(option...)
	store, ok := storeBasicFishBarrier.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishBarrier)
		if ok {
			return storeMap[id]
		}
	}
	lockBasicFishBarrier.Lock()
	defer lockBasicFishBarrier.Unlock()
	store, ok = storeBasicFishBarrier.Load(fitKey)
	if ok {
		tblBasicFishBarrier, ok := store.(*BasicFishBarrier)
		if ok {
			return tblBasicFishBarrier
		}
	}
	tblBasicFishBarrier := make(map[int64]*BasicFishBarrier)
	basic_fish_barrier_str, err := consulconfig.GetInstance().GetConfig(strBasicFishBarrier, option...)
	if basic_fish_barrier_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_barrier_str), &tblBasicFishBarrier)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_barrier", errUnmarshal)
		return nil
	}
	storeBasicFishBarrier.Store(fitKey, tblBasicFishBarrier)
	return tblBasicFishBarrier[id]
}

func LoadAllBasicFishBarrierCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBasicFishBarrier, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BasicFishBarrier", successChannels)
	return nil
}
