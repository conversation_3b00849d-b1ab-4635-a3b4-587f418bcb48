// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Ann<PERSON><PERSON><PERSON> struct {
	Id         int64  `json:"id"`
	OpenSurvey bool   `json:"openSurvey"`
	Url        string `json:"url"`
}

var lockAnnSurvey sync.RWMutex
var storeAnnSurvey sync.Map
var strAnnSurvey string = "ann_survey"

func InitAnnSurveyCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strAnnSurvey, watchAnnSurveyFunc)
	return LoadAllAnnSurveyCfg()
}

func fixKeyAnnSurvey(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strAnnSurvey)
}
func watchAnnSurveyFunc(key string, js string) {
	store, ok := storeAnnSurvey.Load(key)
	if !ok {
		store = &AnnSurvey{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeAnnSurvey.Store(key, store)
}

func GetAnnSurvey(option ...consulconfig.Option) *AnnSurvey {
	fitKey := fixKeyAnnSurvey(option...)
	store, ok := storeAnnSurvey.Load(fitKey)
	if ok {
		tblAnnSurvey, ok := store.(*AnnSurvey)
		if ok {
			return tblAnnSurvey
		}
	}
	lockAnnSurvey.Lock()
	defer lockAnnSurvey.Unlock()
	store, ok = storeAnnSurvey.Load(fitKey)
	if ok {
		tblAnnSurvey, ok := store.(*AnnSurvey)
		if ok {
			return tblAnnSurvey
		}
	}
	tblAnnSurvey := &AnnSurvey{}
	ann_survey_str, err := consulconfig.GetInstance().GetConfig(strAnnSurvey, option...)
	if ann_survey_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(ann_survey_str), &tblAnnSurvey)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strAnnSurvey, errUnmarshal, ann_survey_str)
		return nil
	}
	storeAnnSurvey.Store(fitKey, tblAnnSurvey)
	return tblAnnSurvey
}

func LoadAllAnnSurveyCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strAnnSurvey, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "AnnSurvey", successChannels)
	return nil
}
