// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type LocationBlock struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	Country string `json:"country"`
	City    string `json:"city"`
	Mark    string `json:"mark"`
}

var lockLocationBlock sync.RWMutex
var storeLocationBlock sync.Map
var strLocationBlock string = "location_block"

func InitLocationBlockCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLocationBlock, watchLocationBlockFunc)
	return LoadAllLocationBlockCfg()
}

func fixKeyLocationBlock(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLocationBlock)
}
func watchLocationBlockFunc(key string, js string) {
	mapLocationBlock := make(map[int64]*LocationBlock)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLocationBlock)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLocationBlock.Store(key, mapLocationBlock)
}

func GetAllLocationBlock(option ...consulconfig.Option) map[int64]*LocationBlock {
	fitKey := fixKeyLocationBlock(option...)
	store, ok := storeLocationBlock.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LocationBlock)
		if ok {
			return storeMap
		}
	}
	lockLocationBlock.Lock()
	defer lockLocationBlock.Unlock()
	store, ok = storeLocationBlock.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LocationBlock)
		if ok {
			return storeMap
		}
	}
	tblLocationBlock := make(map[int64]*LocationBlock)
	location_block_str, err := consulconfig.GetInstance().GetConfig(strLocationBlock, option...)
	if location_block_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(location_block_str), &tblLocationBlock)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "location_block", errUnmarshal)
		return nil
	}
	storeLocationBlock.Store(fitKey, tblLocationBlock)
	return tblLocationBlock
}

func GetLocationBlock(id int64, option ...consulconfig.Option) *LocationBlock {
	fitKey := fixKeyLocationBlock(option...)
	store, ok := storeLocationBlock.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LocationBlock)
		if ok {
			return storeMap[id]
		}
	}
	lockLocationBlock.Lock()
	defer lockLocationBlock.Unlock()
	store, ok = storeLocationBlock.Load(fitKey)
	if ok {
		tblLocationBlock, ok := store.(*LocationBlock)
		if ok {
			return tblLocationBlock
		}
	}
	tblLocationBlock := make(map[int64]*LocationBlock)
	location_block_str, err := consulconfig.GetInstance().GetConfig(strLocationBlock, option...)
	if location_block_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(location_block_str), &tblLocationBlock)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "location_block", errUnmarshal)
		return nil
	}
	storeLocationBlock.Store(fitKey, tblLocationBlock)
	return tblLocationBlock[id]
}

func LoadAllLocationBlockCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strLocationBlock, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "LocationBlock", successChannels)
	return nil
}
