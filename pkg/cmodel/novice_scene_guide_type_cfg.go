// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type NoviceSceneGuideType struct {
	Id   int64  `json:"id"`
	Mark string `json:"mark"`
	Name string `json:"name"`
}

var lockNoviceSceneGuideType sync.RWMutex
var storeNoviceSceneGuideType sync.Map
var strNoviceSceneGuideType string = "novice_scene_guide_type"

func InitNoviceSceneGuideTypeCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strNoviceSceneGuideType, watchNoviceSceneGuideTypeFunc)
	return LoadAllNoviceSceneGuideTypeCfg()
}

func fixKeyNoviceSceneGuideType(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strNoviceSceneGuideType)
}
func watchNoviceSceneGuideTypeFunc(key string, js string) {
	mapNoviceSceneGuideType := make(map[int64]*NoviceSceneGuideType)
	errUnmarshal := json.Unmarshal([]byte(js), &mapNoviceSceneGuideType)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeNoviceSceneGuideType.Store(key, mapNoviceSceneGuideType)
}

func GetAllNoviceSceneGuideType(option ...consulconfig.Option) map[int64]*NoviceSceneGuideType {
	fitKey := fixKeyNoviceSceneGuideType(option...)
	store, ok := storeNoviceSceneGuideType.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*NoviceSceneGuideType)
		if ok {
			return storeMap
		}
	}
	lockNoviceSceneGuideType.Lock()
	defer lockNoviceSceneGuideType.Unlock()
	store, ok = storeNoviceSceneGuideType.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*NoviceSceneGuideType)
		if ok {
			return storeMap
		}
	}
	tblNoviceSceneGuideType := make(map[int64]*NoviceSceneGuideType)
	novice_scene_guide_type_str, err := consulconfig.GetInstance().GetConfig(strNoviceSceneGuideType, option...)
	if novice_scene_guide_type_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(novice_scene_guide_type_str), &tblNoviceSceneGuideType)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "novice_scene_guide_type", errUnmarshal)
		return nil
	}
	storeNoviceSceneGuideType.Store(fitKey, tblNoviceSceneGuideType)
	return tblNoviceSceneGuideType
}

func GetNoviceSceneGuideType(id int64, option ...consulconfig.Option) *NoviceSceneGuideType {
	fitKey := fixKeyNoviceSceneGuideType(option...)
	store, ok := storeNoviceSceneGuideType.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*NoviceSceneGuideType)
		if ok {
			return storeMap[id]
		}
	}
	lockNoviceSceneGuideType.Lock()
	defer lockNoviceSceneGuideType.Unlock()
	store, ok = storeNoviceSceneGuideType.Load(fitKey)
	if ok {
		tblNoviceSceneGuideType, ok := store.(*NoviceSceneGuideType)
		if ok {
			return tblNoviceSceneGuideType
		}
	}
	tblNoviceSceneGuideType := make(map[int64]*NoviceSceneGuideType)
	novice_scene_guide_type_str, err := consulconfig.GetInstance().GetConfig(strNoviceSceneGuideType, option...)
	if novice_scene_guide_type_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(novice_scene_guide_type_str), &tblNoviceSceneGuideType)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "novice_scene_guide_type", errUnmarshal)
		return nil
	}
	storeNoviceSceneGuideType.Store(fitKey, tblNoviceSceneGuideType)
	return tblNoviceSceneGuideType[id]
}

func LoadAllNoviceSceneGuideTypeCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strNoviceSceneGuideType, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "NoviceSceneGuideType", successChannels)
	return nil
}
