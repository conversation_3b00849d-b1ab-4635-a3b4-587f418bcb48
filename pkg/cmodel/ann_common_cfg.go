// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type AnnCommon struct {
	Id        int64  `json:"id"`
	Name      string `json:"name"`
	Enable    bool   `json:"enable"`
	Tag       int64  `json:"tag"`
	Priority  int64  `json:"priority"`
	JumpType  int64  `json:"jumpType"`
	JumpArgs  string `json:"jumpArgs"`
	Title     string `json:"title"`
	ImageName string `json:"imageName"`
	Text      string `json:"text"`
	StartTime int64  `json:"startTime"`
	EndTime   int64  `json:"endTime"`
}

var lockAnnCommon sync.RWMutex
var storeAnnCommon sync.Map
var strAnnCommon string = "ann_common"

func InitAnnCommonCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strAnnCommon, watchAnnCommonFunc)
	return LoadAllAnnCommonCfg()
}

func fixKeyAnnCommon(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strAnnCommon)
}
func watchAnnCommonFunc(key string, js string) {
	mapAnnCommon := make(map[int64]*AnnCommon)
	errUnmarshal := json.Unmarshal([]byte(js), &mapAnnCommon)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeAnnCommon.Store(key, mapAnnCommon)
}

func GetAllAnnCommon(option ...consulconfig.Option) map[int64]*AnnCommon {
	fitKey := fixKeyAnnCommon(option...)
	store, ok := storeAnnCommon.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*AnnCommon)
		if ok {
			return storeMap
		}
	}
	lockAnnCommon.Lock()
	defer lockAnnCommon.Unlock()
	store, ok = storeAnnCommon.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*AnnCommon)
		if ok {
			return storeMap
		}
	}
	tblAnnCommon := make(map[int64]*AnnCommon)
	ann_common_str, err := consulconfig.GetInstance().GetConfig(strAnnCommon, option...)
	if ann_common_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(ann_common_str), &tblAnnCommon)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "ann_common", errUnmarshal)
		return nil
	}
	storeAnnCommon.Store(fitKey, tblAnnCommon)
	return tblAnnCommon
}

func GetAnnCommon(id int64, option ...consulconfig.Option) *AnnCommon {
	fitKey := fixKeyAnnCommon(option...)
	store, ok := storeAnnCommon.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*AnnCommon)
		if ok {
			return storeMap[id]
		}
	}
	lockAnnCommon.Lock()
	defer lockAnnCommon.Unlock()
	store, ok = storeAnnCommon.Load(fitKey)
	if ok {
		tblAnnCommon, ok := store.(*AnnCommon)
		if ok {
			return tblAnnCommon
		}
	}
	tblAnnCommon := make(map[int64]*AnnCommon)
	ann_common_str, err := consulconfig.GetInstance().GetConfig(strAnnCommon, option...)
	if ann_common_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(ann_common_str), &tblAnnCommon)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "ann_common", errUnmarshal)
		return nil
	}
	storeAnnCommon.Store(fitKey, tblAnnCommon)
	return tblAnnCommon[id]
}

func LoadAllAnnCommonCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strAnnCommon, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "AnnCommon", successChannels)
	return nil
}
