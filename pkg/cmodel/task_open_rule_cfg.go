// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type TaskOpenRule struct {
	Id       int64  `json:"id"`
	Name     string `json:"name"`
	Mark     string `json:"mark"`
	Language int64  `json:"language"`
	Label    int32  `json:"label"`
	Operate  int32  `json:"operate"`
}

var lockTaskOpenRule sync.RWMutex
var storeTaskOpenRule sync.Map
var strTaskOpenRule string = "task_open_rule"

func InitTaskOpenRuleCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strTaskOpenRule, watchTaskOpenRuleFunc)
	return LoadAllTaskOpenRuleCfg()
}

func fixKeyTaskOpenRule(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strTaskOpenRule)
}
func watchTaskOpenRuleFunc(key string, js string) {
	mapTaskOpenRule := make(map[int64]*TaskOpenRule)
	errUnmarshal := json.Unmarshal([]byte(js), &mapTaskOpenRule)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeTaskOpenRule.Store(key, mapTaskOpenRule)
}

func GetAllTaskOpenRule(option ...consulconfig.Option) map[int64]*TaskOpenRule {
	fitKey := fixKeyTaskOpenRule(option...)
	store, ok := storeTaskOpenRule.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskOpenRule)
		if ok {
			return storeMap
		}
	}
	lockTaskOpenRule.Lock()
	defer lockTaskOpenRule.Unlock()
	store, ok = storeTaskOpenRule.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskOpenRule)
		if ok {
			return storeMap
		}
	}
	tblTaskOpenRule := make(map[int64]*TaskOpenRule)
	task_open_rule_str, err := consulconfig.GetInstance().GetConfig(strTaskOpenRule, option...)
	if task_open_rule_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_open_rule_str), &tblTaskOpenRule)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_open_rule", errUnmarshal)
		return nil
	}
	storeTaskOpenRule.Store(fitKey, tblTaskOpenRule)
	return tblTaskOpenRule
}

func GetTaskOpenRule(id int64, option ...consulconfig.Option) *TaskOpenRule {
	fitKey := fixKeyTaskOpenRule(option...)
	store, ok := storeTaskOpenRule.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskOpenRule)
		if ok {
			return storeMap[id]
		}
	}
	lockTaskOpenRule.Lock()
	defer lockTaskOpenRule.Unlock()
	store, ok = storeTaskOpenRule.Load(fitKey)
	if ok {
		tblTaskOpenRule, ok := store.(*TaskOpenRule)
		if ok {
			return tblTaskOpenRule
		}
	}
	tblTaskOpenRule := make(map[int64]*TaskOpenRule)
	task_open_rule_str, err := consulconfig.GetInstance().GetConfig(strTaskOpenRule, option...)
	if task_open_rule_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_open_rule_str), &tblTaskOpenRule)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_open_rule", errUnmarshal)
		return nil
	}
	storeTaskOpenRule.Store(fitKey, tblTaskOpenRule)
	return tblTaskOpenRule[id]
}

func LoadAllTaskOpenRuleCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strTaskOpenRule, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "TaskOpenRule", successChannels)
	return nil
}
