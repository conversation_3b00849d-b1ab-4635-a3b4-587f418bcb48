// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishDamagedLevel struct {
	Type                   int32   `json:"type"`
	FishDamagedValueFactor float32 `json:"fishDamagedValueFactor"`
}

var lockFishDamagedLevel sync.RWMutex
var storeFishDamagedLevel sync.Map
var strFishDamagedLevel string = "fish_damaged_level"

func InitFishDamagedLevelCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishDamagedLevel, watchFishDamagedLevelFunc)
	return LoadAllFishDamagedLevelCfg()
}

func fixKeyFishDamagedLevel(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishDamagedLevel)
}
func watchFishDamagedLevelFunc(key string, js string) {
	mapFishDamagedLevel := make(map[int64]*FishDamagedLevel)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFishDamagedLevel)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishDamagedLevel.Store(key, mapFishDamagedLevel)
}

func GetAllFishDamagedLevel(option ...consulconfig.Option) map[int64]*FishDamagedLevel {
	fitKey := fixKeyFishDamagedLevel(option...)
	store, ok := storeFishDamagedLevel.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDamagedLevel)
		if ok {
			return storeMap
		}
	}
	lockFishDamagedLevel.Lock()
	defer lockFishDamagedLevel.Unlock()
	store, ok = storeFishDamagedLevel.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDamagedLevel)
		if ok {
			return storeMap
		}
	}
	tblFishDamagedLevel := make(map[int64]*FishDamagedLevel)
	fish_damaged_level_str, err := consulconfig.GetInstance().GetConfig(strFishDamagedLevel, option...)
	if fish_damaged_level_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_damaged_level_str), &tblFishDamagedLevel)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_damaged_level", errUnmarshal)
		return nil
	}
	storeFishDamagedLevel.Store(fitKey, tblFishDamagedLevel)
	return tblFishDamagedLevel
}

func GetFishDamagedLevel(id int64, option ...consulconfig.Option) *FishDamagedLevel {
	fitKey := fixKeyFishDamagedLevel(option...)
	store, ok := storeFishDamagedLevel.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDamagedLevel)
		if ok {
			return storeMap[id]
		}
	}
	lockFishDamagedLevel.Lock()
	defer lockFishDamagedLevel.Unlock()
	store, ok = storeFishDamagedLevel.Load(fitKey)
	if ok {
		tblFishDamagedLevel, ok := store.(*FishDamagedLevel)
		if ok {
			return tblFishDamagedLevel
		}
	}
	tblFishDamagedLevel := make(map[int64]*FishDamagedLevel)
	fish_damaged_level_str, err := consulconfig.GetInstance().GetConfig(strFishDamagedLevel, option...)
	if fish_damaged_level_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_damaged_level_str), &tblFishDamagedLevel)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_damaged_level", errUnmarshal)
		return nil
	}
	storeFishDamagedLevel.Store(fitKey, tblFishDamagedLevel)
	return tblFishDamagedLevel[id]
}

func LoadAllFishDamagedLevelCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strFishDamagedLevel, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishDamagedLevel", successChannels)
	return nil
}
