// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RealNameAuth struct {
	Enable                   bool    `json:"enable"`
	RuleType                 int64   `json:"ruleType"`
	Appid                    string  `json:"appid"`
	Bizid                    string  `json:"bizid"`
	SecretKey                string  `json:"secretKey"`
	MinorTime                int64   `json:"minorTime"`
	MinorTimeRange           string  `json:"minorTimeRange"`
	MajorTipsTime            int64   `json:"majorTipsTime"`
	MajorForceTime           int64   `json:"majorForceTime"`
	ShumaiAppCode            string  `json:"shumaiAppCode"`
	ShumaiHolidayEnable      bool    `json:"shumaiHolidayEnable"`
	RechargeUnallowedAgeType []int32 `json:"rechargeUnallowedAgeType"`
	RechargeLimitAgeType     []int32 `json:"rechargeLimitAgeType"`
}

var lockRealNameAuth sync.RWMutex
var storeRealNameAuth sync.Map
var strRealNameAuth string = "real_name_auth"

func InitRealNameAuthCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRealNameAuth, watchRealNameAuthFunc)
	return LoadAllRealNameAuthCfg()
}

func fixKeyRealNameAuth(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRealNameAuth)
}
func watchRealNameAuthFunc(key string, js string) {
	store, ok := storeRealNameAuth.Load(key)
	if !ok {
		store = &RealNameAuth{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRealNameAuth.Store(key, store)
}

func GetRealNameAuth(option ...consulconfig.Option) *RealNameAuth {
	fitKey := fixKeyRealNameAuth(option...)
	store, ok := storeRealNameAuth.Load(fitKey)
	if ok {
		tblRealNameAuth, ok := store.(*RealNameAuth)
		if ok {
			return tblRealNameAuth
		}
	}
	lockRealNameAuth.Lock()
	defer lockRealNameAuth.Unlock()
	store, ok = storeRealNameAuth.Load(fitKey)
	if ok {
		tblRealNameAuth, ok := store.(*RealNameAuth)
		if ok {
			return tblRealNameAuth
		}
	}
	tblRealNameAuth := &RealNameAuth{}
	real_name_auth_str, err := consulconfig.GetInstance().GetConfig(strRealNameAuth, option...)
	if real_name_auth_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(real_name_auth_str), &tblRealNameAuth)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strRealNameAuth, errUnmarshal, real_name_auth_str)
		return nil
	}
	storeRealNameAuth.Store(fitKey, tblRealNameAuth)
	return tblRealNameAuth
}

func LoadAllRealNameAuthCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strRealNameAuth, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RealNameAuth", successChannels)
	return nil
}
