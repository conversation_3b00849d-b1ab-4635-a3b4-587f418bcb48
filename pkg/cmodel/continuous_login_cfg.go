// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type ContinuousLoginAward struct {
	ItemId  int64 `json:"itemId"`
	ItemVal int64 `json:"itemVal"`
}

type ContinuousLogin struct {
	Id              int64                  `json:"id"`
	Name            string                 `json:"name"`
	Day             int32                  `json:"day"`
	IsSpecialReward bool                   `json:"isSpecialReward"`
	Desc            string                 `json:"desc"`
	Award           []ContinuousLoginAward `json:"award"`
}

var lockContinuousLogin sync.RWMutex
var storeContinuousLogin sync.Map
var strContinuousLogin string = "continuous_login"

func InitContinuousLoginCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strContinuousLogin, watchContinuousLoginFunc)
	return LoadAllContinuousLoginCfg()
}

func fixKeyContinuousLogin(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strContinuousLogin)
}
func watchContinuousLoginFunc(key string, js string) {
	mapContinuousLogin := make(map[int64]*ContinuousLogin)
	errUnmarshal := json.Unmarshal([]byte(js), &mapContinuousLogin)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeContinuousLogin.Store(key, mapContinuousLogin)
}

func GetAllContinuousLogin(option ...consulconfig.Option) map[int64]*ContinuousLogin {
	fitKey := fixKeyContinuousLogin(option...)
	store, ok := storeContinuousLogin.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ContinuousLogin)
		if ok {
			return storeMap
		}
	}
	lockContinuousLogin.Lock()
	defer lockContinuousLogin.Unlock()
	store, ok = storeContinuousLogin.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ContinuousLogin)
		if ok {
			return storeMap
		}
	}
	tblContinuousLogin := make(map[int64]*ContinuousLogin)
	continuous_login_str, err := consulconfig.GetInstance().GetConfig(strContinuousLogin, option...)
	if continuous_login_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(continuous_login_str), &tblContinuousLogin)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "continuous_login", errUnmarshal)
		return nil
	}
	storeContinuousLogin.Store(fitKey, tblContinuousLogin)
	return tblContinuousLogin
}

func GetContinuousLogin(id int64, option ...consulconfig.Option) *ContinuousLogin {
	fitKey := fixKeyContinuousLogin(option...)
	store, ok := storeContinuousLogin.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ContinuousLogin)
		if ok {
			return storeMap[id]
		}
	}
	lockContinuousLogin.Lock()
	defer lockContinuousLogin.Unlock()
	store, ok = storeContinuousLogin.Load(fitKey)
	if ok {
		tblContinuousLogin, ok := store.(*ContinuousLogin)
		if ok {
			return tblContinuousLogin
		}
	}
	tblContinuousLogin := make(map[int64]*ContinuousLogin)
	continuous_login_str, err := consulconfig.GetInstance().GetConfig(strContinuousLogin, option...)
	if continuous_login_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(continuous_login_str), &tblContinuousLogin)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "continuous_login", errUnmarshal)
		return nil
	}
	storeContinuousLogin.Store(fitKey, tblContinuousLogin)
	return tblContinuousLogin[id]
}

func LoadAllContinuousLoginCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strContinuousLogin, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "ContinuousLogin", successChannels)
	return nil
}
