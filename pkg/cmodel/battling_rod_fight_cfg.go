// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BattlingRodFight struct {
	Type                      int32   `json:"type"`
	RodHardnessBattlingFactor float32 `json:"rodHardnessBattlingFactor"`
}

var lockBattlingRodFight sync.RWMutex
var storeBattlingRodFight sync.Map
var strBattlingRodFight string = "battling_rod_fight"

func InitBattlingRodFightCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBattlingRodFight, watchBattlingRodFightFunc)
	return LoadAllBattlingRodFightCfg()
}

func fixKeyBattlingRodFight(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBattlingRodFight)
}
func watchBattlingRodFightFunc(key string, js string) {
	mapBattlingRodFight := make(map[int64]*BattlingRodFight)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBattlingRodFight)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBattlingRodFight.Store(key, mapBattlingRodFight)
}

func GetAllBattlingRodFight(option ...consulconfig.Option) map[int64]*BattlingRodFight {
	fitKey := fixKeyBattlingRodFight(option...)
	store, ok := storeBattlingRodFight.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BattlingRodFight)
		if ok {
			return storeMap
		}
	}
	lockBattlingRodFight.Lock()
	defer lockBattlingRodFight.Unlock()
	store, ok = storeBattlingRodFight.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BattlingRodFight)
		if ok {
			return storeMap
		}
	}
	tblBattlingRodFight := make(map[int64]*BattlingRodFight)
	battling_rod_fight_str, err := consulconfig.GetInstance().GetConfig(strBattlingRodFight, option...)
	if battling_rod_fight_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(battling_rod_fight_str), &tblBattlingRodFight)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "battling_rod_fight", errUnmarshal)
		return nil
	}
	storeBattlingRodFight.Store(fitKey, tblBattlingRodFight)
	return tblBattlingRodFight
}

func GetBattlingRodFight(id int64, option ...consulconfig.Option) *BattlingRodFight {
	fitKey := fixKeyBattlingRodFight(option...)
	store, ok := storeBattlingRodFight.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BattlingRodFight)
		if ok {
			return storeMap[id]
		}
	}
	lockBattlingRodFight.Lock()
	defer lockBattlingRodFight.Unlock()
	store, ok = storeBattlingRodFight.Load(fitKey)
	if ok {
		tblBattlingRodFight, ok := store.(*BattlingRodFight)
		if ok {
			return tblBattlingRodFight
		}
	}
	tblBattlingRodFight := make(map[int64]*BattlingRodFight)
	battling_rod_fight_str, err := consulconfig.GetInstance().GetConfig(strBattlingRodFight, option...)
	if battling_rod_fight_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(battling_rod_fight_str), &tblBattlingRodFight)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "battling_rod_fight", errUnmarshal)
		return nil
	}
	storeBattlingRodFight.Store(fitKey, tblBattlingRodFight)
	return tblBattlingRodFight[id]
}

func LoadAllBattlingRodFightCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBattlingRodFight, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BattlingRodFight", successChannels)
	return nil
}
