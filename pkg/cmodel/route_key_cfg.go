// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RouteKey struct {
	Id         int64  `json:"id"`
	Name       string `json:"name"`
	NeedAuth   bool   `json:"needAuth"`
	GrayEnable bool   `json:"grayEnable"`
	Enable     bool   `json:"enable"`
	Version    string `json:"version"`
	Demotion   int64  `json:"demotion"`
}

var lockRouteKey sync.RWMutex
var storeRouteKey sync.Map
var strRouteKey string = "route_key"

func InitRouteKeyCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRouteKey, watchRouteKeyFunc)
	return LoadAllRouteKeyCfg()
}

func fixKeyRouteKey(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRouteKey)
}
func watchRouteKeyFunc(key string, js string) {
	mapRouteKey := make(map[int64]*RouteKey)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRouteKey)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRouteKey.Store(key, mapRouteKey)
}

func GetAllRouteKey(option ...consulconfig.Option) map[int64]*RouteKey {
	fitKey := fixKeyRouteKey(option...)
	store, ok := storeRouteKey.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RouteKey)
		if ok {
			return storeMap
		}
	}
	lockRouteKey.Lock()
	defer lockRouteKey.Unlock()
	store, ok = storeRouteKey.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RouteKey)
		if ok {
			return storeMap
		}
	}
	tblRouteKey := make(map[int64]*RouteKey)
	route_key_str, err := consulconfig.GetInstance().GetConfig(strRouteKey, option...)
	if route_key_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(route_key_str), &tblRouteKey)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "route_key", errUnmarshal)
		return nil
	}
	storeRouteKey.Store(fitKey, tblRouteKey)
	return tblRouteKey
}

func GetRouteKey(id int64, option ...consulconfig.Option) *RouteKey {
	fitKey := fixKeyRouteKey(option...)
	store, ok := storeRouteKey.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RouteKey)
		if ok {
			return storeMap[id]
		}
	}
	lockRouteKey.Lock()
	defer lockRouteKey.Unlock()
	store, ok = storeRouteKey.Load(fitKey)
	if ok {
		tblRouteKey, ok := store.(*RouteKey)
		if ok {
			return tblRouteKey
		}
	}
	tblRouteKey := make(map[int64]*RouteKey)
	route_key_str, err := consulconfig.GetInstance().GetConfig(strRouteKey, option...)
	if route_key_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(route_key_str), &tblRouteKey)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "route_key", errUnmarshal)
		return nil
	}
	storeRouteKey.Store(fitKey, tblRouteKey)
	return tblRouteKey[id]
}

func LoadAllRouteKeyCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strRouteKey, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RouteKey", successChannels)
	return nil
}
