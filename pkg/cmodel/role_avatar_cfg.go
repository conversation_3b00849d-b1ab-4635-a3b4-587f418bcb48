// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RoleAvatar struct {
	Id   int32  `json:"id"`
	Name string `json:"name"`
	Mark string `json:"mark"`
}

var lockRoleAvatar sync.RWMutex
var storeRoleAvatar sync.Map
var strRoleAvatar string = "role_avatar"

func InitRoleAvatarCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRoleAvatar, watchRoleAvatarFunc)
	return LoadAllRoleAvatarCfg()
}

func fixKeyRoleAvatar(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.<PERSON><PERSON>hannel(), strRoleAvatar)
}
func watchRoleAvatarFunc(key string, js string) {
	mapRoleAvatar := make(map[int64]*RoleAvatar)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRoleAvatar)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRoleAvatar.Store(key, mapRoleAvatar)
}

func GetAllRoleAvatar(option ...consulconfig.Option) map[int64]*RoleAvatar {
	fitKey := fixKeyRoleAvatar(option...)
	store, ok := storeRoleAvatar.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RoleAvatar)
		if ok {
			return storeMap
		}
	}
	lockRoleAvatar.Lock()
	defer lockRoleAvatar.Unlock()
	store, ok = storeRoleAvatar.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RoleAvatar)
		if ok {
			return storeMap
		}
	}
	tblRoleAvatar := make(map[int64]*RoleAvatar)
	role_avatar_str, err := consulconfig.GetInstance().GetConfig(strRoleAvatar, option...)
	if role_avatar_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(role_avatar_str), &tblRoleAvatar)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "role_avatar", errUnmarshal)
		return nil
	}
	storeRoleAvatar.Store(fitKey, tblRoleAvatar)
	return tblRoleAvatar
}

func GetRoleAvatar(id int64, option ...consulconfig.Option) *RoleAvatar {
	fitKey := fixKeyRoleAvatar(option...)
	store, ok := storeRoleAvatar.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RoleAvatar)
		if ok {
			return storeMap[id]
		}
	}
	lockRoleAvatar.Lock()
	defer lockRoleAvatar.Unlock()
	store, ok = storeRoleAvatar.Load(fitKey)
	if ok {
		tblRoleAvatar, ok := store.(*RoleAvatar)
		if ok {
			return tblRoleAvatar
		}
	}
	tblRoleAvatar := make(map[int64]*RoleAvatar)
	role_avatar_str, err := consulconfig.GetInstance().GetConfig(strRoleAvatar, option...)
	if role_avatar_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(role_avatar_str), &tblRoleAvatar)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "role_avatar", errUnmarshal)
		return nil
	}
	storeRoleAvatar.Store(fitKey, tblRoleAvatar)
	return tblRoleAvatar[id]
}

func LoadAllRoleAvatarCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strRoleAvatar, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RoleAvatar", successChannels)
	return nil
}
