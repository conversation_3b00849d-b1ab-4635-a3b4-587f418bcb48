// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type PoseAffinity struct {
	Id        int64   `json:"id"`
	PoseGroup int64   `json:"poseGroup"`
	PoseType  int32   `json:"poseType"`
	A         float64 `json:"a"`
	B         float64 `json:"b"`
	C         float64 `json:"c"`
}

var lockPoseAffinity sync.RWMutex
var storePoseAffinity sync.Map
var strPoseAffinity string = "pose_affinity"

func InitPoseAffinityCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strPoseAffinity, watchPoseAffinityFunc)
	return LoadAllPoseAffinityCfg()
}

func fixKeyPoseAffinity(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strPoseAffinity)
}
func watchPoseAffinityFunc(key string, js string) {
	mapPoseAffinity := make(map[int64]*PoseAffinity)
	errUnmarshal := json.Unmarshal([]byte(js), &mapPoseAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storePoseAffinity.Store(key, mapPoseAffinity)
}

func GetAllPoseAffinity(option ...consulconfig.Option) map[int64]*PoseAffinity {
	fitKey := fixKeyPoseAffinity(option...)
	store, ok := storePoseAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PoseAffinity)
		if ok {
			return storeMap
		}
	}
	lockPoseAffinity.Lock()
	defer lockPoseAffinity.Unlock()
	store, ok = storePoseAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PoseAffinity)
		if ok {
			return storeMap
		}
	}
	tblPoseAffinity := make(map[int64]*PoseAffinity)
	pose_affinity_str, err := consulconfig.GetInstance().GetConfig(strPoseAffinity, option...)
	if pose_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(pose_affinity_str), &tblPoseAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "pose_affinity", errUnmarshal)
		return nil
	}
	storePoseAffinity.Store(fitKey, tblPoseAffinity)
	return tblPoseAffinity
}

func GetPoseAffinity(id int64, option ...consulconfig.Option) *PoseAffinity {
	fitKey := fixKeyPoseAffinity(option...)
	store, ok := storePoseAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PoseAffinity)
		if ok {
			return storeMap[id]
		}
	}
	lockPoseAffinity.Lock()
	defer lockPoseAffinity.Unlock()
	store, ok = storePoseAffinity.Load(fitKey)
	if ok {
		tblPoseAffinity, ok := store.(*PoseAffinity)
		if ok {
			return tblPoseAffinity
		}
	}
	tblPoseAffinity := make(map[int64]*PoseAffinity)
	pose_affinity_str, err := consulconfig.GetInstance().GetConfig(strPoseAffinity, option...)
	if pose_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(pose_affinity_str), &tblPoseAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "pose_affinity", errUnmarshal)
		return nil
	}
	storePoseAffinity.Store(fitKey, tblPoseAffinity)
	return tblPoseAffinity[id]
}

func LoadAllPoseAffinityCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strPoseAffinity, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "PoseAffinity", successChannels)
	return nil
}
