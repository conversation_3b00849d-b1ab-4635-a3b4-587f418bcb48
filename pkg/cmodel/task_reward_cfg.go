// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type TaskRewardRewards struct {
	ItemId int64 `json:"itemId"`
	Count  int64 `json:"count"`
}

type TaskReward struct {
	Id      int64               `json:"id"`
	Name    string              `json:"name"`
	Mark    string              `json:"mark"`
	Rewards []TaskRewardRewards `json:"rewards"`
}

var lockTaskReward sync.RWMutex
var storeTaskReward sync.Map
var strTaskReward string = "task_reward"

func InitTaskRewardCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strTaskReward, watchTaskRewardFunc)
	return LoadAllTaskRewardCfg()
}

func fixKeyTaskReward(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strTaskReward)
}
func watchTaskRewardFunc(key string, js string) {
	mapTaskReward := make(map[int64]*TaskReward)
	errUnmarshal := json.Unmarshal([]byte(js), &mapTaskReward)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeTaskReward.Store(key, mapTaskReward)
}

func GetAllTaskReward(option ...consulconfig.Option) map[int64]*TaskReward {
	fitKey := fixKeyTaskReward(option...)
	store, ok := storeTaskReward.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskReward)
		if ok {
			return storeMap
		}
	}
	lockTaskReward.Lock()
	defer lockTaskReward.Unlock()
	store, ok = storeTaskReward.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskReward)
		if ok {
			return storeMap
		}
	}
	tblTaskReward := make(map[int64]*TaskReward)
	task_reward_str, err := consulconfig.GetInstance().GetConfig(strTaskReward, option...)
	if task_reward_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_reward_str), &tblTaskReward)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_reward", errUnmarshal)
		return nil
	}
	storeTaskReward.Store(fitKey, tblTaskReward)
	return tblTaskReward
}

func GetTaskReward(id int64, option ...consulconfig.Option) *TaskReward {
	fitKey := fixKeyTaskReward(option...)
	store, ok := storeTaskReward.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*TaskReward)
		if ok {
			return storeMap[id]
		}
	}
	lockTaskReward.Lock()
	defer lockTaskReward.Unlock()
	store, ok = storeTaskReward.Load(fitKey)
	if ok {
		tblTaskReward, ok := store.(*TaskReward)
		if ok {
			return tblTaskReward
		}
	}
	tblTaskReward := make(map[int64]*TaskReward)
	task_reward_str, err := consulconfig.GetInstance().GetConfig(strTaskReward, option...)
	if task_reward_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_reward_str), &tblTaskReward)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task_reward", errUnmarshal)
		return nil
	}
	storeTaskReward.Store(fitKey, tblTaskReward)
	return tblTaskReward[id]
}

func LoadAllTaskRewardCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strTaskReward, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "TaskReward", successChannels)
	return nil
}
