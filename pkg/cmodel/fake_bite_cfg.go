// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FakeBite struct {
	Id            int32 `json:"id"`
	BehaviourId   int32 `json:"behaviourId"`
	ActionId      int32 `json:"actionId"`
	ProbWeight    int32 `json:"probWeight"`
	ProportionMin int32 `json:"proportionMin"`
	ProportionMax int32 `json:"proportionMax"`
	TimeMin       int32 `json:"timeMin"`
	TimeMax       int32 `json:"timeMax"`
}

var lockFakeBite sync.RWMutex
var storeFakeBite sync.Map
var strFakeBite string = "fake_bite"

func InitFakeBiteCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFakeBite, watchFakeBiteFunc)
	return LoadAllFakeBiteCfg()
}

func fixKeyFakeBite(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFakeBite)
}
func watchFakeBiteFunc(key string, js string) {
	mapFakeBite := make(map[int64]*FakeBite)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFakeBite)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFakeBite.Store(key, mapFakeBite)
}

func GetAllFakeBite(option ...consulconfig.Option) map[int64]*FakeBite {
	fitKey := fixKeyFakeBite(option...)
	store, ok := storeFakeBite.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FakeBite)
		if ok {
			return storeMap
		}
	}
	lockFakeBite.Lock()
	defer lockFakeBite.Unlock()
	store, ok = storeFakeBite.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FakeBite)
		if ok {
			return storeMap
		}
	}
	tblFakeBite := make(map[int64]*FakeBite)
	fake_bite_str, err := consulconfig.GetInstance().GetConfig(strFakeBite, option...)
	if fake_bite_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fake_bite_str), &tblFakeBite)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fake_bite", errUnmarshal)
		return nil
	}
	storeFakeBite.Store(fitKey, tblFakeBite)
	return tblFakeBite
}

func GetFakeBite(id int64, option ...consulconfig.Option) *FakeBite {
	fitKey := fixKeyFakeBite(option...)
	store, ok := storeFakeBite.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FakeBite)
		if ok {
			return storeMap[id]
		}
	}
	lockFakeBite.Lock()
	defer lockFakeBite.Unlock()
	store, ok = storeFakeBite.Load(fitKey)
	if ok {
		tblFakeBite, ok := store.(*FakeBite)
		if ok {
			return tblFakeBite
		}
	}
	tblFakeBite := make(map[int64]*FakeBite)
	fake_bite_str, err := consulconfig.GetInstance().GetConfig(strFakeBite, option...)
	if fake_bite_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fake_bite_str), &tblFakeBite)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fake_bite", errUnmarshal)
		return nil
	}
	storeFakeBite.Store(fitKey, tblFakeBite)
	return tblFakeBite[id]
}

func LoadAllFakeBiteCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strFakeBite, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FakeBite", successChannels)
	return nil
}
