package protox

import (
	"hash/crc32"

	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
)

// ClientVerData 带版本号数据报文
type ClientVerData interface {
	Reset()
	GetDataVer() uint32
}

// CheckDataVer 版本号相同数据重置
// eg:：
//
//	if req.DataVer != nil {
//		rsp.DataVer = protox.CheckDataVer(&req, rsp, rsp)
//	}
func CheckDataVer(req, rsp ClientVerData, body proto.Message) *uint32 {
	oldHash := req.GetDataVer()
	newHash := GetDataVer(body)
	if oldHash != newHash {
		return proto.Uint32(newHash)
	}
	rsp.Reset()
	return proto.Uint32(newHash)
}

// GetDataVer 获取数据Hash值
func GetDataVer(body proto.Message) uint32 {
	buff, err := proto.Marshal(body)
	if err != nil {
		logrus.WithError(err).Error("check datagram fail, serialize failed")
		return 0
	}

	hash32 := crc32.NewIEEE()
	hash32.Write(buff)
	return hash32.Sum32()
}
