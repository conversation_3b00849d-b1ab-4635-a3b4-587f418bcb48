package playerx

import (
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

type Info struct {
	ProductId int32  `json:"productId"` // 产品线
	PlayerId  uint64 `json:"playerId"`  // 用户id
}

func New(productId int32, playerId uint64) (*Info, error) {
	if _, exist := commonPB.PRODUCT_ID_name[productId]; !exist {
		return nil, fmt.Errorf("product invaild :%d", productId)
	}
	if playerId == 0 {
		return nil, fmt.Errorf("playerId not be zero")
	}

	return &Info{
		ProductId: productId,
		PlayerId:  playerId,
	}, nil
}
