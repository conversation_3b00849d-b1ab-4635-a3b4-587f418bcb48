package item_kit

import (
	"context"
	"fmt"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	consul_config "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

// GetTackleConf 尝试取得道具的配置
func GetTackleConf(ctx context.Context, itemId int64) (*TackleConf, error) {
	itemCfg := cmodel.GetItem(itemId, consul_config.WithGrpcCtx(ctx))
	if itemCfg == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("unknown item: %d", itemId))
	}
	var cfg *TackleConf

	// 根据类型取表 聚合数据
	switch commonPB.ITEM_TYPE(itemCfg.ItemType) {
	// 鱼竿
	case commonPB.ITEM_TYPE_IT_TACKLE_RODS:
		sourceCfg := cmodel.GetRods(itemId, consul_config.WithGrpcCtx(ctx))
		if sourceCfg == nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("unknown rods: %d", itemId))
		}

		cfg = &TackleConf{
			Durability:      sourceCfg.Durability,
			DurabilityCoeff: sourceCfg.DurabilityCoeff,
		}
		// 渔轮
	case commonPB.ITEM_TYPE_IT_TACKLE_REEl:
		sourceCfg := cmodel.GetReels(itemId, consul_config.WithGrpcCtx(ctx))
		if sourceCfg == nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("unknown reels: %d", itemId))
		}

		cfg = &TackleConf{
			Durability:      sourceCfg.Durability,
			DurabilityCoeff: sourceCfg.DurabilityCoeff,
		}
		// 主线
	case commonPB.ITEM_TYPE_IT_TACKLE_LINE:
		sourceCfg := cmodel.GetLines(itemId, consul_config.WithGrpcCtx(ctx))
		if sourceCfg == nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("unknown line: %d", itemId))
		}

		cfg = &TackleConf{
			Durability:      sourceCfg.Durability,
			DurabilityCoeff: sourceCfg.DurabilityCoeff,
		}
		// 前导
	case commonPB.ITEM_TYPE_IT_TACKLE_LEADER:
		sourceCfg := cmodel.GetLeaders(itemId, consul_config.WithGrpcCtx(ctx))
		if sourceCfg == nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("unknown rods: %d", itemId))
		}

		cfg = &TackleConf{
			Durability:      sourceCfg.Durability,
			DurabilityCoeff: sourceCfg.DurabilityCoeff,
		}
		// 鱼饵
	case commonPB.ITEM_TYPE_IT_TACKLE_BAIT:
		sourceCfg := cmodel.GetBaits(itemId, consul_config.WithGrpcCtx(ctx))
		if sourceCfg == nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("unknown rods: %d", itemId))
		}

		cfg = &TackleConf{
			Durability: sourceCfg.Durability,
		}
		// 浮漂
	case commonPB.ITEM_TYPE_IT_TACKLE_BOBBERS:
		sourceCfg := cmodel.GetBobbers(itemId, consul_config.WithGrpcCtx(ctx))
		if sourceCfg == nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("unknown rods: %d", itemId))
		}

		cfg = &TackleConf{
			Durability: sourceCfg.Durability,
		}
		// 钩子
	case commonPB.ITEM_TYPE_IT_TACKLE_HOOKS:
		sourceCfg := cmodel.GetHooks(itemId, consul_config.WithGrpcCtx(ctx))
		if sourceCfg == nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("unknown rods: %d", itemId))
		}

		cfg = &TackleConf{}
		// 路亚饵
	case commonPB.ITEM_TYPE_IT_TACKLE_LURES:
		sourceCfg := cmodel.GetLures(itemId, consul_config.WithGrpcCtx(ctx))
		if sourceCfg == nil {
			return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, fmt.Sprintf("unknown rods: %d", itemId))
		}

		cfg = &TackleConf{
			Durability: sourceCfg.Durability,
		}
	}

	if cfg == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, fmt.Sprintf("unknown tackle item: %d", itemCfg.Id))
	}

	return cfg, nil
}

// 聚合后端需要使用的配置
type TackleConf struct {
	Durability      int32 // 耐久度
	DurabilityCoeff int32 // 耐久度系数 / 强度系数
}
