package item_kit

import (
	"context"
	"testing"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func TestExtract(t *testing.T) {
	stocks := []*commonPB.ItemInfo{
		{
			Item: &commonPB.Item{
				ItemId:     1,
				InstanceId: "xx",
				ItemLevel:  1,
			},

			ItemCount: 3,
		},
		{
			Item: &commonPB.Item{
				ItemId:     1,
				InstanceId: "pp",
				ItemLevel:  1,
			},

			ItemCount: 2,
		},
	}
	expect := []*commonPB.ItemBase{
		{
			ItemId:     1,
			InstanceId: "xx",
			ItemCount:  4,
		},
	}
	ctx := context.Background()
	itemList, err := ExtractItem(ctx, stocks, expect)
	if err != nil {
		t.Fatalf("err:%+v", err)
	}
	t.<PERSON>alf("itemList:%+v", itemList)
}
