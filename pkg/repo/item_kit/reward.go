package item_kit

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hallrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_hall"
	"github.com/sirupsen/logrus"
)

// SendReward 发送奖励 ctx要携带productId
// isUnpack: 是否拆包 针对礼包奖励
// 注意: hall服务器不要使用这个方法 可以用OperatePlayerItem
func SendReward(ctx context.Context, playerId uint64, itemList []*commonPB.ItemBase, srcType commonPB.ITEM_SOURCE_TYPE, isUnpack bool) (*commonPB.Reward, error) {
	rpcReq := &hallRpc.OptPlayerItemReq{
		PlayerId:      playerId,
		ItemList:      itemList,
		ItemOperation: commonPB.ITEM_OPERATION_IO_ADD,
		ItemSource:    srcType,
		IsUnpack:      isUnpack,
	}

	rpcRsp, err := crpc_hall.RpcOptPlayerItem(ctx, rpcReq)
	if err != nil || rpcRsp == nil {
		logrus.Errorf("add player:%d item failed, err: %v", playerId, err)
		return nil, err
	}

	return rpcRsp, nil
}

// DeductItem 扣除道具 ctx要携带productId
// 注意: hall服务器不要使用这个方法 可以用OperatePlayerItem
func DeductItem(ctx context.Context, playerId uint64, itemList []*commonPB.ItemBase, srcType commonPB.ITEM_SOURCE_TYPE) (*commonPB.Reward, error) {
	rpcReq := &hallRpc.OptPlayerItemReq{
		PlayerId:      playerId,
		ItemList:      itemList,
		ItemOperation: commonPB.ITEM_OPERATION_IO_REDUCE,
		ItemSource:    srcType,
		IsUnpack:      false,
	}

	rpcRsp, err := crpc_hall.RpcOptPlayerItem(ctx, rpcReq)
	if err != nil || rpcRsp == nil {
		logrus.Errorf("deduct player:%d item failed, err: %v", playerId, err)
		return nil, err
	}

	return rpcRsp, nil
}
