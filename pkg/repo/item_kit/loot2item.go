package item_kit

import (
	"sort"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// MergeLoot2Item 将LootArray结果转为ItemArray
func MergeLoot2Item(loots []*commonPB.OriginLoot) []*commonPB.OriginLoot {
	itemCountMap := make(map[commonPB.ITEM_TYPE]*commonPB.OriginLoot)
	itemExpect := make([]*commonPB.OriginLoot, 0)
	var sortIndex []int64 // 排序，最后按照原始数据的itemType排序

	// 货币按照ItemType进行分类汇总
	for _, loot := range loots {
		itemID := loot.Item.ItemId
		itemType := loot.Item.ItemType
		itemValue := loot.Value

		if itemCountMap[itemType] == nil {
			itemCountMap[itemType] = loot
			sortIndex = append(sortIndex, itemID)
		} else {
			itemCountMap[itemType].Value += itemValue
		}
	}

	for _, itemLoot := range itemCountMap {
		itemExpect = append(itemExpect, &commonPB.OriginLoot{
			Item:  itemLoot.Item,
			Value: itemLoot.Value,
			// Source: itemLoot.Source,
		})
	}

	// 使用sort.SliceStable，传入自定义的排序逻辑
	sort.SliceStable(itemExpect, func(i, j int) bool {
		return itemExpect[i].Item.ItemId < sortIndex[j]
	})
	return itemExpect
}
