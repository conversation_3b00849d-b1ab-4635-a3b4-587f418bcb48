package item_kit

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func ItemBase2ItemInfo(ctx context.Context, ib *commonPB.ItemBase) *commonPB.ItemInfo {

	return &commonPB.ItemInfo{
		Item: &commonPB.Item{
			ItemId:     ib.ItemId,
			InstanceId: ib.InstanceId,
		},
		ItemDeltaCount: ib.ItemCount,
	}
}

func ItemInfo2ItemBase(ctx context.Context, itf *commonPB.ItemInfo) *commonPB.ItemBase {
	return &commonPB.ItemBase{
		ItemId:     itf.Item.ItemId,
		InstanceId: itf.Item.InstanceId,
		ItemCount:  itf.ItemDeltaCount,
	}
}

func CopyItem(old *commonPB.Item) *commonPB.Item {
	return &commonPB.Item{
		ItemId:         old.ItemId,
		ItemCategory:   old.ItemCategory,
		ItemType:       old.ItemType,
		ItemSubType:    old.ItemSubType,
		InstanceId:     old.InstanceId,
		ItemExpireTime: old.ItemExpireTime,
		Extra:          old.Extra,
	}
}

func Loot2ItemInfo(loot *commonPB.OriginLoot) *commonPB.ItemInfo {
	return &commonPB.ItemInfo{
		Item:      loot.Item,
		ItemCount: loot.Value,
	}
}
