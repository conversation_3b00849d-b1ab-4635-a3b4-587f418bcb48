package item_kit

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// 耐久度相关处理

// GetCurrDurability 获取当前耐久度
func GetCurrDurability(ctx context.Context, item *commonPB.Item) int64 {
	if item.Extra == nil {
		return 0
	}
	val := item.Extra[int32(commonPB.ITEM_EXTRA_KEY_IEK_CURR_DURABILITY)]
	return val
}

// 设置当前耐久
func SetCurrDurability(ctx context.Context, item *commonPB.Item, currDurability int64) {
	if item.Extra == nil {
		item.Extra = make(map[int32]int64)
	}
	item.Extra[int32(commonPB.ITEM_EXTRA_KEY_IEK_CURR_DURABILITY)] = currDurability
}

func GetMaxDurability(ctx context.Context, item *commonPB.Item) int64 {
	if item.Extra == nil {
		return 0
	}
	val := item.Extra[int32(commonPB.ITEM_EXTRA_KEY_IEK_MAX_DURABILITY)]

	return val
}

// 设置最大耐久
func SetMaxDurability(ctx context.Context, item *commonPB.Item, maxDurability int64) {
	if item.Extra == nil {
		item.Extra = make(map[int32]int64)
	}
	item.Extra[int32(commonPB.ITEM_EXTRA_KEY_IEK_MAX_DURABILITY)] = maxDurability
}

// CheckItemAbility 检查物品可用性
func CheckItemAbility(ctx context.Context, item *commonPB.Item) bool {
	durability := GetCurrDurability(ctx, item)
	// 当前耐久属性是否大于0
	return durability > 0
}
