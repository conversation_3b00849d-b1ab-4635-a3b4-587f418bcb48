package item_kit

import commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"

type CategoryInfo struct {
	Category  commonPB.ITEM_CATEGORY
	Name      string
	SubFromID int
	SubToID   int
}

func GetAllItemTypeIdInfo() []CategoryInfo {
	return []CategoryInfo{
		{
			Category:  commonPB.ITEM_CATEGORY_IC_CURRENCY,
			Name:      "货币",
			SubFromID: 101,
			SubToID:   199,
		},
		{
			Category:  commonPB.ITEM_CATEGORY_IC_PROP,
			Name:      "道具",
			SubFromID: 201,
			SubToID:   299,
		},
		{
			Category:  commonPB.ITEM_CATEGORY_IC_TACKLE,
			Name:      "渔具",
			SubFromID: 301,
			SubToID:   399,
		},
		{
			Category:  commonPB.ITEM_CATEGORY_IC_WEARABLE,
			Name:      "穿戴物品",
			SubFromID: 401,
			SubToID:   499,
		},
		{
			Category:  commonPB.ITEM_CATEGORY_IC_EQUIP,
			Name:      "装备类型",
			SubFromID: 501,
			SubToID:   599,
		},
		{
			Category:  commonPB.ITEM_CATEGORY_IC_TICKET,
			Name:      "票券类型",
			SubFromID: 601,
			SubToID:   699,
		},
		{
			Category:  commonPB.ITEM_CATEGORY_IC_FRAGMENTS,
			Name:      "合成系统碎片",
			SubFromID: 701,
			SubToID:   799,
		},
		{
			Category:  commonPB.ITEM_CATEGORY_IC_REWARD,
			Name:      "奖励物品",
			SubFromID: 801,
			SubToID:   899,
		},
		{
			Category:  commonPB.ITEM_CATEGORY_IC_GOODS,
			Name:      "商品",
			SubFromID: 901,
			SubToID:   999,
		},
		{
			Category:  commonPB.ITEM_CATEGORY_IC_CATCH,
			Name:      "捕获物",
			SubFromID: 1001,
			SubToID:   1199,
		},
	}
}

// GetItemTypeRangeByCategory 查询道具大类的信息
func GetItemTypeRangeByCategory(category commonPB.ITEM_CATEGORY) CategoryInfo {
	categoryArr := GetAllItemTypeIdInfo()
	retCategory := CategoryInfo{}
	for _, info := range categoryArr {
		if info.Category == category {
			retCategory = info
		}
	}
	return retCategory
}

// GetAllItemCategory 查询所有道具Category 类型
func GetAllItemCategory() []commonPB.ITEM_CATEGORY {

	categoryArr := make([]commonPB.ITEM_CATEGORY, 0, len(commonPB.ITEM_CATEGORY_name))

	for v, _ := range commonPB.ITEM_CATEGORY_name {
		if v == 0 {
			continue
		}
		categoryArr = append(categoryArr, commonPB.ITEM_CATEGORY(v))
	}

	return categoryArr
}
