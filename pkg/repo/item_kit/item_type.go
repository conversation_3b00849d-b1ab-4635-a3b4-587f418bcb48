package item_kit

import commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"

// BelongToCategory ItemType是否属于Category
func BelongToCategory(itemType commonPB.ITEM_TYPE, category commonPB.ITEM_CATEGORY) bool {
	isBelong := false
	categoryInfo := GetItemTypeRangeByCategory(category)
	if int(itemType) >= categoryInfo.SubFromID && int(itemType) <= categoryInfo.SubToID {
		isBelong = true
	}
	return isBelong
}

// GetItemTypeByCategoryArray 获取所有Category的子ItemType列表
func GetItemTypeByCategoryArray(category commonPB.ITEM_CATEGORY) []commonPB.ITEM_TYPE {
	var array []commonPB.ITEM_TYPE
	for v, _ := range commonPB.ITEM_TYPE_name {
		if BelongToCategory(commonPB.ITEM_TYPE(v), category) {
			array = append(array, commonPB.ITEM_TYPE(v))
		}
	}
	return array
}

// GetItemTypeCategory 查询item_type归属的category
func GetItemTypeCategory(itemType commonPB.ITEM_TYPE) commonPB.ITEM_CATEGORY {
	for v, _ := range commonPB.ITEM_CATEGORY_name {
		if BelongToCategory(itemType, commonPB.ITEM_CATEGORY(v)) {
			return commonPB.ITEM_CATEGORY(v)
		}
	}

	return commonPB.ITEM_CATEGORY_IC_UNKNOWN
}

// IsCurrency 是否货币
func IsCurrency(itemType commonPB.ITEM_TYPE) bool {
	return BelongToCategory(itemType, commonPB.ITEM_CATEGORY_IC_CURRENCY)
}

// IsProp 是否Prop
func IsProp(itemType commonPB.ITEM_TYPE) bool {
	return BelongToCategory(itemType, commonPB.ITEM_CATEGORY_IC_PROP)
}

// IsTackle 是否渔具
func IsTackle(itemType commonPB.ITEM_TYPE) bool {
	return BelongToCategory(itemType, commonPB.ITEM_CATEGORY_IC_TACKLE)
}

// IsEquip 是否装备
func IsEquip(itemType commonPB.ITEM_TYPE) bool {
	return BelongToCategory(itemType, commonPB.ITEM_CATEGORY_IC_EQUIP)
}

var initCurrency []commonPB.ITEM_TYPE = []commonPB.ITEM_TYPE{
	commonPB.ITEM_TYPE_IT_CURRENCY_COIN,
	commonPB.ITEM_TYPE_IT_CURRENCY_DIAMOND,
}

// InitCurrency 初始化货币(必须存在货币)
func InitCurrency() []commonPB.ITEM_TYPE {
	return initCurrency
}

// IsStackType 是否是渔具道具类型
func IsStackType(t commonPB.ITEM_TYPE) bool {
	switch t {
	// 渔具类型
	case commonPB.ITEM_TYPE_IT_TACKLE_RODS, commonPB.ITEM_TYPE_IT_TACKLE_REEl, commonPB.ITEM_TYPE_IT_TACKLE_LINE, commonPB.ITEM_TYPE_IT_TACKLE_LEADER, commonPB.ITEM_TYPE_IT_TACKLE_BOBBERS, commonPB.ITEM_TYPE_IT_TACKLE_KEEPNETS:
		return false
		// 卡牌
		// case :
		// 	return true
	}
	return true
}

// IsPondItemType 是否是钓场中使用的道具类型 变化后需要通知钓场 更新数据
func IsPondItemType(t commonPB.ITEM_TYPE) bool {
	switch t {
	case commonPB.ITEM_TYPE_IT_CURRENCY_EXP, commonPB.ITEM_TYPE_IT_PROP_FOOD:
		return true
	}
	return false
}

func IsWearable(t commonPB.ITEM_TYPE) bool {
	switch t {
	case commonPB.ITEM_TYPE_IT_WEARABLE_AVATAR, commonPB.ITEM_TYPE_IT_WEARABLE_HEAD_FRAME:
		return true
	}
	return false
}

func IsCanSell(t commonPB.ITEM_TYPE) bool {
	switch t {
	// 出售范围： 杆轮线
	case commonPB.ITEM_TYPE_IT_TACKLE_RODS, commonPB.ITEM_TYPE_IT_TACKLE_REEl, commonPB.ITEM_TYPE_IT_TACKLE_LINE, commonPB.ITEM_TYPE_IT_TACKLE_BAIT:
		return true
	}
	return false
}
