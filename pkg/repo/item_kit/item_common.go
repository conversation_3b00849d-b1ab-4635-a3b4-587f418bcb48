package item_kit

import (
	"context"
	"fmt"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

// 规则：
// 1. 使用前使用merge聚合
// 2. 不可又使用instance又仅指定itemId使用
// 3. 有属性的内容通常只有一个且不堆叠
// ExtractItem 通用提取道具
func ExtractItem(ctx context.Context, stocks []*commonPB.ItemInfo, expects []*commonPB.ItemBase) ([]*commonPB.ItemInfo, error) {
	// 数据模式不一致
	if !sameModelCheck(expects) {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "extract item model check failed")
	}

	rtnList := make([]*commonPB.ItemInfo, 0)
	for _, one := range expects {
		rtn, err := doExtractItem(ctx, stocks, one)
		if err != nil {
			return nil, err
		}
		rtnList = append(rtnList, rtn...)
	}

	return rtnList, nil
}

// 计算提取数量
func doExtractItem(ctx context.Context, stocks []*commonPB.ItemInfo, expect *commonPB.ItemBase) ([]*commonPB.ItemInfo, error) {
	rtnList := make([]*commonPB.ItemInfo, 0)
	expectNum := expect.GetItemCount()
	for _, one := range stocks {
		if one.GetItem().GetItemId() == expect.GetItemId() {
			// 如果期望值 InstanceId 不为空，则需要全匹配
			if expect.GetInstanceId() != "" {
				if one.GetItem().GetInstanceId() != expect.GetInstanceId() {
					continue
				}
			}
			// 解析剩余数量
			stockNum := one.GetItemCount() - one.GetItemDeltaCount()
			// 已经莫得了
			if stockNum <= 0 {
				continue
			}

			// 提取数量小于库存数量
			if expectNum <= stockNum {
				one.ItemDeltaCount += expectNum
				expectNum = 0
				rtnList = append(rtnList, one)
				break
			} else {
				// 指定获取不可分拆
				if expect.InstanceId != "" {
					return nil, protox.CodeError(commonPB.ErrCode_ERR_HALL_ITEM_NOT_ENOUGH, fmt.Sprintf("item not enough, item_id:%d, instance:%s item_count: %d", expect.ItemId, expect.GetInstanceId(), expect.ItemCount))
				}
				// 提取数量大于库存数量 需要分拆
				expectNum -= stockNum
				// 之前已插入差异
				if one.ItemDeltaCount == 0 {
					rtnList = append(rtnList, one)
				}
				one.ItemDeltaCount = one.ItemCount // 全部使用
			}
		}
	}
	if expectNum > 0 {
		// 提取数量大于库存数量
		return nil, protox.CodeError(commonPB.ErrCode_ERR_HALL_ITEM_NOT_ENOUGH, fmt.Sprintf("item not enough, item_id:%d instance:%s item_count: %d", expect.ItemId, expect.GetInstanceId(), expect.ItemCount))
	}

	return rtnList, nil
}

// 是否使用相同的请求模式
func sameModelCheck(list []*commonPB.ItemBase) bool {
	if len(list) == 0 {
		return false
	}

	isInstance := false
	for index, item := range list {
		// 根据第一个请求判断基础模式
		if index == 0 {
			isInstance = item.InstanceId != ""
		}
		if isInstance {
			if item.InstanceId == "" {
				return false
			}
		} else {
			if item.InstanceId != "" {
				return false
			}
		}
	}
	return true
}
