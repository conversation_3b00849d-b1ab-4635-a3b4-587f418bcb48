package game_status

import (
	"context"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/gray_release"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

func CalculatePlayerGrayStatus(ctx context.Context, uid uint64, ip string, version string) commonPB.GRAY_STATUS {
	grayStatus := commonPB.GRAY_STATUS_GS_Normal
	strategy := cmodel.GetGrayStrategy()

	if !strategy.Enable {
		return grayStatus
	}

	startTimeStr := strategy.StartTime
	endTimeStr := strategy.EndTime
	if startTimeStr == "" || endTimeStr == "" {
		return grayStatus
	}

	startTimeInt := timex.ParseToTimeUnix(startTimeStr)
	endTimeInt := timex.ParseToTimeUnix(endTimeStr)
	nowTime := timex.Now().Unix()
	if nowTime < startTimeInt || nowTime > endTimeInt {
		return grayStatus
	}

	switch strategy.RuleType {
	case int64(commonPB.GRAY_STRATEGY_GS_IP):
		ipGrayArray := transform.StrSplit2Slice(strategy.IpList, dict.SysSymbolVerticalLine)

		if gray_release.InIPList(ip, ipGrayArray) {
			grayStatus = commonPB.GRAY_STATUS_GS_Gray
		}
	case int64(commonPB.GRAY_STRATEGY_GS_UID):
		tailArray := transform.StrSplit2Slice(strategy.UidTail, dict.SysSymbolVerticalLine)
		for _, s := range tailArray {
			nTail := transform.Str2Int(s)
			if gray_release.IsGrayByUserIDTail(uid, nTail) {
				grayStatus = commonPB.GRAY_STATUS_GS_Gray
				break
			}
		}
	case int64(commonPB.GRAY_STRATEGY_GS_TAGS):
		// TODO 根据用户标签
		tailArray := transform.StrSplit2Slice(strategy.TagList, dict.SysSymbolVerticalLine)

		if gray_release.InTagsList([]string{""}, tailArray) {
			grayStatus = commonPB.GRAY_STATUS_GS_Gray
		}
	case int64(commonPB.GRAY_STRATEGY_GS_VERSION):
		if gray_release.InGrayVersion(version, strategy.Version) {
			grayStatus = commonPB.GRAY_STATUS_GS_Gray
		}
	}
	return grayStatus
}
