package eventx

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

func EventPlayerCtx(event *commonPB.EventCommon) context.Context {
	return interceptor.NewRpcClientCtx(
		interceptor.WithPlayerId(event.PlayerId),
		interceptor.WithProductId(event.ProductId),
		interceptor.WithChannelType(event.ChannelId),
	)
}
