package recordx

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/spf13/viper"
	"testing"
	"time"
)

type RTest struct {
	UserID    int64
	HasFriend bool
	DeviceID  string
}

func (r *RTest) GetTableName() string {
	return "r_test"
}

func (r *RTest) Format() string {
	return MarshalWithLine(
		transform.Int642Str(r.UserID),
		transform.GetBoolStr(r.<PERSON>riend),
		r.DeviceID)
}

func init() {
	viper.Set("kafka.endpoints", []string{"************:9092"})
	Init()
}

func TestPush(t *testing.T) {

	header := DefaultHeader{
		ProductID:   commonPB.PRODUCT_ID_PID_FISHER,
		Platform:    commonPB.PLATFORM_TYPE_PT_WINDOWS,
		AppVersion:  "0.0.5",
		AppLanguage: commonPB.LANGUAGE_TYPE_LT_ZH_CN,
		Country:     "china",
		ChannelType: commonPB.CHANNEL_TYPE_CT_MASTER,
		AccType:     commonPB.ACC_TYPE_AT_GUEST,
	}

	obj := &RTest{
		UserID:    123322323,
		HasFriend: true,
		DeviceID:  "11223123",
	}

	body := SerializeData(header, obj)
	t.Log(body)
	Deliver(obj.GetTableName(), body)
	time.Sleep(1 * time.Second)
}
