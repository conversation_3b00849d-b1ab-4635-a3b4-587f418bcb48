package recordx

import (
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

func SerializeData(head DefaultHeader, body IReport) string {
	now := timex.Unix()
	nowDateStr := timex.TimeMsToDateString(timex.Millisecond())
	return MarshalWithLine(
		body.GetTableName(),
		head.String(),
		nowDateStr,
		transform.Int642Str(now),
		body.Format())
}
