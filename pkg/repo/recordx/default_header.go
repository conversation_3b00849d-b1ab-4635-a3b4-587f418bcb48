package recordx

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

type DefaultHeader struct {
	ProductID   commonPB.PRODUCT_ID    // 产品id
	ChannelType commonPB.CHANNEL_TYPE  // 渠道
	Platform    commonPB.PLATFORM_TYPE // 平台
	AppLanguage commonPB.LANGUAGE_TYPE // 语言
	Country     string                 // 国家
	AccType     commonPB.ACC_TYPE      // 账号类型
	AppVersion  string                 // 游戏版本
	// ReportDay   int                    // 日期
	// ReportTime  int                    // 时间
}

func (h DefaultHeader) String() string {
	headStr, _ := transform.Struct2String(h, dict.SysSymbolVerticalLine)
	return headStr
}

// NewDefaultHeaderFromCtx 从ctx中获取header
func NewDefaultHeaderFromCtx(ctx context.Context) DefaultHeader {

	// 从rpc ctx取出header
	options := interceptor.GetRPCOptions(ctx)

	// TODO 如果国家没有 先给个默认值 后续修改
	if options.Country == "" {
		options.Country = "CN"
	}

	return DefaultHeader{
		ProductID:   commonPB.PRODUCT_ID(options.ProductId),
		ChannelType: commonPB.CHANNEL_TYPE(options.ChannelType),
		Platform:    commonPB.PLATFORM_TYPE(options.Platform),
		AppLanguage: commonPB.LANGUAGE_TYPE(options.AppLanguage),
		Country:     options.Country,
		AccType:     commonPB.ACC_TYPE(options.AccType),
		AppVersion:  options.AppVersion,
	}
}
