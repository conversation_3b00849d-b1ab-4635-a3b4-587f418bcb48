package recordx

import (
	"sync"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/kafkax"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

var (
	mu        sync.RWMutex
	endpoints string
	producer  *kafkax.KafkaProducer
)

func Init() {
	endpoints := viper.GetStringSlice(dict.ConfigKafkaUrl)
	var err error
	producer, err = kafkax.NewKafkaAsyncProducer(endpoints)
	if err != nil {
		// TODO 线上不能崩
		logrus.Errorf("NewKafkaAsyncProducer err : %v", err)
	}
}

func Deliver(topic, data string) {
	if producer == nil {
		logrus.Warnf("producer is null")
		return

	}
	producer.OnProduce(topic, data, nil)
}
