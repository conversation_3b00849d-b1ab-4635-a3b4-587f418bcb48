package operate

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/mathx"
	"github.com/sirupsen/logrus"
)

func CheckVal(opType commonPB.VAL_OPERATE, val, check int64) bool {
	switch opType {
	case commonPB.VAL_OPERATE_VO_LESS:
		return val < check
	case commonPB.VAL_OPERATE_VO_LESS_EQUAL:
		return val <= check
	case commonPB.VAL_OPERATE_VO_EQUAL:
		return val == check
	case commonPB.VAL_OPERATE_VO_GREATER:
		return val > check
	case commonPB.VAL_OPERATE_VO_GREATER_EQUAL:
		return val >= check
	case commonPB.VAL_OPERATE_VO_NOT_EQUAL:
		return val != check
	}
	return false
}

// SumAddVal 统计更新数值 (需要提前过滤除重累加)
func SumAddVal(addType commonPB.SUM_ADD, old, new int64) int64 {
	switch addType {
	case commonPB.SUM_ADD_SA_ADD:
		return old + new
	case commonPB.SUM_ADD_SA_MAX:
		return mathx.Max(old, new)
	case commonPB.SUM_ADD_SA_VAL:
		return new
	}
	logrus.Errorf("SumAddVal: unknown addType:%d val:%d:%d", addType, old, new)
	return 0
}
