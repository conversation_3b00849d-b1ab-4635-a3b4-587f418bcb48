package a_login

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
	"time"
)

func (te *TeRegisterInfo) GetEvent() string {
	return analyze.EventRegister
}

type TeRegisterInfo struct {
	*analyze.DefaultHeader
	*TeRegister
}

type TeRegister struct {
	DeviceInfo   *common.DeviceInfo `json:"device_info,omitempty"` // 设备信息
	RegisterTime time.Time          `json:"register_time"`         // 注册时间
}
