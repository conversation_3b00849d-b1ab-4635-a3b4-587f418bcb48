package a_login

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
	"time"
)

func (te *TeLoginInfo) GetEvent() string {
	return analyze.EventLogin
}

type TeLoginInfo struct {
	*analyze.DefaultHeader
	*TeLogin
}

type TeLogin struct {
	LoginType     common.LOGIN_TYPE  `json:"login_type,omitempty"`  // 登录方式
	DeviceInfo    *common.DeviceInfo `json:"device_info,omitempty"` // 设备信息
	LastLoginTime time.Time          `json:"last_login_time"`       // 登录时间
}
