package a_hall

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
	"time"
)

// 事件名称
const (
	EventItemGet      = "item_get"      // 道具获得
	EventItemConsume  = "item_consume"  // 道具消耗
	EventTokenGet     = "token_get"     // 金币获得
	EventTokenConsume = "token_consume" // 金币消耗
	EventShopBuy      = "shop_buy"      // 商店购买
)

func (te *TeItemInfo) GetEvent() string {
	return analyze.EventItem
}

type TeItemInfo struct {
	*analyze.DefaultHeader
	*TeItem
}

type TeItem struct {
	Items       []*commonPB.ItemInfo      `json:"items"`        // 道具详情
	Source      commonPB.ITEM_SOURCE_TYPE `json:"source"`       // 来源类型
	ChangeType  commonPB.ITEM_OPERATION   `json:"change_type"`  // 修改类型
	OperateTime time.Time                 `json:"operate_time"` // 操作时间
}
