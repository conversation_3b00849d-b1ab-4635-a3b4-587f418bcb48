package gm_handler

import (
	"context"
	"google.golang.org/protobuf/encoding/protojson"

	"errors"
	"time"

	grpc "git.keepfancy.xyz/back-end/frameworks/kit/rpc"

	gmPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gm"
	gmRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/gmrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/lib/logdog"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/sirupsen/logrus"
)

type GmHandlerServer struct{}

func (s *GmHandlerServer) Cmd(ctx context.Context, req *gmRpc.GmCmdReq) (*gmRpc.GmCmdRsp, error) {
	defer logdog.RecoverAndLog()

	var (
		result = &gmRpc.GmCmdRsp{}
		cmdId  = req.GetCmd()
		entry  = logrus.WithFields(logrus.Fields{
			"cmdId": cmdId,
		})
		err error
	)

	handler := getHandler(cmdId)
	if handler == nil {
		entry.Warnf("Message handlers are not registered")
		return result, errors.New("message handlers are not registered")
	}

	result, err = handler(ctx, req)

	return result, err
}

func (s *GmHandlerServer) SetTestTime(ctx context.Context, req *gmRpc.GmCmdReq) (*gmRpc.GmCmdRsp, error) {
	logrus.Infof("[gm:SetGameTime]:%s", req.String())
	rsp := &gmRpc.GmCmdRsp{
		Ret: protox.DefaultResult(),
	}
	reqd := &gmPB.GmCmdGameTimeReq{}
	opt := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	err := opt.Unmarshal([]byte(req.Data), reqd)
	if err != nil {
		return rsp, err
	}
	if reqd.GetTs() != 0 {
		timex.SetOffset(reqd.GetTs() - time.Now().Unix())
	} else {
		timex.SetOffset(reqd.GetOffset())
	}

	return rsp, nil
}

func InitGmHandler() {
	gmRpc.RegisterGmServiceServer(grpc.Server, &GmHandlerServer{})
}
