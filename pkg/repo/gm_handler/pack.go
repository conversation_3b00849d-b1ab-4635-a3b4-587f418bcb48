package gm_handler

import (
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

func Marshal(v proto.Message) ([]byte, error) {
	jsMarsher := protojson.MarshalOptions{
		AllowPartial: true,
	}
	return jsMarsher.Marshal(v)
}

func Unmarshal(data []byte, v proto.Message) error {
	jsUnMarsher := protojson.UnmarshalOptions{
		AllowPartial:   true,
		DiscardUnknown: true,
	}
	return jsUnMarsher.Unmarshal(data, v)
}
