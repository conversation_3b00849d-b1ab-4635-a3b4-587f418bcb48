package gm_handler

import (
	"context"
	"sync"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	gmRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/gmrpc"
)

// 注入方法
func Handler(msgId commonPB.GM_CMD, handler handlerFunc) {
	_, loaded := handlerMap.LoadOrStore(msgId, handler)
	if loaded {
		panic("handler already exists")
	}
}

var handlerMap sync.Map // 注册器

type handlerFunc func(context.Context, *gmRpc.GmCmdReq) (*gmRpc.GmCmdRsp, error)

func getHandler(msgID commonPB.GM_CMD) handlerFunc {
	handler, ok := handlerMap.Load(msgID)
	if !ok {
		return nil
	}
	v := handler.(handlerFunc)
	return v
}
