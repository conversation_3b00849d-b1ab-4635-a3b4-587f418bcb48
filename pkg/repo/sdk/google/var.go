package google

const (
	PurchaseStateOk     = 0 // 已购买
	PurchaseStateCancel = 1 // 已取消
	PurchaseStateUnKnow = 2 // 未知
)

const (
	ConsumptionStateNot    = 0 // 尚未消费
	ConsumptionStateYet    = 1 // 已消费
	ConsumptionStateUnKnow = 2 // 未知
)

// AccessToken 获取AccessToken
type AccessToken struct {
	AccessToken  string `json:"access_token"`
	ExpiresIn    int64  `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	Scope        string `json:"scope"`
	TokenType    string `json:"token_type"`
}

type ErrorRsp struct {
	Code    int32  `json:"code"`
	Message string `json:"message"`
	Errors  []struct {
		Message string `json:"message"`
		Domain  string `json:"domain"`
		Reason  string `json:"reason"`
	} `json:"errors"`
}

// PurchaseBodyRsp 订单校验返回结果
type PurchaseBodyRsp struct {
	Error ErrorRsp `json:"error"`
	// This kind represents an in appPurchase object
	Kid string `json:"kind"`
	// he time the product was purchased
	PurchaseTimeMillis string `json:"purchaseTimeMillis"`
	// The purchase state of the order (购买状态 0:已购买 1：已取消)
	PurchaseState int `json:"purchaseState"`
	// The consumption state of the in-app product. (消费状态 0:尚未消费 1:已消费)
	ConsumptionState int `json:"consumptionState"`
	// A developer-specified string that contains supplemental information about an order.
	DeveloperPayload string `json:"developerPayload"`
	// The order id associated with the purchase of the in-app product.
	OrderId string `json:"orderId"`
	// The type of purchase of the in-app product.
	PurchaseType int `json:"purchaseType"`
	// The acknowledgement state of the in-app product
	AcknowledgementState int `json:"acknowledgementState"`
	// he purchase token generated to identify this purchase
	PurchaseToken string `json:"purchaseToken"`
	// The in-app product SKU.
	ProductId string `json:"productId"`
	// The quantity associated with the purchase of the in-app product.
	Quantity int `json:"quantity"`
	// An obfuscated version of the id that is uniquely associated with the user's account in your app
	ObfuscatedExternalAccountId string `json:"obfuscatedExternalAccountId"`
	// n obfuscated version of the id that is uniquely associated with the user's profile in your app
	ObfuscatedExternalProfileId string `json:"obfuscatedExternalProfileId"`
	// ISO 3166-1 alpha-2 billing region code of the user at the time the product was granted.
	RegionCode string `json:"regionCode"`
}

// ClientRsp 前端Json解析
type ClientRsp struct {
	Payload       string `json:"Payload"`
	StoreStr      string `json:"Store"`
	TransactionID string `json:"TransactionID"`
}

type CPayLoad struct {
	Json       string `json:"json"`
	Signature  string `json:"signature"`
	SkuDetails string `json:"skuDetails"`
}

type CPayLoadV4 struct {
	Json       string   `json:"json"`
	Signature  string   `json:"signature"`
	SkuDetails []string `json:"skuDetails"` // 新4.0版本
}

type CJson struct {
	OrderId              string `json:"orderId"`
	PackageName          string `json:"packageName"`
	ProductId            string `json:"productId"`
	PurchaseTime         int64  `json:"purchaseTime"`
	PurchaseState        int    `json:"purchaseState"`
	PurchaseToken        string `json:"purchaseToken"`
	AcknowledgementState bool   `json:"acknowledged"`
}

type CSkuDetails struct {
	ProductId         string `json:"productId"`
	Type              string `json:"type"`
	Title             string `json:"title"`
	Name              string `json:"name"`
	Description       string `json:"description"`
	Price             string `json:"price"`
	PriceAmountMicros int64  `json:"price_amount_micros"`
	PriceCurrencyCode string `json:"price_currency_code"`
	SkuDetailsToken   string `json:"skuDetailsToken"`
}

// UserInfo Google用户信息结构体
type UserInfo struct {
	Sub           string `json:"sub"`               // 用户在Google的唯一标识
	Name          string `json:"name"`              // 用户昵称
	GivenName     string `json:"given_name"`        // 名
	FamilyName    string `json:"family_name"`       // 姓
	Picture       string `json:"picture"`           // 头像URL
	Email         string `json:"email"`             // 邮箱
	EmailVerified bool   `json:"email_verified"`    // 邮箱是否验证
	Locale        string `json:"locale"`            // 地区
	Error         string `json:"error"`             // 错误信息
	ErrorDesc     string `json:"error_description"` // 错误描述
}

// PlayGamesPlayerInfo Google Play Games玩家信息结构体
type PlayGamesPlayerInfo struct {
	Kind               string     `json:"kind"`
	BaseInfo           BaseInfo   `json:"baseInfo"`
	PlayerId           string     `json:"playerId"`
	DisplayName        string     `json:"displayName"`
	Name               string     `json:"name"`
	AvatarImageUrl     string     `json:"avatarImageUrl"`
	BannerUrlLandscape string     `json:"bannerUrlLandscape"`
	BannerUrlPortrait  string     `json:"bannerUrlPortrait"`
	Error              *ErrorInfo `json:"error,omitempty"`
}

// BaseInfo Play Games用户基本信息结构体
type BaseInfo struct {
	ProfileSettings ProfileSettings `json:"profileSettings"`
	GamePlayerId    string          `json:"gamePlayerId"`
	GamerTag        string          `json:"gamerTag"`
	DisplayName     string          `json:"displayName"`
	AvatarImageUrl  string          `json:"avatarImageUrl"`
	BannerImageUrl  string          `json:"bannerImageUrl"`
}

// ProfileSettings Play Games用户资料设置结构体
type ProfileSettings struct {
	FriendsListVisibility string `json:"friendsListVisibility"`
	ProfileVisibility     string `json:"profileVisibility"`
}

// ErrorInfo Play Games API错误信息结构体
type ErrorInfo struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Status  string `json:"status"`
}
