package sdk

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/applestore"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/facebook"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/google"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/sdkvar"
)

type ILogin interface {
	GetUserAccount(channelID commonPB.CHANNEL_TYPE, accessToken string, code string) (*sdkvar.SdkUserInfo, error)
}

type LoginParams struct {
	ChannelID   commonPB.CHANNEL_TYPE
	AccType     commonPB.LOGIN_TYPE
	AccessToken string
	Code        string
}

func GetUserInfo(params *LoginParams) (userAccount *sdkvar.SdkUserInfo, err error) {
	var iLogin ILogin
	switch params.AccType {
	case commonPB.LOGIN_TYPE_LT_FACEBOOK:
		iLogin = new(facebook.FBLogin)
	case commonPB.LOGIN_TYPE_LT_GOOGLE:
		iLogin = new(google.GoogleLogin)
	case commonPB.LOGIN_TYPE_LT_APPLE:
		iLogin = new(applestore.AppleLogin)
	default:
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_TYPE)
	}
	return iLogin.GetUserAccount(params.ChannelID, params.AccessToken, params.Code)
}
