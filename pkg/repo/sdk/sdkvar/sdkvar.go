package sdkvar

import commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"

// SdkUserInfo SDK调用相关参数
type SdkUserInfo struct {
	OpenID     string `json:"OpenID"`
	Name       string `json:"Name"`
	Avatar     string `json:"Avatar"`
	Country    string `json:"Country"`
	Email      string `json:"Email"`
	AppleToken string `json:"AppleToken"`
}

// PayVerifyParams 支付校验参数
type PayVerifyParams struct {
	// 必须参数
	ProductId        commonPB.PRODUCT_ID // 产品ID
	CommodityID      string              // 第三方商品ID
	Token            string
	AppleReceiptData string

	// 其它参数
	OrderID    int64             // 内部订单ID
	Uid        uint64            // 玩家ID
	PayChannel commonPB.PAY_TYPE // 支付类型
	AppVersion string            // 玩家App版本
}

// PayVerifyRsp 支付校验返回
type PayVerifyRsp struct {
	ProductId            commonPB.PRODUCT_ID // 产品ID
	PayChannel           commonPB.PAY_TYPE   // 支付类型
	OrderID              int64               // 后端订单ID
	SdkOrderID           string              // SDK订单ID
	CanDeliver           bool                // 校验结果，是否能发货
	ReqUrl               string              // 校验url
	SdkPurchaseState     int                 // SDK返回订单状态
	ConsumptionState     int                 // 消费状态
	AcknowledgementState int                 // 回执 Google
	RetErrorCode         int32               // 回执 处理结果状态码
	RetError             string              // 回执 处理结果
	DealRet              string              // 处理结果转成Json字符串 （google.PurchaseRsp  applestore.AppleVerifyReceiptResponse）
	TransactionID        string              // 订单处理第三方流水号，或唯一标识，Google支付返回
	IsTestPay            bool                // 是否测试账号
	RegionCode           string              // 国家
	CurrencyCode         string              // 货币代码
	PriceAmountMicros    int64               // 当前支付金额
	PurchaseTime         int64               // 支付时间
}
