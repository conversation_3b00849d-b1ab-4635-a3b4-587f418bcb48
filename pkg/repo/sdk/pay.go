package sdk

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/applestore"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/sdkvar"
	"github.com/sirupsen/logrus"
)

type IPay interface {
	VerifyOrder(params *sdkvar.PayVerifyParams) (*sdkvar.PayVerifyRsp, error)
}

// Verify 校验订单
func Verify(payChannel commonPB.PAY_TYPE, params *sdkvar.PayVerifyParams) (rsp *sdkvar.PayVerifyRsp, err error) {
	var iPay IPay
	switch payChannel {
	case commonPB.PAY_TYPE_PT_APPLE:
		iPay = new(applestore.IApplePayImpl)
	case commonPB.PAY_TYPE_PT_GOOGLE:
		// iPay = new(google.IGooglePayImpl)
	default:
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_PAY_CHANNEL_NOT_EXIST)
	}

	logrus.Tracef("VerifyOrder params : %v", params)

	rsp, err = iPay.VerifyOrder(params)
	if err != nil {
		logrus.Errorf("verify err : %v", err)
		return rsp, err
	}

	return rsp, nil
}
