package applestore

const (
	HostSandBox  = "https://sandbox.itunes.apple.com/verifyReceipt"
	HostRelease  = "https://buy.itunes.apple.com/verifyReceipt"
	PkgName      = "com.playsparkle.apps.mt.ios"
	ClientID     = "1626967967"
	AuthTokenURL = "https://appleid.apple.com/auth/token"
	KeyID        = "6U85ZUQ8TD"
	TeamID       = "X2T26K95ZU"

	secret = `*****************************************************************************************************************************************************************************************************************************************************************`
)

// 服务器二次验证代码
const (
	ERR_21000 = 21000 //* 21000 App Store不能读取你提供的JSON对象
	ERR_21002 = 21002 //* 21002 receipt-data域的数据有问题
	ERR_21003 = 21003 //* 21003 receipt无法通过验证
	ERR_21004 = 21004 //* 21004 提供的shared secret不匹配你账号中的shared secret
	ERR_21005 = 21005 //* 21005 receipt服务器当前不可用
	ERR_21006 = 21006 //* 21006 receipt合法，但是订阅已过期。服务器接收到这个状态码时，receipt数据仍然会解码并一起发送
	ERR_21007 = 21007 //* 21007 receipt是Sandbox receipt，但却发送至生产系统的验证服务
	ERR_21008 = 21008 //* 21008 receipt是生产receipt，但却发送至Sandbox环境的验证服务
)
