package applestore

import (
	"encoding/json"
	"errors"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/sdkvar"
	"git.keepfancy.xyz/back-end/frameworks/lib/httpx"
	"github.com/sirupsen/logrus"
)

type IApplePayImpl struct{}

// VerifyOrder https://developer.apple.com/documentation/appstorereceipts/verifyreceipt
func (I IApplePayImpl) VerifyOrder(params *sdkvar.PayVerifyParams) (*sdkvar.PayVerifyRsp, error) {

	rspRet := &sdkvar.PayVerifyRsp{
		ProductId:  params.ProductId,
		PayChannel: params.PayChannel,
		OrderID:    params.OrderID,
	}

	clientJson := &ClientIAPRsp{}
	if err := json.Unmarshal([]byte(params.Token), clientJson); err != nil {
		logrus.Errorf("Json解析失败 %v", err)
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_JSON_PARSE_ERROR)
	}

	// 从正式服校验
	rspRet.ReqUrl = HostRelease
	var verifyRet *AppleVerifyReceiptResponse
	var verifyErr error
	verifyRet, verifyErr = I.appleVerifyReceipt(HostRelease, clientJson.Payload)
	var dealJson []byte
	dealJson, _ = json.Marshal(verifyRet)
	rspRet.DealRet = string(dealJson)

	if verifyErr != nil {
		logrus.Errorf("[AppleVerify]appleVerify release post error %v", verifyRet)
		return rspRet, verifyErr
	}

	if verifyRet.Status != 0 {
		if verifyRet.Status == ERR_21007 {
			logrus.Info("状态码校验Status 为沙盒账号")
		} else {
			logrus.Error("玩家%d状态码校验Status ：%d", params.Uid, verifyRet.Status)
		}
		switch verifyRet.Status {
		case ERR_21007:
			// 沙箱环境订单
			rspRet.ReqUrl = HostSandBox
			verifyRet, verifyErr = I.appleVerifyReceipt(HostSandBox, clientJson.Payload)
			dealJson, _ = json.Marshal(verifyRet)

			if verifyErr != nil {
				logrus.Errorf("[AppleVerify]Sandbox appleVerify post error %v", verifyRet)
				rspRet.CanDeliver = false
				rspRet.DealRet = string(dealJson)
				return rspRet, verifyErr
			}
			rspRet.IsTestPay = true // 是测试订单
		case ERR_21002:
			logrus.Errorf("uid:%d,receipt-data was invalid~", params.Uid)
			rspRet.CanDeliver = false
			return rspRet, errors.New("receipt-data was invalid")
		}
	}

	if len(verifyRet.Receipt.InAPP) == 0 {
		logrus.Errorf("uid:%d,AppleStore InApp was empty~", params.Uid)
		rspRet.CanDeliver = false
		return rspRet, errors.New("AppleStore InApp was empty")
	}

	inApp := verifyRet.Receipt.InAPP[0]
	if inApp.ProductID != params.CommodityID {
		logrus.Warnf("%d : AppleStore InApp ProductID diff ~", params.Uid)
	}

	rspRet.CanDeliver = true
	rspRet.SdkPurchaseState = 0
	rspRet.SdkOrderID = inApp.TransactionID // 对应应用内OrderID 透传
	rspRet.ConsumptionState = 0
	rspRet.AcknowledgementState = 0

	return rspRet, nil
}

func (I IApplePayImpl) appleVerifyReceipt(url string, receipt string) (*AppleVerifyReceiptResponse, error) {
	data := make(map[string]string)
	data["receipt-data"] = receipt
	ret, err := httpx.PostBackJsonByMap(url, data)
	if err != nil {
		logrus.Errorf("[AppleVerify]GoogleVerifyReceipt error[%v]", data)
		return nil, err
	}

	rsp := &AppleVerifyReceiptResponse{}
	if err = json.Unmarshal(ret, rsp); err != nil {
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_JSON_PARSE_ERROR)
	}
	return rsp, nil
}
