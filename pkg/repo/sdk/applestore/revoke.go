package applestore

import (
	"context"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/applestore/lib"
)

const AppleRevokeUrl = "https://appleid.apple.com/auth/revoke"

func AppleRevokeToken(token string) error {
	secret, err := lib.GenerateClientSecret(secret, TeamID, PkgName, KeyID)
	if err != nil {
		return err
	}

	// Generate a new validation client
	client := lib.NewWithURL(AppleRevokeUrl)

	vReq := lib.AppRevokeTokenRequest{
		ClientID:     PkgName,
		ClientSecret: secret,
		Token:        token,
	}

	var resp lib.RevokeResponse

	// Do the verification
	err = client.VerifyAppRevokeToken(context.Background(), vReq, &resp)
	if err != nil {
		fmt.Println("error verifying: " + err.Error())
		return err
	}

	if resp.Error != "" {
		fmt.Printf("apple  revoke returned an error: %s \n", resp.Error)
		return fmt.Errorf("apple rsp err:%s", resp.Error)
	}

	return nil
}
