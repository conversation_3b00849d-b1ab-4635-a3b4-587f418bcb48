package applestore

import "net/url"

// ClientIAPRsp 前端iOS Json解析
type ClientIAPRsp struct {
	Payload       string `json:"Payload"`
	StoreStr      string `json:"Store"`
	TransactionID string `json:"TransactionID"`
}

type AppleVerifyReceiptResponse struct {
	// The environment for which the receipt was generated.
	// Possible values: Sandbox, Production
	Environment string `json:"environment"`

	// An indicator that an error occurred during the request. A value of 1 indicates a temporary issue; retry validation for this receipt at a later time.
	// A value of 0 indicates an unresolvable issue; do not retry validation for this receipt. Only applicable to status codes 21100-21199.
	IsRetryable bool `json:"is-retryable"`

	// A JSON representation of the receipt that was sent for verification.
	Receipt struct {
		BundleID string `json:"bundle_id"`
		InAPP    []struct {
			ProductID      string `json:"product_id"`
			TransactionID  string `json:"transaction_id"`
			PurchaseDateMS string `json:"purchase_date_ms"`
		} `json:"in_app"`
	} `json:"receipt"`

	// Either 0 if the receipt is valid, or a status code if there is an error.
	// The status code reflects the status of the app receipt as a whole. See status for possible status codes and descriptions.
	// https://developer.apple.com/documentation/appstorereceipts/status
	Status int `json:"status"`
}

type AppleAuthTokenGrantType string

const (
	AppleAuthTokenAuthorizationCode = "authorization_code" // 检验Token
	// AppleAuthTokenRefreshToken      = "refresh_token" //刷新token
)

// AppleAuthTokenReq token检验或刷新请求
type AppleAuthTokenReq struct {
	ClientID     string                  `json:"client_id"`     // 必填
	ClientSecret string                  `json:"client_secret"` // 必填
	Code         string                  `json:"code"`
	GrantType    AppleAuthTokenGrantType `json:"grant_type"` // 校验为 auth
	RefreshToken string                  `json:"refresh_token"`
	RedirectURI  string                  `json:"redirect_uri"`
}

func (a *AppleAuthTokenReq) GetParams() url.Values {
	param := url.Values{}
	param.Add("client_id", a.ClientID)
	param.Add("client_secret", a.ClientSecret)
	param.Add("code", a.Code)
	param.Add("grant_type", string(a.GrantType))
	// param.Add("refresh_token", a.RefreshToken)
	// param.Add("redirect_uri", a.RedirectURI)
	return param
}

// AppleAuthTokenRsp token校验回包
// URL https://appleid.apple.com/auth/token
type AppleAuthTokenRsp struct {
	Err          string `json:"error"` // 错误描述
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	IDToken      string `json:"id_token"`
}
