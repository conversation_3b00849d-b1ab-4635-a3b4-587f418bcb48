package lib

import (
	"testing"

	"github.com/tideland/gorest/jwt"

	"github.com/stretchr/testify/assert"
)

func TestGenerateClientSecret(t *testing.T) {
	testGoodKey := `*****************************************************************************************************************************************************************************************************************************************************************` // A revoked key that can be used for testing

	testWrongKey := `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************` // Wrong format - this is PKCS1

	tests := []struct {
		name       string
		signingKey string
		wantSecret bool
		wantErr    bool
	}{
		{
			name:       "bad key",
			signingKey: "bad_key",
			wantSecret: false,
			wantErr:    true,
		},
		{
			name:       "bad key wrong format",
			signingKey: testWrongKey,
			wantSecret: false,
			wantErr:    true,
		},
		{
			name:       "good key",
			signingKey: testGoodKey,
			wantSecret: true,
			wantErr:    false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GenerateClientSecret(tt.signingKey, "1234567890", "com.example.app", "0987654321")
			if !tt.wantErr {
				assert.NoError(t, err, "expected no error but got %s", err)
			}
			if tt.wantSecret {
				assert.NotEmpty(t, got, "wanted a secret string returned but got none")

				decoded, err := jwt.Decode(got)
				assert.NoError(t, err, "error while decoding the secret")

				r, b := decoded.Claims().Issuer()
				assert.True(t, b, "invalid issuer")
				assert.Equal(t, "1234567890", r)

				r, b = decoded.Claims().Subject()
				assert.True(t, b, "invalid subject")
				assert.Equal(t, "com.example.app", r)

				assert.Equal(t, jwt.ES256, decoded.Algorithm())
			}
		})
	}
}
