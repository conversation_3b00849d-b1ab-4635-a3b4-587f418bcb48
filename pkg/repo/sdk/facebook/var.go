package facebook

type Error struct {
	Code      int32  `json:"code"`
	Message   string `json:"message"`
	Type      string `json:"type"`
	FBTraceID string `json:"fbtrace_id"`
}

type UserInfo struct {
	Error Error   `json:"error"`
	Name  string  `json:"name"`
	ID    string  `json:"id"`
	Pic   Picture `json:"picture"`
	Email string  `json:"email"`
}

type Picture struct {
	Data struct {
		Height       int32  `json:"height"`
		Width        int32  `json:"width"`
		IsSilhouette bool   `json:"is_silhouette"`
		URL          string `json:"url"`
	} `json:"data"`
}

type PictureRsp struct {
	Error Error `json:"error"`
	Picture
}

type FriendRsp struct {
	Error   Error        `json:"error"`
	Data    []FriendItem `json:"data"`    //好友信息列表
	Paging  Paging       `json:"paging"`  //分页信息
	Summary Summary      `json:"summary"` //记录总数
}

type FriendItem struct {
	ID      string  `json:"id"`      //facebook id
	Name    string  `json:"name"`    //昵称
	Picture Picture `json:"picture"` //头像信息
}

type Cursors struct {
	Before string `json:"before"` //上一个游标
	After  string `json:"after"`  //下一个游标
}

type Paging struct {
	Cursors  Cursors `json:"cursors"`  //游标信息
	Previous string  `json:"previous"` //上一页地址，如果为空则是第一页
	Next     string  `json:"next"`     //下一页地址，如果为空则是最后一页
}

type Summary struct {
	TotalCount int32 `json:"total_count"` //记录总数
}

type FinderRsp struct {
	Error  Error        `json:"error"`
	Data   []FinderItem `json:"data"`   //好友信息列表
	Paging Paging       `json:"paging"` //分页信息
}

type FinderItem struct {
	Application ApplicationInfo `json:"application"`
	CreatedTime string          `json:"created_time"`
	From        FromInfo        `json:"from"`
	Message     string          `json:"message"`
	To          ToInfo          `json:"to"`
	ID          string          `json:"id"`
}

type FromInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type ToInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type ApplicationInfo struct {
	Name string `json:"name"`
	Id   string `json:"id"`
}
