package facebook

import (
	"encoding/json"
	"log"
	"testing"
)

func TestGetUserFriends(t *testing.T) {
	accessToken := "EAAd6E15hmfABO0KLr5vLIPd6c5cs6N4CTqjaqdrLRKnxMcZAFFWCAmKtSjZCVdnbkJrYAbNIo9XIbKpQL9nI9aeAnClhwNRcHHUOQnyNtqdZCBRcodaFfgjXcR3tLVBHiCFhNGvkhhnOrNeWjXZCDvfC7aZAjGPjHDUJioxYpxvNuV9h6krQ3tFP7HaEE2tmG1jifCfeLyb9wxCcdazT8deeJplTiqjG6zn1q9hYgnEZBVom5UZCkMoDaRblcEVUqkZD"
	//now := time.Now()
	rsp, err := GetFriends(accessToken, 10, "")
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetFriends err:%s", err)
		return
	}
	js, _ := json.Marshal(rsp)
	log.Printf("friendList:%s", js)
}

func TestGetFriendsFinder(t *testing.T) {
	accessToken := "GGQVlhQ0ZAlOXlxaFIycDBqM2ZAvdzY4QlkwR2dGMXlTVWs5anp6dnNDS2dZAdTVaQkx4cEcxeVZAjbU5DeVVQdnVjX3dFVjJSV3hCRmlWMmN3V0dLS0VockRGa25nVmtwRkNRTmU1UExrel9FUFpIeDNyUll2STBEa1l2VlRWbDhfcGRHV1BPQlI5Vk50UjNnbnhMRjJTS05KMG56a3NwM3VScwZDZD"
	//now := time.Now()
	rsp, err := GetFinder(accessToken)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetFriends err:%s", err)
		return
	}
	js, _ := json.Marshal(rsp)
	log.Printf("friendList:%s", js)
}
