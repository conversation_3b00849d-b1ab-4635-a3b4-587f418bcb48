package facebook

import (
	"encoding/json"
	"net/url"
	"regexp"
	"strconv"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk/sdkvar"
	"git.keepfancy.xyz/back-end/frameworks/lib/httpx"
	"github.com/sirupsen/logrus"
)

type FBLogin struct {
}

func (fb *FBLogin) GetUserAccount(channelID commonPB.CHANNEL_TYPE, accessToken string, code string) (*sdkvar.SdkUserInfo, error) {
	logrus.Infof("GetUserAccount called with: accessToken=%s, authorization=%s",
		maskToken(accessToken), maskToken(code))

	// 获取用户信息
	fbUserInfo, err := getUserInfo(channelID, accessToken)
	if err != nil {
		logrus.Errorf("facebook get user info error: %v", err)
		return nil, err
	}

	userInfo := &sdkvar.SdkUserInfo{
		Name:   fbUserInfo.Name,
		Avatar: fbUserInfo.Pic.Data.URL,
		OpenID: fbUserInfo.ID,
		Email:  fbUserInfo.Email,
	}

	logrus.Infof("Successfully got user info: name=%s, openid=%s", userInfo.Name, userInfo.OpenID)
	return userInfo, nil
}

// getUserInfo 获取facebook信息
func getUserInfo(channelID commonPB.CHANNEL_TYPE, accessToken string) (userInfo *UserInfo, err error) {
	params := url.Values{}
	params.Set("access_token", accessToken)

	// 生成 app secret proof
	appSecretProof := GenerateAppSecretProof(channelID, accessToken)
	if appSecretProof == "" {
		logrus.Errorf("failed to generate app secret proof")
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}
	params.Set("appsecret_proof", appSecretProof)
	params.Set("fields", "id,name,picture,email")

	logrus.Debugf("facebook get user info request: url=%s, params=%v", UserInfoURL, params)

	data, err := httpx.GetBackJson(UserInfoURL, params)
	if err != nil {
		logrus.Errorf("facebook get user info http error: %v", err)
		return nil, err
	}

	logrus.Debugf("facebook get user info response: %s", maskSensitiveInfo(string(data)))

	userInfo = &UserInfo{}
	if err = json.Unmarshal(data, userInfo); err != nil {
		logrus.Errorf("facebook unmarshal user info error: %v", err)
		return nil, err
	}

	if userInfo.Error.Code != 0 {
		logrus.Errorf("facebook get user info error: code=%d, message=%s, type=%s",
			userInfo.Error.Code, userInfo.Error.Message, userInfo.Error.Type)
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}

	return
}

// maskToken 对令牌进行掩码处理以便安全记录
func maskToken(token string) string {
	if token == "" {
		return ""
	}
	if len(token) <= 8 {
		return "****"
	}
	return token[:4] + "..." + token[len(token)-4:]
}

// maskSensitiveInfo 对响应中的敏感信息进行掩码处理
func maskSensitiveInfo(jsonStr string) string {
	// 简单替换访问令牌
	jsonStr = regexp.MustCompile(`"access_token":"[^"]+"`).ReplaceAllString(jsonStr, `"access_token":"***"`)
	return jsonStr
}

// GetFriends 获取好友信息
// @accessToken 用户fbtoken
// @limit 分页数量
// @after 下一页请求地址
func GetFriends(accessToken string, limit int64, after string) (frdRsp *FriendRsp, err error) {
	params := url.Values{}
	params.Set("access_token", accessToken)
	params.Set("limit", strconv.Itoa(int(limit)))
	params.Set("after", after)
	data, err := httpx.GetBackJson(FriendURL, params)
	if err != nil {
		return
	}
	frdRsp = &FriendRsp{}
	if err = json.Unmarshal(data, frdRsp); err != nil {
		return
	}

	if frdRsp.Error.Code != 0 {
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}
	return
}

// GetFinder 获取邀请信息
// @accessToken 用户token
func GetFinder(accessToken string) (fidRsp *FinderRsp, err error) {
	params := url.Values{}
	params.Set("access_token", accessToken)
	params.Set("limit", GetFinderLimit)
	data, err := httpx.GetBackJson(FinderURL, params)
	if err != nil {
		return
	}
	fidRsp = &FinderRsp{}
	if err = json.Unmarshal(data, fidRsp); err != nil {
		return
	}

	if fidRsp.Error.Code != 0 {
		return nil, protox.PB2Error(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO)
	}
	return
}
