package game_time

import (
	"fmt"
	"math"
)

const NatureDay = 3600

var BaseTime int64 = 0
var Year int64 = 365

var MinDur float64
var HourDur float64

var DayPeriod = 4                                   // 每天N个时段
var initialYear, initialMonth, initialDay = 1, 1, 1 // 钓鱼元年

func init() {
	HourDur = NatureDay / 24
	MinDur = HourDur / 60
}

type GameTime struct {
	Day    int64
	Hour   int64
	Min    int64
	Period int64 // 当天第N时段
}

// ToGameTime 转换游戏时间
func ToGameTime(t int64) *GameTime {
	durTime := t - BaseTime
	day := durTime / NatureDay
	reset := float64(durTime % NatureDay)
	hour := math.Floor(reset / HourDur)
	minx := math.Floor((reset - HourDur*hour) / MinDur)
	gt := &GameTime{
		Day:  day,
		Hour: int64(hour),
		Min:  int64(minx),
	}
	gt.updatePeriod()
	return gt
}

func (g *GameTime) updatePeriod() {
	g.Period = int64(g.Hour) / 4
}

// ToTimeZone 时区转换
func (g *GameTime) ToTimeZone(tz int64) *GameTime {
	ng := &GameTime{
		Day:  g.Day,
		Hour: g.Hour,
		Min:  g.Min,
	}

	if ng.Hour+tz >= 24 {
		ng.Hour = ng.Hour + tz - 24
		ng.Day = ng.Day + 1
	} else if ng.Hour+tz < 0 {
		ng.Hour = ng.Hour + tz + 24
		ng.Day = ng.Day - 1
	}
	ng.updatePeriod()
	return ng
}

// GetPeriod 获取当前时间段
func (g *GameTime) GetPeriod() int64 {
	return g.Period
}

func (g *GameTime) GetDay() int64 {
	return g.Day
}

func (g *GameTime) GetHour() int64 {
	return g.Hour
}

func (g *GameTime) GetMinute() int64 {
	return g.Min
}

func (g *GameTime) GetTimeStr() string {
	year, month, day := addDaysToDate(initialYear, initialMonth, initialDay, int(g.Day))

	return fmt.Sprintf("%d-%d-%d %d:%d", year, month, day, g.Hour, g.Min)
}

// isLeapYear 判断给定的年份是否为闰年
func isLeapYear(year int) bool {
	return year%4 == 0 && (year%100 != 0 || year%400 == 0)
}

// daysInMonth 返回给定年月的天数
func daysInMonth(year, month int) int {
	switch month {
	case 2: // 二月
		if isLeapYear(year) {
			return 29
		}
		return 28
	case 4, 6, 9, 11: // 四月、六月、九月、十一月
		return 30
	default: // 一月、三月、五月、七月、八月、十月、十二月
		return 31
	}
}

// addDaysToDate 计算过了n天后的年和月
func addDaysToDate(year, month, day, n int) (int, int, int) {
	for n > 0 {
		daysInCurrMonth := daysInMonth(year, month)
		if day+1 > daysInCurrMonth { // 如果加上n天超过了当前月的最大天数
			day = 1          // 重置日为1，进入下个月
			if month == 12 { // 如果是12月，则年份加1，月份变为1
				month = 1
				year++
			} else { // 否则月份加1
				month++
			}
		} else {
			day++
		}
		n--
	}
	return year, month, day
}
