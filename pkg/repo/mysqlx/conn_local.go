package mysqlx

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/env"
	"github.com/spf13/viper"
)

func InitMysqlAssetDbDevEnv() {

	if env.DeployEnv != env.DeployEnvDev {
		return
	}

	confAsset := map[string]interface{}{
		"addr":   "************:3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     dict_mysql.MysqlDBAsset,
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		dict_mysql.MysqlDBAsset: confAsset,
	})
}

func InitMysqlGeneralDbDevEnv() {

	if env.DeployEnv != env.DeployEnvDev {
		return
	}

	confMail := map[string]interface{}{
		"addr":   "************:3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     dict_mysql.MysqlDBGeneral,
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		dict_mysql.MysqlDBGeneral: confMail,
	})
}
