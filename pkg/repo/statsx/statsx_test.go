package statsx

import (
	"testing"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
)

func TestSumStatsAdd(t *testing.T) {
	stats := NewStats(1)
	stats.Progress = 5
	{
		typ := commonPB.SUM_ADD_SA_ADD
		var val int64 = 10
		t.Logf("old stats:%+v", stats)
		isChange := stats.SumStats(typ, val)
		t.Logf("type:%+v val:%+v stats:%+v isChange:%+v", typ, val, stats, isChange)
	}
	{
		typ := commonPB.SUM_ADD_SA_MAX
		var val int64 = 99
		t.Logf("old stats:%+v", stats)
		isChange := stats.SumStats(typ, val)
		t.Logf("type:%+v val:%+v stats:%+v isChange:%+v", typ, val, stats, isChange)
	}
	{
		typ := commonPB.SUM_ADD_SA_MIN
		var val int64 = 7
		t.Logf("old stats:%+v", stats)
		isChange := stats.SumStats(typ, val)
		t.Logf("type:%+v val:%+v stats:%+v isChange:%+v", typ, val, stats, isChange)
	}
	{
		typ := commonPB.SUM_ADD_SA_VAL
		var val int64 = 666
		t.Logf("old stats:%+v", stats)
		isChange := stats.SumStats(typ, val)
		t.Logf("type:%+v val:%+v stats:%+v isChange:%+v", typ, val, stats, isChange)
	}

}

func TestSumStatsUni(t *testing.T) {
	stats := NewStats(1)
	stats.Stash = append(stats.Stash, 3, 4, 5)
	// 除重累计
	{
		typ := commonPB.SUM_ADD_SA_UNI_ADD
		var val int64 = 3
		t.Logf("old stats:%+v", stats)
		isChange := stats.SumStats(typ, val)
		t.Logf("type:%+v val:%+v stats:%+v isChange:%+v", typ, val, stats, isChange)
	}
	{
		typ := commonPB.SUM_ADD_SA_UNI_ADD
		var val int64 = 7
		t.Logf("old stats:%+v", stats)
		isChange := stats.SumStats(typ, val)
		t.Logf("type:%+v val:%+v stats:%+v isChange:%+v", typ, val, stats, isChange)
	}
}

func TestSumStatsDayContinue(t *testing.T) {
	stats := NewStats(1)
	now := timex.Now()
	// 除重累计
	{
		typ := commonPB.SUM_ADD_SA_CONTINUE_DAY
		t.Logf("old stats:%+v", stats)
		isChange := stats.SumStats(typ, now.Unix())
		t.Logf("type:%+v val:%+v stats:%+v isChange:%+v", typ, now, stats, isChange)
	}
	{
		typ := commonPB.SUM_ADD_SA_CONTINUE_DAY
		now = now.Add(86400 * time.Second)
		t.Logf("old stats:%+v", stats)
		isChange := stats.SumStats(typ, now.Unix())
		t.Logf("type:%+v val:%+v stats:%+v isChange:%+v", typ, now, stats, isChange)
	}
	{
		typ := commonPB.SUM_ADD_SA_CONTINUE_DAY
		t.Logf("old stats:%+v", stats)
		isChange := stats.SumStats(typ, now.Unix())
		t.Logf("type:%+v val:%+v stats:%+v isChange:%+v", typ, now, stats, isChange)
	}
	{
		typ := commonPB.SUM_ADD_SA_CONTINUE_DAY
		now = now.Add(2 * 86400 * time.Second)
		t.Logf("old stats:%+v", stats)
		isChange := stats.SumStats(typ, now.Unix())
		t.Logf("type:%+v val:%+v stats:%+v isChange:%+v", typ, now, stats, isChange)
	}
}
