package statsx

import (
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/lib/mathx"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/sirupsen/logrus"
)

// 通用统计模块 (数据量比较多，用缩写标记json)
type Stats struct {
	Id       int64   `json:"i"` // id key
	Progress int64   `json:"p"` // progress 进度
	Stash    []int64 `json:"s"` // stash 缓存区
}

func NewStats(id int64) *Stats {
	return &Stats{
		Id:    id,
		Stash: make([]int64, 0),
	}
}

// 统计更新进度
func (stats *Stats) SumStats(addType commonPB.SUM_ADD, val int64) (change bool) {
	switch addType {
	case commonPB.SUM_ADD_SA_ADD:
		// 累计
		stats.Progress += val
		return val != 0
	case commonPB.SUM_ADD_SA_VAL:
		// 直接更新值
		stats.Progress = val
		return true
	case commonPB.SUM_ADD_SA_MAX:
		// 最大值
		if val != stats.Progress {
			stats.Progress = mathx.Max(stats.Progress, val)
			return true
		}
	case commonPB.SUM_ADD_SA_MIN:
		// 最小值
		if val != stats.Progress {
			stats.Progress = mathx.Min(stats.Progress, val)
			return true
		}
	case commonPB.SUM_ADD_SA_UNI_ADD:
		// 除重累计
		if stats.Stash == nil {
			logrus.Warnf("sumStats check nil:%+v", stats)
			return false
		}

		// 有相同元素, 则跳过
		for _, old := range stats.Stash {
			if val == old {
				return false
			}
		}
		stats.Stash = append(stats.Stash, val)
		stats.Progress = int64(len(stats.Stash))
		return true
	case commonPB.SUM_ADD_SA_CONTINUE_DAY:
		// 默认传递val为日期值 （20060102）
		// 日期连续
		if stats.Stash == nil {
			logrus.Warnf("sumStats check nil:%+v", stats)
			return false
		}
		if val == 0 {
			logrus.Warnf("sumStats check time is zero :%+v", stats)
			return false
		}
		dayFormat := "20060102"

		dayVal := val

		// tx := time.Unix(val, 0)
		// times := tx.Format(dayFormat)
		// dayVal := transform.ToInt64(times)
		// 空表直接塞
		if len(stats.Stash) == 0 {
			stats.Stash = append(stats.Stash, dayVal)
			stats.Progress = int64(len(stats.Stash))
			return true
		} else {
			lastOldDay := stats.Stash[len(stats.Stash)-1]
			// 缓存区最后一天是当天,跳过
			if lastOldDay == dayVal {
				return false
			}
			lastDayTime, _ := time.Parse(dayFormat, transform.Int642Str(val))
			lastDayTime = lastDayTime.AddDate(0, 0, -1)
			lastTs := lastDayTime.Format(dayFormat)
			lastDay := transform.ToInt64(lastTs)

			// 比较昨天是不是缓存区最后一天
			if lastDay == lastOldDay {
				stats.Stash = append(stats.Stash, dayVal)
				stats.Progress = int64(len(stats.Stash))
				return true
			} else {
				// 重置
				stats.Stash = make([]int64, 0)
				stats.Progress = int64(len(stats.Stash))
				return true
			}
		}
	default:
		logrus.Errorf("unKnown addType:%d stats:%+v", addType, stats)
	}
	return false
}
