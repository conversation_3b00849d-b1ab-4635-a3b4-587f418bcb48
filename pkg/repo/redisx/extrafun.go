package redisx

import (
	"context"
	"time"

	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/go-redis/redis/v8"
)

type extraCmdAble func(ctx context.Context, cmd redis.Cmder) error

const FILLER_NIL = "nil"

func formatSec(ctx context.Context, dur time.Duration) int64 {
	if dur > 0 && dur < time.Second {
		// internal.Logger.Printf(
		// 	ctx,
		// 	"specified duration is %s, but minimal supported value is %s - truncating to 1s",
		// 	dur, time.Second,
		// )
		return 1
	}
	return int64(dur / time.Second)
}

// RandExpire 随机过期
func (c extraCmdAble) RandExpire(ctx context.Context, key string, expiration int64, randExport int64) *redis.BoolCmd {
	var rand int64
	// // 过期时间随机扰动
	if randExport != 0 {
		rand = random.Int64n(randExport)
	}
	args := make([]interface{}, 3, 4)
	args[0] = "expire"
	args[1] = key
	args[2] = formatSec(ctx, time.Duration((expiration+rand)*int64(time.Second)))
	cmd := redis.NewBoolCmd(ctx, args...)
	_ = c(ctx, cmd)
	return cmd
}

// HSet 塞空标记内容
func (c *Client) HSetNil(ctx context.Context, key string) error {
	pipe := c.Pipeline()
	_ = redis.Nil
	pipe.HSetNX(ctx, key, FILLER_NIL, FILLER_NIL)
	rand := random.Int64n(86400)
	pipe.Expire(ctx, key, 5*time.Minute+time.Duration(rand))
	_, err := pipe.Exec(ctx)
	return err
}

// HGetAllWithNil 获取hash数据 若有空标记则剔除
// 通常使用CacheAside 模式不会出现Nil标记与其他内容共存的情况
func (c *Client) HGetAllWithNil(ctx context.Context, key string) *redis.StringStringMapCmd {
	result := c.HGetAll(ctx, key)
	if len(result.Val()) == 1 {
		// 剔除无效内容
		if _, ok := result.Val()[FILLER_NIL]; ok {
			result.SetVal(map[string]string{})
			result.SetErr(Empty)
			return result
		}
	}
	return result
}

func (c *Client) HGetWithNil(ctx context.Context, key string, field string) *redis.StringCmd {
	rtn := redis.NewStringCmd(ctx, key, field)
	data, err := c.HMGet(ctx, key, field, FILLER_NIL).Result()
	if err != nil {
		rtn.SetErr(err)
		return rtn
	}
	if data[0] != nil {
		rtn.SetVal(data[0].(string))
		return rtn
	}
	// 携带空标记，拦截数据
	if data[1] != nil {
		rtn.SetErr(Empty)
		return rtn
	}
	// key不存在
	if data[0] == nil && data[1] == nil {
		rtn.SetErr(redis.Nil)
		return rtn
	}
	return rtn
}
