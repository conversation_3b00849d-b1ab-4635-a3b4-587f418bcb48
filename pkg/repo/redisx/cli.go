package redisx

import (
	"git.keepfancy.xyz/back-end/frameworks/kit/redisfactory"
	"github.com/go-redis/redis/v8"
)

func GetRedisCli(dbName string, dbIndex int) *Client {
	cli := redisfactory.GetRedisClient(dbName, dbIndex)
	return &Client{
		Client:       cli,
		extraCmdAble: cli.Process,
	}
}

type Client struct {
	*redis.Client
	extraCmdAble
}

func (c *Client) Pipeline() *Pipeline {
	old := c.Client.Pipeline().(*redis.Pipeline) // 映射回来
	return &Pipeline{
		Pipeline:     old,
		extraCmdAble: old.Process,
	}
}

type Pipeline struct {
	extraCmdAble
	*redis.Pipeline
}

func (c *Client) TxPipeline() *Pipeline {
	old := c.Client.TxPipeline().(*redis.Pipeline) // 映射回来
	return &Pipeline{
		Pipeline:     old,
		extraCmdAble: old.Process,
	}
}
