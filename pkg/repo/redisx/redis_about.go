package redisx

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
)

func GetPlayerCli() *Client {
	return GetRedisCli(dict_redis.RDBPlayer, dict_redis.RDBPlayerIndex)
}

func GetGatewayCli() *Client {
	return GetRedisCli(dict_redis.RDBGateway, dict_redis.RDBGatewayIndex)
}

func GetAssetCli() *Client {
	return GetRedisCli(dict_redis.RDBAsset, dict_redis.RDBAssetIndex)
}

func GetRoomCli() *Client {
	return GetRedisCli(dict_redis.RDBRoom, dict_redis.RDBRoomIndex)
}

func GetGameCli() *Client {
	return GetRedisCli(dict_redis.RDBGame, dict_redis.RDBGameIndex)
}

func GetTaskCli() *Client {
	return GetRedisCli(dict_redis.RDBTask, dict_redis.RDBTaskIndex)
}

func GetGeneralCli() *Client {
	return GetRedisCli(dict_redis.RDBGeneral, dict_redis.RDBGeneralIndex)
}

func GetBizCli() *Client {
	return GetRedisCli(dict_redis.RDBBiz, dict_redis.RDBBizIndex)
}

func GetLockCli() *Client {
	return GetRedisCli(dict_redis.RDBLock, dict_redis.RDBLockIndex)
}