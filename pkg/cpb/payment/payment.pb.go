// 支付模块协议

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: payment.proto

package paymentPB

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 支付下单
type CreatePurchaseOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductID    common.PRODUCT_ID             `protobuf:"varint,1,opt,name=ProductID,proto3,enum=common.PRODUCT_ID" json:"ProductID,omitempty"`                   // 产品ID
	PurchaseID   int32                         `protobuf:"varint,2,opt,name=PurchaseID,proto3" json:"PurchaseID,omitempty"`                                        // 支付ID(对应配置文件)
	CommodityID  string                        `protobuf:"bytes,3,opt,name=CommodityID,proto3" json:"CommodityID,omitempty"`                                       // 支付渠道商品ID
	EntranceType common.PURCHASE_ENTRANCE_TYPE `protobuf:"varint,4,opt,name=EntranceType,proto3,enum=common.PURCHASE_ENTRANCE_TYPE" json:"EntranceType,omitempty"` // 支付下单入口类型
	TriggerType  common.PURCHASE_TRIGGER_TYPE  `protobuf:"varint,5,opt,name=TriggerType,proto3,enum=common.PURCHASE_TRIGGER_TYPE" json:"TriggerType,omitempty"`    // 触发支付入口原因
}

func (x *CreatePurchaseOrderReq) Reset() {
	*x = CreatePurchaseOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePurchaseOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePurchaseOrderReq) ProtoMessage() {}

func (x *CreatePurchaseOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_payment_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePurchaseOrderReq.ProtoReflect.Descriptor instead.
func (*CreatePurchaseOrderReq) Descriptor() ([]byte, []int) {
	return file_payment_proto_rawDescGZIP(), []int{0}
}

func (x *CreatePurchaseOrderReq) GetProductID() common.PRODUCT_ID {
	if x != nil {
		return x.ProductID
	}
	return common.PRODUCT_ID(0)
}

func (x *CreatePurchaseOrderReq) GetPurchaseID() int32 {
	if x != nil {
		return x.PurchaseID
	}
	return 0
}

func (x *CreatePurchaseOrderReq) GetCommodityID() string {
	if x != nil {
		return x.CommodityID
	}
	return ""
}

func (x *CreatePurchaseOrderReq) GetEntranceType() common.PURCHASE_ENTRANCE_TYPE {
	if x != nil {
		return x.EntranceType
	}
	return common.PURCHASE_ENTRANCE_TYPE(0)
}

func (x *CreatePurchaseOrderReq) GetTriggerType() common.PURCHASE_TRIGGER_TYPE {
	if x != nil {
		return x.TriggerType
	}
	return common.PURCHASE_TRIGGER_TYPE(0)
}

type CreatePurchaseOrderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret               *common.Result            `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	PurchaseOrderInfo *common.PurchaseOrderInfo `protobuf:"bytes,2,opt,name=PurchaseOrderInfo,proto3" json:"PurchaseOrderInfo,omitempty"` // 支付订单信息
}

func (x *CreatePurchaseOrderRsp) Reset() {
	*x = CreatePurchaseOrderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePurchaseOrderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePurchaseOrderRsp) ProtoMessage() {}

func (x *CreatePurchaseOrderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_payment_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePurchaseOrderRsp.ProtoReflect.Descriptor instead.
func (*CreatePurchaseOrderRsp) Descriptor() ([]byte, []int) {
	return file_payment_proto_rawDescGZIP(), []int{1}
}

func (x *CreatePurchaseOrderRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *CreatePurchaseOrderRsp) GetPurchaseOrderInfo() *common.PurchaseOrderInfo {
	if x != nil {
		return x.PurchaseOrderInfo
	}
	return nil
}

// 支付成功，请求奖励发货
type DeliverPurchaseRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductID       common.PRODUCT_ID `protobuf:"varint,1,opt,name=ProductID,proto3,enum=common.PRODUCT_ID" json:"ProductID,omitempty"` // 产品ID
	CommodityID     string            `protobuf:"bytes,2,opt,name=CommodityID,proto3" json:"CommodityID,omitempty"`                     // 渠道商品ID
	OrderID         int64             `protobuf:"varint,3,opt,name=OrderID,proto3" json:"OrderID,omitempty"`                            // 订单ID
	PurchaseID      int32             `protobuf:"varint,4,opt,name=PurchaseID,proto3" json:"PurchaseID,omitempty"`                      // 支付ID(对应配置文件)
	PayType         common.PAY_TYPE   `protobuf:"varint,5,opt,name=PayType,proto3,enum=common.PAY_TYPE" json:"PayType,omitempty"`       // 支付渠道类型
	Receipt         string            `protobuf:"bytes,6,opt,name=Receipt,proto3" json:"Receipt,omitempty"`                             // 第三方返回的收据凭证
	TransactionID   string            `protobuf:"bytes,7,opt,name=TransactionID,proto3" json:"TransactionID,omitempty"`                 // 第三方生成的交易ID
	IsoCurrencyCode string            `protobuf:"bytes,8,opt,name=IsoCurrencyCode,proto3" json:"IsoCurrencyCode,omitempty"`             // 货币码
	LocalizedPrice  int64             `protobuf:"varint,9,opt,name=LocalizedPrice,proto3" json:"LocalizedPrice,omitempty"`              // 产品本地价格
}

func (x *DeliverPurchaseRewardReq) Reset() {
	*x = DeliverPurchaseRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliverPurchaseRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliverPurchaseRewardReq) ProtoMessage() {}

func (x *DeliverPurchaseRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_payment_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliverPurchaseRewardReq.ProtoReflect.Descriptor instead.
func (*DeliverPurchaseRewardReq) Descriptor() ([]byte, []int) {
	return file_payment_proto_rawDescGZIP(), []int{2}
}

func (x *DeliverPurchaseRewardReq) GetProductID() common.PRODUCT_ID {
	if x != nil {
		return x.ProductID
	}
	return common.PRODUCT_ID(0)
}

func (x *DeliverPurchaseRewardReq) GetCommodityID() string {
	if x != nil {
		return x.CommodityID
	}
	return ""
}

func (x *DeliverPurchaseRewardReq) GetOrderID() int64 {
	if x != nil {
		return x.OrderID
	}
	return 0
}

func (x *DeliverPurchaseRewardReq) GetPurchaseID() int32 {
	if x != nil {
		return x.PurchaseID
	}
	return 0
}

func (x *DeliverPurchaseRewardReq) GetPayType() common.PAY_TYPE {
	if x != nil {
		return x.PayType
	}
	return common.PAY_TYPE(0)
}

func (x *DeliverPurchaseRewardReq) GetReceipt() string {
	if x != nil {
		return x.Receipt
	}
	return ""
}

func (x *DeliverPurchaseRewardReq) GetTransactionID() string {
	if x != nil {
		return x.TransactionID
	}
	return ""
}

func (x *DeliverPurchaseRewardReq) GetIsoCurrencyCode() string {
	if x != nil {
		return x.IsoCurrencyCode
	}
	return ""
}

func (x *DeliverPurchaseRewardReq) GetLocalizedPrice() int64 {
	if x != nil {
		return x.LocalizedPrice
	}
	return 0
}

type DeliverPurchaseRewardRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result   `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Reward []*common.Reward `protobuf:"bytes,2,rep,name=Reward,proto3" json:"Reward,omitempty"`
}

func (x *DeliverPurchaseRewardRsp) Reset() {
	*x = DeliverPurchaseRewardRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliverPurchaseRewardRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliverPurchaseRewardRsp) ProtoMessage() {}

func (x *DeliverPurchaseRewardRsp) ProtoReflect() protoreflect.Message {
	mi := &file_payment_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliverPurchaseRewardRsp.ProtoReflect.Descriptor instead.
func (*DeliverPurchaseRewardRsp) Descriptor() ([]byte, []int) {
	return file_payment_proto_rawDescGZIP(), []int{3}
}

func (x *DeliverPurchaseRewardRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *DeliverPurchaseRewardRsp) GetReward() []*common.Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

// 支付失败，关闭订单
type ClosePurchaseOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductID   common.PRODUCT_ID           `protobuf:"varint,1,opt,name=ProductID,proto3,enum=common.PRODUCT_ID" json:"ProductID,omitempty"`             // 产品ID
	CommodityID string                      `protobuf:"bytes,2,opt,name=CommodityID,proto3" json:"CommodityID,omitempty"`                                 // 第三方商品ID
	OrderID     int64                       `protobuf:"varint,3,opt,name=OrderID,proto3" json:"OrderID,omitempty"`                                        // 订单ID
	PurchaseID  int32                       `protobuf:"varint,4,opt,name=PurchaseID,proto3" json:"PurchaseID,omitempty"`                                  // 支付ID(对应配置文件)
	FailedType  common.PURCHASE_FAILED_TYPE `protobuf:"varint,5,opt,name=FailedType,proto3,enum=common.PURCHASE_FAILED_TYPE" json:"FailedType,omitempty"` // 支付失败原因
}

func (x *ClosePurchaseOrderReq) Reset() {
	*x = ClosePurchaseOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClosePurchaseOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClosePurchaseOrderReq) ProtoMessage() {}

func (x *ClosePurchaseOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_payment_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClosePurchaseOrderReq.ProtoReflect.Descriptor instead.
func (*ClosePurchaseOrderReq) Descriptor() ([]byte, []int) {
	return file_payment_proto_rawDescGZIP(), []int{4}
}

func (x *ClosePurchaseOrderReq) GetProductID() common.PRODUCT_ID {
	if x != nil {
		return x.ProductID
	}
	return common.PRODUCT_ID(0)
}

func (x *ClosePurchaseOrderReq) GetCommodityID() string {
	if x != nil {
		return x.CommodityID
	}
	return ""
}

func (x *ClosePurchaseOrderReq) GetOrderID() int64 {
	if x != nil {
		return x.OrderID
	}
	return 0
}

func (x *ClosePurchaseOrderReq) GetPurchaseID() int32 {
	if x != nil {
		return x.PurchaseID
	}
	return 0
}

func (x *ClosePurchaseOrderReq) GetFailedType() common.PURCHASE_FAILED_TYPE {
	if x != nil {
		return x.FailedType
	}
	return common.PURCHASE_FAILED_TYPE(0)
}

type ClosePurchaseOrderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
}

func (x *ClosePurchaseOrderRsp) Reset() {
	*x = ClosePurchaseOrderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_payment_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClosePurchaseOrderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClosePurchaseOrderRsp) ProtoMessage() {}

func (x *ClosePurchaseOrderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_payment_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClosePurchaseOrderRsp.ProtoReflect.Descriptor instead.
func (*ClosePurchaseOrderRsp) Descriptor() ([]byte, []int) {
	return file_payment_proto_rawDescGZIP(), []int{5}
}

func (x *ClosePurchaseOrderRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

var File_payment_proto protoreflect.FileDescriptor

var file_payment_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x02, 0x50, 0x42, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x91, 0x02, 0x0a, 0x16,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x30, 0x0a, 0x09, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x49, 0x44, 0x52, 0x09, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x75, 0x72, 0x63,
	0x68, 0x61, 0x73, 0x65, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x50, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x64, 0x69, 0x74, 0x79, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x49, 0x44, 0x12, 0x42, 0x0a, 0x0c, 0x45, 0x6e,
	0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41,
	0x53, 0x45, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x52, 0x0c, 0x45, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3f,
	0x0a, 0x0b, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x55, 0x52,
	0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x52, 0x49, 0x47, 0x47, 0x45, 0x52, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x52, 0x0b, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x83, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61,
	0x73, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x47, 0x0a, 0x11,
	0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x11, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xe6, 0x02, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x12, 0x30, 0x0a, 0x09, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x49, 0x44, 0x52, 0x09, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74,
	0x79, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x64, 0x69, 0x74, 0x79, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x44,
	0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x49, 0x44, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x49, 0x44,
	0x12, 0x2a, 0x0a, 0x07, 0x50, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x41, 0x59, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x52, 0x07, 0x50, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x52,
	0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x28, 0x0a, 0x0f,
	0x49, 0x73, 0x6f, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x49, 0x73, 0x6f, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69,
	0x7a, 0x65, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x22, 0x64,
	0x0a, 0x18, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x26, 0x0a, 0x06,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x06, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x22, 0xe3, 0x01, 0x0a, 0x15, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x50, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x30,
	0x0a, 0x09, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x52, 0x4f, 0x44, 0x55,
	0x43, 0x54, 0x5f, 0x49, 0x44, 0x52, 0x09, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x44,
	0x12, 0x20, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x49, 0x44, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79,
	0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x44, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a,
	0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x49, 0x44, 0x12, 0x3c, 0x0a, 0x0a,
	0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41,
	0x53, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x0a,
	0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x22, 0x39, 0x0a, 0x15, 0x43, 0x6c,
	0x6f, 0x73, 0x65, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x03, 0x72, 0x65, 0x74, 0x42, 0x43, 0x5a, 0x41, 0x67, 0x69, 0x74, 0x2e, 0x6b, 0x65, 0x65,
	0x70, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2e, 0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x2d,
	0x65, 0x6e, 0x64, 0x2f, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x70, 0x62, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x42, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_payment_proto_rawDescOnce sync.Once
	file_payment_proto_rawDescData = file_payment_proto_rawDesc
)

func file_payment_proto_rawDescGZIP() []byte {
	file_payment_proto_rawDescOnce.Do(func() {
		file_payment_proto_rawDescData = protoimpl.X.CompressGZIP(file_payment_proto_rawDescData)
	})
	return file_payment_proto_rawDescData
}

var file_payment_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_payment_proto_goTypes = []interface{}{
	(*CreatePurchaseOrderReq)(nil),     // 0: PB.CreatePurchaseOrderReq
	(*CreatePurchaseOrderRsp)(nil),     // 1: PB.CreatePurchaseOrderRsp
	(*DeliverPurchaseRewardReq)(nil),   // 2: PB.DeliverPurchaseRewardReq
	(*DeliverPurchaseRewardRsp)(nil),   // 3: PB.DeliverPurchaseRewardRsp
	(*ClosePurchaseOrderReq)(nil),      // 4: PB.ClosePurchaseOrderReq
	(*ClosePurchaseOrderRsp)(nil),      // 5: PB.ClosePurchaseOrderRsp
	(common.PRODUCT_ID)(0),             // 6: common.PRODUCT_ID
	(common.PURCHASE_ENTRANCE_TYPE)(0), // 7: common.PURCHASE_ENTRANCE_TYPE
	(common.PURCHASE_TRIGGER_TYPE)(0),  // 8: common.PURCHASE_TRIGGER_TYPE
	(*common.Result)(nil),              // 9: common.Result
	(*common.PurchaseOrderInfo)(nil),   // 10: common.PurchaseOrderInfo
	(common.PAY_TYPE)(0),               // 11: common.PAY_TYPE
	(*common.Reward)(nil),              // 12: common.Reward
	(common.PURCHASE_FAILED_TYPE)(0),   // 13: common.PURCHASE_FAILED_TYPE
}
var file_payment_proto_depIdxs = []int32{
	6,  // 0: PB.CreatePurchaseOrderReq.ProductID:type_name -> common.PRODUCT_ID
	7,  // 1: PB.CreatePurchaseOrderReq.EntranceType:type_name -> common.PURCHASE_ENTRANCE_TYPE
	8,  // 2: PB.CreatePurchaseOrderReq.TriggerType:type_name -> common.PURCHASE_TRIGGER_TYPE
	9,  // 3: PB.CreatePurchaseOrderRsp.ret:type_name -> common.Result
	10, // 4: PB.CreatePurchaseOrderRsp.PurchaseOrderInfo:type_name -> common.PurchaseOrderInfo
	6,  // 5: PB.DeliverPurchaseRewardReq.ProductID:type_name -> common.PRODUCT_ID
	11, // 6: PB.DeliverPurchaseRewardReq.PayType:type_name -> common.PAY_TYPE
	9,  // 7: PB.DeliverPurchaseRewardRsp.ret:type_name -> common.Result
	12, // 8: PB.DeliverPurchaseRewardRsp.Reward:type_name -> common.Reward
	6,  // 9: PB.ClosePurchaseOrderReq.ProductID:type_name -> common.PRODUCT_ID
	13, // 10: PB.ClosePurchaseOrderReq.FailedType:type_name -> common.PURCHASE_FAILED_TYPE
	9,  // 11: PB.ClosePurchaseOrderRsp.ret:type_name -> common.Result
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_payment_proto_init() }
func file_payment_proto_init() {
	if File_payment_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_payment_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePurchaseOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePurchaseOrderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliverPurchaseRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliverPurchaseRewardRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClosePurchaseOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_payment_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClosePurchaseOrderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_payment_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_payment_proto_goTypes,
		DependencyIndexes: file_payment_proto_depIdxs,
		MessageInfos:      file_payment_proto_msgTypes,
	}.Build()
	File_payment_proto = out.File
	file_payment_proto_rawDesc = nil
	file_payment_proto_goTypes = nil
	file_payment_proto_depIdxs = nil
}
