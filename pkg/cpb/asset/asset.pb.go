// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: asset.proto

package assetPB

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ------------------------------------
// 查询背包请求
type GetBagReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category []common.ITEM_CATEGORY `protobuf:"varint,1,rep,packed,name=category,proto3,enum=common.ITEM_CATEGORY" json:"category,omitempty"`
}

func (x *GetBagReq) Reset() {
	*x = GetBagReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBagReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBagReq) ProtoMessage() {}

func (x *GetBagReq) ProtoReflect() protoreflect.Message {
	mi := &file_asset_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBagReq.ProtoReflect.Descriptor instead.
func (*GetBagReq) Descriptor() ([]byte, []int) {
	return file_asset_proto_rawDescGZIP(), []int{0}
}

func (x *GetBagReq) GetCategory() []common.ITEM_CATEGORY {
	if x != nil {
		return x.Category
	}
	return nil
}

type BagInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category common.ITEM_CATEGORY `protobuf:"varint,1,opt,name=category,proto3,enum=common.ITEM_CATEGORY" json:"category,omitempty"`
	ItemInfo []*common.ItemInfo   `protobuf:"bytes,2,rep,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`
}

func (x *BagInfo) Reset() {
	*x = BagInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BagInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BagInfo) ProtoMessage() {}

func (x *BagInfo) ProtoReflect() protoreflect.Message {
	mi := &file_asset_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BagInfo.ProtoReflect.Descriptor instead.
func (*BagInfo) Descriptor() ([]byte, []int) {
	return file_asset_proto_rawDescGZIP(), []int{1}
}

func (x *BagInfo) GetCategory() common.ITEM_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.ITEM_CATEGORY(0)
}

func (x *BagInfo) GetItemInfo() []*common.ItemInfo {
	if x != nil {
		return x.ItemInfo
	}
	return nil
}

// 查询背包响应
type GetBagRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret     *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	BagInfo []*BagInfo     `protobuf:"bytes,2,rep,name=BagInfo,proto3" json:"BagInfo,omitempty"`
}

func (x *GetBagRsp) Reset() {
	*x = GetBagRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBagRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBagRsp) ProtoMessage() {}

func (x *GetBagRsp) ProtoReflect() protoreflect.Message {
	mi := &file_asset_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBagRsp.ProtoReflect.Descriptor instead.
func (*GetBagRsp) Descriptor() ([]byte, []int) {
	return file_asset_proto_rawDescGZIP(), []int{2}
}

func (x *GetBagRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetBagRsp) GetBagInfo() []*BagInfo {
	if x != nil {
		return x.BagInfo
	}
	return nil
}

// ------------------------------------
// 用户操作道具请求
type OperateItemReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OperateType common.BAG_OPERATE_TYPE `protobuf:"varint,1,opt,name=operate_type,json=operateType,proto3,enum=common.BAG_OPERATE_TYPE" json:"operate_type,omitempty"` // 操作类型
	ItemId      int32                   `protobuf:"varint,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`                                             // 道具id
	Count       int32                   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`                                                             // 使用数量
}

func (x *OperateItemReq) Reset() {
	*x = OperateItemReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperateItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperateItemReq) ProtoMessage() {}

func (x *OperateItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_asset_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperateItemReq.ProtoReflect.Descriptor instead.
func (*OperateItemReq) Descriptor() ([]byte, []int) {
	return file_asset_proto_rawDescGZIP(), []int{3}
}

func (x *OperateItemReq) GetOperateType() common.BAG_OPERATE_TYPE {
	if x != nil {
		return x.OperateType
	}
	return common.BAG_OPERATE_TYPE(0)
}

func (x *OperateItemReq) GetItemId() int32 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *OperateItemReq) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 用户操作道具响应
type OperateItemRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Effect []*common.ItemInfo `protobuf:"bytes,2,rep,name=effect,proto3" json:"effect,omitempty"` // 执行产生影响
}

func (x *OperateItemRsp) Reset() {
	*x = OperateItemRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperateItemRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperateItemRsp) ProtoMessage() {}

func (x *OperateItemRsp) ProtoReflect() protoreflect.Message {
	mi := &file_asset_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperateItemRsp.ProtoReflect.Descriptor instead.
func (*OperateItemRsp) Descriptor() ([]byte, []int) {
	return file_asset_proto_rawDescGZIP(), []int{4}
}

func (x *OperateItemRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *OperateItemRsp) GetEffect() []*common.ItemInfo {
	if x != nil {
		return x.Effect
	}
	return nil
}

// ------------------------------------
// 奖励发放信息 Tips
type RewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reward *common.Reward `protobuf:"bytes,1,opt,name=reward,proto3" json:"reward,omitempty"` // 奖励信息
}

func (x *RewardMessage) Reset() {
	*x = RewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardMessage) ProtoMessage() {}

func (x *RewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_asset_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardMessage.ProtoReflect.Descriptor instead.
func (*RewardMessage) Descriptor() ([]byte, []int) {
	return file_asset_proto_rawDescGZIP(), []int{5}
}

func (x *RewardMessage) GetReward() *common.Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

var File_asset_proto protoreflect.FileDescriptor

var file_asset_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x50, 0x42, 0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3e,
	0x0a, 0x09, 0x47, 0x65, 0x74, 0x42, 0x61, 0x67, 0x52, 0x65, 0x71, 0x12, 0x31, 0x0a, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x6b,
	0x0a, 0x07, 0x42, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x2d, 0x0a, 0x09,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x59, 0x0a, 0x09, 0x47,
	0x65, 0x74, 0x42, 0x61, 0x67, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2a, 0x0a, 0x07, 0x42, 0x61,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x50, 0x42, 0x2e, 0x42, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x42,
	0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x7c, 0x0a, 0x0e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x3b, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x41, 0x47, 0x5f, 0x4f, 0x50, 0x45, 0x52,
	0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x5c, 0x0a, 0x0e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x28, 0x0a, 0x06, 0x65, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x65, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x22, 0x37, 0x0a, 0x0d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x42, 0x3f, 0x5a, 0x3d, 0x67,
	0x69, 0x74, 0x2e, 0x6b, 0x65, 0x65, 0x70, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2e, 0x78, 0x79, 0x7a,
	0x2f, 0x62, 0x61, 0x63, 0x6b, 0x2d, 0x65, 0x6e, 0x64, 0x2f, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2d,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x70, 0x62, 0x2f, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x3b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x50, 0x42, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_asset_proto_rawDescOnce sync.Once
	file_asset_proto_rawDescData = file_asset_proto_rawDesc
)

func file_asset_proto_rawDescGZIP() []byte {
	file_asset_proto_rawDescOnce.Do(func() {
		file_asset_proto_rawDescData = protoimpl.X.CompressGZIP(file_asset_proto_rawDescData)
	})
	return file_asset_proto_rawDescData
}

var file_asset_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_asset_proto_goTypes = []interface{}{
	(*GetBagReq)(nil),            // 0: assetPB.GetBagReq
	(*BagInfo)(nil),              // 1: assetPB.BagInfo
	(*GetBagRsp)(nil),            // 2: assetPB.GetBagRsp
	(*OperateItemReq)(nil),       // 3: assetPB.OperateItemReq
	(*OperateItemRsp)(nil),       // 4: assetPB.OperateItemRsp
	(*RewardMessage)(nil),        // 5: assetPB.RewardMessage
	(common.ITEM_CATEGORY)(0),    // 6: common.ITEM_CATEGORY
	(*common.ItemInfo)(nil),      // 7: common.ItemInfo
	(*common.Result)(nil),        // 8: common.Result
	(common.BAG_OPERATE_TYPE)(0), // 9: common.BAG_OPERATE_TYPE
	(*common.Reward)(nil),        // 10: common.Reward
}
var file_asset_proto_depIdxs = []int32{
	6,  // 0: assetPB.GetBagReq.category:type_name -> common.ITEM_CATEGORY
	6,  // 1: assetPB.BagInfo.category:type_name -> common.ITEM_CATEGORY
	7,  // 2: assetPB.BagInfo.item_info:type_name -> common.ItemInfo
	8,  // 3: assetPB.GetBagRsp.ret:type_name -> common.Result
	1,  // 4: assetPB.GetBagRsp.BagInfo:type_name -> assetPB.BagInfo
	9,  // 5: assetPB.OperateItemReq.operate_type:type_name -> common.BAG_OPERATE_TYPE
	8,  // 6: assetPB.OperateItemRsp.ret:type_name -> common.Result
	7,  // 7: assetPB.OperateItemRsp.effect:type_name -> common.ItemInfo
	10, // 8: assetPB.RewardMessage.reward:type_name -> common.Reward
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_asset_proto_init() }
func file_asset_proto_init() {
	if File_asset_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_asset_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBagReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BagInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBagRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperateItemReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperateItemRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_asset_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_asset_proto_goTypes,
		DependencyIndexes: file_asset_proto_depIdxs,
		MessageInfos:      file_asset_proto_msgTypes,
	}.Build()
	File_asset_proto = out.File
	file_asset_proto_rawDesc = nil
	file_asset_proto_goTypes = nil
	file_asset_proto_depIdxs = nil
}
