// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: errors.proto

package commonPB

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ErrCode 错误码
type ErrCode int32

const (
	// ***********************************************************************************
	ErrCode_ERR_SUCCESS           ErrCode = 0  // 成功
	ErrCode_ERR_FAIL              ErrCode = 1  // 失败
	ErrCode_ERR_BAD_PARAM         ErrCode = 2  // 参数错误
	ErrCode_ERR_NOT_EXIST         ErrCode = 3  // 不存在
	ErrCode_ERR_SYSTEM_MISTAKE    ErrCode = 4  // 系统错误
	ErrCode_ERR_OPERATION         ErrCode = 5  // 操作异常
	ErrCode_ERR_NOT_ENOUGH        ErrCode = 6  // 条件不足
	ErrCode_ERR_ILLEGAL_OPERATION ErrCode = 7  // 非法操作
	ErrCode_ERR_EXCEED            ErrCode = 8  // 超出，超过
	ErrCode_ERR_CONF_ERROR        ErrCode = 9  // 配置错误
	ErrCode_ERR_JSON_PARSE_ERROR  ErrCode = 10 // JSON解析错误
	ErrCode_ERR_DB_OPE_ERROR      ErrCode = 11 // DB操作错误
	ErrCode_ERR_FORBID_WORD       ErrCode = 12 // 含有敏感字
	ErrCode_ERR_UNKNOWN           ErrCode = 99 // 未知错误
	// ***********************************************************************************
	ErrCode_ERR_LOGIN_TOKEN_EXPIRE           ErrCode = 100 // Token失效
	ErrCode_ERR_LOGIN_TYPE                   ErrCode = 101 // 登录类型错误
	ErrCode_ERR_LOGIN_FORBID                 ErrCode = 102 // 账号禁止登录
	ErrCode_ERR_LOGIN_PLAY_FORBID            ErrCode = 103 // 账号禁玩，可以登录
	ErrCode_ERR_LOGIN_BAN                    ErrCode = 104 // 封号
	ErrCode_ERR_LOGIN_REGISTERED             ErrCode = 105 // 已注册
	ErrCode_ERR_LOGIN_SDK_BAD_INFO           ErrCode = 106 // SDK信息错误
	ErrCode_ERR_LOGIN_ACCOUNT_PASSWORD       ErrCode = 107 // 账号或密码格式不对
	ErrCode_ERR_LOGIN_ACCOUNT_NOT_EXIST      ErrCode = 108 // 账号不存在(针对登录)
	ErrCode_ERR_LOGIN_ACCOUNT_IS_EXIST       ErrCode = 109 // 账号已存在(针对注册
	ErrCode_ERR_LOGIN_ACCOUNT_PASSWORD_ERROR ErrCode = 110 // 账号或密码错误
	ErrCode_ERR_LOGIN_ANTI_ADDICTION         ErrCode = 111 // 反沉迷状态不可登录
	ErrCode_ERR_LOGIN_BLOCK_LOCATION         ErrCode = 112 // 地区封杀
	ErrCode_ERR_LOGIN_UNKNOWN                ErrCode = 199 // 未知登录错误
	// ***********************************************************************************
	ErrCode_ERR_HALL_ENTER_SPOT             ErrCode = 200 // 进入钓场失败
	ErrCode_ERR_HALL_GET_ROOM               ErrCode = 201 // 获取房间失败
	ErrCode_ERR_HALL_ITEM_NOT_ENOUGH        ErrCode = 202 // 道具不足
	ErrCode_ERR_HALL_GOODS_LIMIT            ErrCode = 203 // 超过商品限制购买次数
	ErrCode_ERR_HALL_ADD_REWARD_ERR         ErrCode = 204 // 添加奖励失败
	ErrCode_ERR_HALL_EXIST_IN_ROOM          ErrCode = 205 // 玩家已经在房间
	ErrCode_ERR_HALL_ENTRY_LEVEL            ErrCode = 206 // 玩家等级不够
	ErrCode_ERR_HALL_SPOT_CLOSE             ErrCode = 207 // 钓点关闭
	ErrCode_ERR_HALL_RIG_RULE_WRONG         ErrCode = 208 // 规则错误
	ErrCode_ERR_HALL_RIG_SLOT_NOT_ENOUGH    ErrCode = 209 // 钓组槽位不足
	ErrCode_ERR_HALL_REAL_NAME_AUTH         ErrCode = 210 // 实名认证失败
	ErrCode_ERR_HALL_REAL_NAME_NOT_OPEN     ErrCode = 211 // 实名认证未开启
	ErrCode_ERR_HALL_BAG_FULL               ErrCode = 212 // 背包已满
	ErrCode_ERR_HALL_CDKEY_EXCHANGE_FAILED  ErrCode = 213 // CDKey兑换失败
	ErrCode_ERR_HALL_CDKEY_EXCHANGE_EXPIRED ErrCode = 214 // CDKey已过期
	ErrCode_ERR_HALL_CDKEY_EXCHANGE_MAX     ErrCode = 215 // CDKey已被兑换完
	ErrCode_ERR_HALL_CDKEY_EXCHANGE_USED    ErrCode = 216 // CDKey已使用
	ErrCode_ERR_HALL_UNKNOWN                ErrCode = 299 // 未知错误
	// ***********************************************************************************
	ErrCode_ERR_SPOT_GET_ROOM          ErrCode = 300 // 获取房间id错误
	ErrCode_ERR_SPOT_NOT_IN_ROOM       ErrCode = 301 // 玩家不在此房间
	ErrCode_ERR_SPOT_ROOM_PLAYER       ErrCode = 302 // 钓点房间玩家查询失败
	ErrCode_ERR_SPOT_KEEPENT_FULL      ErrCode = 303 // 鱼护已满
	ErrCode_ERR_SPOT_FISH_WEIGHT_LIMIT ErrCode = 304 // 鱼重限制
	ErrCode_ERR_SPOT_ADD_FISH_EXIST    ErrCode = 305 // 添加鱼已存在
	ErrCode_ERR_SPOT_NOT_ENOUGH_POWER  ErrCode = 306 // 体力不足
	ErrCode_ERR_SPOT_FISH_OUT          ErrCode = 307 // 鱼跑
	ErrCode_ERR_SPOT_FISH_IN_KEEPENT   ErrCode = 308 // 已经入护
	ErrCode_ERR_SPOT_FISH_HAS_OUT      ErrCode = 309 // 已经放生
	// ***********************************************************************************
	ErrCode_ERR_PAY_CHANNEL_NOT_EXIST                ErrCode = 400 // 支付渠道不存在
	ErrCode_ERR_ORDER_PLACE_FAIL                     ErrCode = 401 // 下单失败
	ErrCode_ERR_ORDER_DELIVER_FAIL                   ErrCode = 402 // 发货通知失败
	ErrCode_ERR_ORDER_INVALID                        ErrCode = 403 // 无效订单
	ErrCode_ERR_ORDER_NON_PAYMENT                    ErrCode = 404 // 未支付
	ErrCode_ERR_ORDER_EXIST_UNFINISHED               ErrCode = 405 // 存在未完成订单
	ErrCode_ERR_ORDER_DELIVER_YET                    ErrCode = 406 // 已发货订单
	ErrCode_ERR_ORDER_CLOSE_FAIL                     ErrCode = 407 // 关闭订单处理失败
	ErrCode_ERR_ORDER_VERIFY_PAYED_ORDER_CANCELED    ErrCode = 408 // 校验到取消已支付订单
	ErrCode_ERR_ORDER_VERIFY_HAD_CONSUMPTION         ErrCode = 409 // 校验到订单已消费
	ErrCode_ERR_ORDER_VERIFY_ACCESS_TOKEN_INVALID    ErrCode = 410 // 校验 ACCESS_TOKEN已失效
	ErrCode_ERR_ORDER_VERIFY_CLIENT_JSON_FAILED      ErrCode = 411 // 校验 解析客户端上报Json失败
	ErrCode_ERR_ORDER_VERIFY_ACCESS_TOKEN_GET_FAILED ErrCode = 412 // 校验 获取AccessToken失败
	ErrCode_ERR_ORDER_NOT_MATCH_CONDITION            ErrCode = 413 // 下单条件不匹配
	ErrCode_ERR_ORDER_ORDER_NOT_FOUND                ErrCode = 414 // 订单没有找到
	ErrCode_ERR_ORDER_DELIVER_NO_SUCH_PURCHASE_TYPE  ErrCode = 415 // 没有此种支付类型
	ErrCode_ERR_ORDER_CREATE_ORDER_WRONG_PURCHASE_ID ErrCode = 416 // 错误的支付purchaseID, 非商城支付
	ErrCode_ERR_ORDER_SERVER_PROCESSING_EXCEPTION    ErrCode = 417 // 服务器处理异常
	ErrCode_ERR_ORDER_VERIFY_FAILED                  ErrCode = 418 // 校验失败
	ErrCode_ERR_ORDER_UNBALLOWED_RECHARGE_AGE        ErrCode = 419 // 年龄不可充值
	ErrCode_ERR_ORDER_SINGLE_LIMIT_AGE               ErrCode = 420 // 单笔充值金额限制
	ErrCode_ERR_ORDER_MONTH_LIMIT_AGE                ErrCode = 421 // 月充值金额限制
	// ***********************************************************************************
	ErrCode_ERR_TASK_NOT_EXIST   ErrCode = 500 // 任务不存在
	ErrCode_ERR_TASK_NOT_FINISH  ErrCode = 501 // 任务未完成
	ErrCode_ERR_TASK_NOT_DISPOSE ErrCode = 502 // 任务类型不支持
	// ***********************************************************************************
	ErrCode_ERR_RANK_NOT_EXIST ErrCode = 600 // 排行榜不存在
	ErrCode_ERR_RANK_NOT_OPEN  ErrCode = 601 // 排行榜未开放
	// ***********************************************************************************
	ErrCode_ERR_ACTIVITY_NOT_EXIST       ErrCode = 700 // 活动不存在
	ErrCode_ERR_ACTIVITY_NOT_STARTED     ErrCode = 701 // 活动未开始
	ErrCode_ERR_ACTIVITY_ENDED           ErrCode = 702 // 活动已结束
	ErrCode_ERR_ACTIVITY_ALREADY_CLAIMED ErrCode = 703 // 奖励已领取
	ErrCode_ERR_ACTIVITY_NOT_COMPLETED   ErrCode = 704 // 活动阶段未完成
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:   "ERR_SUCCESS",
		1:   "ERR_FAIL",
		2:   "ERR_BAD_PARAM",
		3:   "ERR_NOT_EXIST",
		4:   "ERR_SYSTEM_MISTAKE",
		5:   "ERR_OPERATION",
		6:   "ERR_NOT_ENOUGH",
		7:   "ERR_ILLEGAL_OPERATION",
		8:   "ERR_EXCEED",
		9:   "ERR_CONF_ERROR",
		10:  "ERR_JSON_PARSE_ERROR",
		11:  "ERR_DB_OPE_ERROR",
		12:  "ERR_FORBID_WORD",
		99:  "ERR_UNKNOWN",
		100: "ERR_LOGIN_TOKEN_EXPIRE",
		101: "ERR_LOGIN_TYPE",
		102: "ERR_LOGIN_FORBID",
		103: "ERR_LOGIN_PLAY_FORBID",
		104: "ERR_LOGIN_BAN",
		105: "ERR_LOGIN_REGISTERED",
		106: "ERR_LOGIN_SDK_BAD_INFO",
		107: "ERR_LOGIN_ACCOUNT_PASSWORD",
		108: "ERR_LOGIN_ACCOUNT_NOT_EXIST",
		109: "ERR_LOGIN_ACCOUNT_IS_EXIST",
		110: "ERR_LOGIN_ACCOUNT_PASSWORD_ERROR",
		111: "ERR_LOGIN_ANTI_ADDICTION",
		112: "ERR_LOGIN_BLOCK_LOCATION",
		199: "ERR_LOGIN_UNKNOWN",
		200: "ERR_HALL_ENTER_SPOT",
		201: "ERR_HALL_GET_ROOM",
		202: "ERR_HALL_ITEM_NOT_ENOUGH",
		203: "ERR_HALL_GOODS_LIMIT",
		204: "ERR_HALL_ADD_REWARD_ERR",
		205: "ERR_HALL_EXIST_IN_ROOM",
		206: "ERR_HALL_ENTRY_LEVEL",
		207: "ERR_HALL_SPOT_CLOSE",
		208: "ERR_HALL_RIG_RULE_WRONG",
		209: "ERR_HALL_RIG_SLOT_NOT_ENOUGH",
		210: "ERR_HALL_REAL_NAME_AUTH",
		211: "ERR_HALL_REAL_NAME_NOT_OPEN",
		212: "ERR_HALL_BAG_FULL",
		213: "ERR_HALL_CDKEY_EXCHANGE_FAILED",
		214: "ERR_HALL_CDKEY_EXCHANGE_EXPIRED",
		215: "ERR_HALL_CDKEY_EXCHANGE_MAX",
		216: "ERR_HALL_CDKEY_EXCHANGE_USED",
		299: "ERR_HALL_UNKNOWN",
		300: "ERR_SPOT_GET_ROOM",
		301: "ERR_SPOT_NOT_IN_ROOM",
		302: "ERR_SPOT_ROOM_PLAYER",
		303: "ERR_SPOT_KEEPENT_FULL",
		304: "ERR_SPOT_FISH_WEIGHT_LIMIT",
		305: "ERR_SPOT_ADD_FISH_EXIST",
		306: "ERR_SPOT_NOT_ENOUGH_POWER",
		307: "ERR_SPOT_FISH_OUT",
		308: "ERR_SPOT_FISH_IN_KEEPENT",
		309: "ERR_SPOT_FISH_HAS_OUT",
		400: "ERR_PAY_CHANNEL_NOT_EXIST",
		401: "ERR_ORDER_PLACE_FAIL",
		402: "ERR_ORDER_DELIVER_FAIL",
		403: "ERR_ORDER_INVALID",
		404: "ERR_ORDER_NON_PAYMENT",
		405: "ERR_ORDER_EXIST_UNFINISHED",
		406: "ERR_ORDER_DELIVER_YET",
		407: "ERR_ORDER_CLOSE_FAIL",
		408: "ERR_ORDER_VERIFY_PAYED_ORDER_CANCELED",
		409: "ERR_ORDER_VERIFY_HAD_CONSUMPTION",
		410: "ERR_ORDER_VERIFY_ACCESS_TOKEN_INVALID",
		411: "ERR_ORDER_VERIFY_CLIENT_JSON_FAILED",
		412: "ERR_ORDER_VERIFY_ACCESS_TOKEN_GET_FAILED",
		413: "ERR_ORDER_NOT_MATCH_CONDITION",
		414: "ERR_ORDER_ORDER_NOT_FOUND",
		415: "ERR_ORDER_DELIVER_NO_SUCH_PURCHASE_TYPE",
		416: "ERR_ORDER_CREATE_ORDER_WRONG_PURCHASE_ID",
		417: "ERR_ORDER_SERVER_PROCESSING_EXCEPTION",
		418: "ERR_ORDER_VERIFY_FAILED",
		419: "ERR_ORDER_UNBALLOWED_RECHARGE_AGE",
		420: "ERR_ORDER_SINGLE_LIMIT_AGE",
		421: "ERR_ORDER_MONTH_LIMIT_AGE",
		500: "ERR_TASK_NOT_EXIST",
		501: "ERR_TASK_NOT_FINISH",
		502: "ERR_TASK_NOT_DISPOSE",
		600: "ERR_RANK_NOT_EXIST",
		601: "ERR_RANK_NOT_OPEN",
		700: "ERR_ACTIVITY_NOT_EXIST",
		701: "ERR_ACTIVITY_NOT_STARTED",
		702: "ERR_ACTIVITY_ENDED",
		703: "ERR_ACTIVITY_ALREADY_CLAIMED",
		704: "ERR_ACTIVITY_NOT_COMPLETED",
	}
	ErrCode_value = map[string]int32{
		"ERR_SUCCESS":                              0,
		"ERR_FAIL":                                 1,
		"ERR_BAD_PARAM":                            2,
		"ERR_NOT_EXIST":                            3,
		"ERR_SYSTEM_MISTAKE":                       4,
		"ERR_OPERATION":                            5,
		"ERR_NOT_ENOUGH":                           6,
		"ERR_ILLEGAL_OPERATION":                    7,
		"ERR_EXCEED":                               8,
		"ERR_CONF_ERROR":                           9,
		"ERR_JSON_PARSE_ERROR":                     10,
		"ERR_DB_OPE_ERROR":                         11,
		"ERR_FORBID_WORD":                          12,
		"ERR_UNKNOWN":                              99,
		"ERR_LOGIN_TOKEN_EXPIRE":                   100,
		"ERR_LOGIN_TYPE":                           101,
		"ERR_LOGIN_FORBID":                         102,
		"ERR_LOGIN_PLAY_FORBID":                    103,
		"ERR_LOGIN_BAN":                            104,
		"ERR_LOGIN_REGISTERED":                     105,
		"ERR_LOGIN_SDK_BAD_INFO":                   106,
		"ERR_LOGIN_ACCOUNT_PASSWORD":               107,
		"ERR_LOGIN_ACCOUNT_NOT_EXIST":              108,
		"ERR_LOGIN_ACCOUNT_IS_EXIST":               109,
		"ERR_LOGIN_ACCOUNT_PASSWORD_ERROR":         110,
		"ERR_LOGIN_ANTI_ADDICTION":                 111,
		"ERR_LOGIN_BLOCK_LOCATION":                 112,
		"ERR_LOGIN_UNKNOWN":                        199,
		"ERR_HALL_ENTER_SPOT":                      200,
		"ERR_HALL_GET_ROOM":                        201,
		"ERR_HALL_ITEM_NOT_ENOUGH":                 202,
		"ERR_HALL_GOODS_LIMIT":                     203,
		"ERR_HALL_ADD_REWARD_ERR":                  204,
		"ERR_HALL_EXIST_IN_ROOM":                   205,
		"ERR_HALL_ENTRY_LEVEL":                     206,
		"ERR_HALL_SPOT_CLOSE":                      207,
		"ERR_HALL_RIG_RULE_WRONG":                  208,
		"ERR_HALL_RIG_SLOT_NOT_ENOUGH":             209,
		"ERR_HALL_REAL_NAME_AUTH":                  210,
		"ERR_HALL_REAL_NAME_NOT_OPEN":              211,
		"ERR_HALL_BAG_FULL":                        212,
		"ERR_HALL_CDKEY_EXCHANGE_FAILED":           213,
		"ERR_HALL_CDKEY_EXCHANGE_EXPIRED":          214,
		"ERR_HALL_CDKEY_EXCHANGE_MAX":              215,
		"ERR_HALL_CDKEY_EXCHANGE_USED":             216,
		"ERR_HALL_UNKNOWN":                         299,
		"ERR_SPOT_GET_ROOM":                        300,
		"ERR_SPOT_NOT_IN_ROOM":                     301,
		"ERR_SPOT_ROOM_PLAYER":                     302,
		"ERR_SPOT_KEEPENT_FULL":                    303,
		"ERR_SPOT_FISH_WEIGHT_LIMIT":               304,
		"ERR_SPOT_ADD_FISH_EXIST":                  305,
		"ERR_SPOT_NOT_ENOUGH_POWER":                306,
		"ERR_SPOT_FISH_OUT":                        307,
		"ERR_SPOT_FISH_IN_KEEPENT":                 308,
		"ERR_SPOT_FISH_HAS_OUT":                    309,
		"ERR_PAY_CHANNEL_NOT_EXIST":                400,
		"ERR_ORDER_PLACE_FAIL":                     401,
		"ERR_ORDER_DELIVER_FAIL":                   402,
		"ERR_ORDER_INVALID":                        403,
		"ERR_ORDER_NON_PAYMENT":                    404,
		"ERR_ORDER_EXIST_UNFINISHED":               405,
		"ERR_ORDER_DELIVER_YET":                    406,
		"ERR_ORDER_CLOSE_FAIL":                     407,
		"ERR_ORDER_VERIFY_PAYED_ORDER_CANCELED":    408,
		"ERR_ORDER_VERIFY_HAD_CONSUMPTION":         409,
		"ERR_ORDER_VERIFY_ACCESS_TOKEN_INVALID":    410,
		"ERR_ORDER_VERIFY_CLIENT_JSON_FAILED":      411,
		"ERR_ORDER_VERIFY_ACCESS_TOKEN_GET_FAILED": 412,
		"ERR_ORDER_NOT_MATCH_CONDITION":            413,
		"ERR_ORDER_ORDER_NOT_FOUND":                414,
		"ERR_ORDER_DELIVER_NO_SUCH_PURCHASE_TYPE":  415,
		"ERR_ORDER_CREATE_ORDER_WRONG_PURCHASE_ID": 416,
		"ERR_ORDER_SERVER_PROCESSING_EXCEPTION":    417,
		"ERR_ORDER_VERIFY_FAILED":                  418,
		"ERR_ORDER_UNBALLOWED_RECHARGE_AGE":        419,
		"ERR_ORDER_SINGLE_LIMIT_AGE":               420,
		"ERR_ORDER_MONTH_LIMIT_AGE":                421,
		"ERR_TASK_NOT_EXIST":                       500,
		"ERR_TASK_NOT_FINISH":                      501,
		"ERR_TASK_NOT_DISPOSE":                     502,
		"ERR_RANK_NOT_EXIST":                       600,
		"ERR_RANK_NOT_OPEN":                        601,
		"ERR_ACTIVITY_NOT_EXIST":                   700,
		"ERR_ACTIVITY_NOT_STARTED":                 701,
		"ERR_ACTIVITY_ENDED":                       702,
		"ERR_ACTIVITY_ALREADY_CLAIMED":             703,
		"ERR_ACTIVITY_NOT_COMPLETED":               704,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_errors_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_errors_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_errors_proto_rawDescGZIP(), []int{0}
}

// Result 通用处理结果
type Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code ErrCode `protobuf:"varint,1,opt,name=code,proto3,enum=common.ErrCode" json:"code,omitempty"`
	Desc string  `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
}

func (x *Result) Reset() {
	*x = Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_errors_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Result) ProtoMessage() {}

func (x *Result) ProtoReflect() protoreflect.Message {
	mi := &file_errors_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Result.ProtoReflect.Descriptor instead.
func (*Result) Descriptor() ([]byte, []int) {
	return file_errors_proto_rawDescGZIP(), []int{0}
}

func (x *Result) GetCode() ErrCode {
	if x != nil {
		return x.Code
	}
	return ErrCode_ERR_SUCCESS
}

func (x *Result) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

// Pagination 通用分页
type PaginationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
	PageSize  int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *PaginationReq) Reset() {
	*x = PaginationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_errors_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaginationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationReq) ProtoMessage() {}

func (x *PaginationReq) ProtoReflect() protoreflect.Message {
	mi := &file_errors_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationReq.ProtoReflect.Descriptor instead.
func (*PaginationReq) Descriptor() ([]byte, []int) {
	return file_errors_proto_rawDescGZIP(), []int{1}
}

func (x *PaginationReq) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *PaginationReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

var File_errors_proto protoreflect.FileDescriptor

var file_errors_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0x41, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x23, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0x4b, 0x0a, 0x0d, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x2a, 0x87, 0x14, 0x0a, 0x07, 0x45, 0x72, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x52, 0x52, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x52, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10,
	0x01, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x52, 0x52, 0x5f, 0x42, 0x41, 0x44, 0x5f, 0x50, 0x41, 0x52,
	0x41, 0x4d, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x52, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x52, 0x52, 0x5f, 0x53,
	0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f, 0x4d, 0x49, 0x53, 0x54, 0x41, 0x4b, 0x45, 0x10, 0x04, 0x12,
	0x11, 0x0a, 0x0d, 0x45, 0x52, 0x52, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x52, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e,
	0x4f, 0x55, 0x47, 0x48, 0x10, 0x06, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x52, 0x52, 0x5f, 0x49, 0x4c,
	0x4c, 0x45, 0x47, 0x41, 0x4c, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x07, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x52, 0x52, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x10,
	0x08, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x52, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x10, 0x09, 0x12, 0x18, 0x0a, 0x14, 0x45, 0x52, 0x52, 0x5f, 0x4a, 0x53, 0x4f,
	0x4e, 0x5f, 0x50, 0x41, 0x52, 0x53, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x0a, 0x12,
	0x14, 0x0a, 0x10, 0x45, 0x52, 0x52, 0x5f, 0x44, 0x42, 0x5f, 0x4f, 0x50, 0x45, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x10, 0x0b, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x52, 0x52, 0x5f, 0x46, 0x4f, 0x52,
	0x42, 0x49, 0x44, 0x5f, 0x57, 0x4f, 0x52, 0x44, 0x10, 0x0c, 0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x52,
	0x52, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x63, 0x12, 0x1a, 0x0a, 0x16, 0x45,
	0x52, 0x52, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x45,
	0x58, 0x50, 0x49, 0x52, 0x45, 0x10, 0x64, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x52, 0x52, 0x5f, 0x4c,
	0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x45,
	0x52, 0x52, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x46, 0x4f, 0x52, 0x42, 0x49, 0x44, 0x10,
	0x66, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x52, 0x52, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x50,
	0x4c, 0x41, 0x59, 0x5f, 0x46, 0x4f, 0x52, 0x42, 0x49, 0x44, 0x10, 0x67, 0x12, 0x11, 0x0a, 0x0d,
	0x45, 0x52, 0x52, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x42, 0x41, 0x4e, 0x10, 0x68, 0x12,
	0x18, 0x0a, 0x14, 0x45, 0x52, 0x52, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x47,
	0x49, 0x53, 0x54, 0x45, 0x52, 0x45, 0x44, 0x10, 0x69, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x52, 0x52,
	0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x53, 0x44, 0x4b, 0x5f, 0x42, 0x41, 0x44, 0x5f, 0x49,
	0x4e, 0x46, 0x4f, 0x10, 0x6a, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x52, 0x52, 0x5f, 0x4c, 0x4f, 0x47,
	0x49, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57,
	0x4f, 0x52, 0x44, 0x10, 0x6b, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x52, 0x52, 0x5f, 0x4c, 0x4f, 0x47,
	0x49, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45,
	0x58, 0x49, 0x53, 0x54, 0x10, 0x6c, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x52, 0x52, 0x5f, 0x4c, 0x4f,
	0x47, 0x49, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x53, 0x5f, 0x45,
	0x58, 0x49, 0x53, 0x54, 0x10, 0x6d, 0x12, 0x24, 0x0a, 0x20, 0x45, 0x52, 0x52, 0x5f, 0x4c, 0x4f,
	0x47, 0x49, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x53, 0x53,
	0x57, 0x4f, 0x52, 0x44, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x6e, 0x12, 0x1c, 0x0a, 0x18,
	0x45, 0x52, 0x52, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x41, 0x4e, 0x54, 0x49, 0x5f, 0x41,
	0x44, 0x44, 0x49, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x6f, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x52,
	0x52, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x4c, 0x4f,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x70, 0x12, 0x16, 0x0a, 0x11, 0x45, 0x52, 0x52, 0x5f,
	0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0xc7, 0x01,
	0x12, 0x18, 0x0a, 0x13, 0x45, 0x52, 0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x45, 0x4e, 0x54,
	0x45, 0x52, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x10, 0xc8, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x45, 0x52,
	0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x52, 0x4f, 0x4f, 0x4d, 0x10,
	0xc9, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x45, 0x52, 0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x49,
	0x54, 0x45, 0x4d, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x4f, 0x55, 0x47, 0x48, 0x10, 0xca,
	0x01, 0x12, 0x19, 0x0a, 0x14, 0x45, 0x52, 0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x47, 0x4f,
	0x4f, 0x44, 0x53, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0xcb, 0x01, 0x12, 0x1c, 0x0a, 0x17,
	0x45, 0x52, 0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x52, 0x45, 0x57,
	0x41, 0x52, 0x44, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xcc, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x45, 0x52,
	0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x5f, 0x49, 0x4e, 0x5f,
	0x52, 0x4f, 0x4f, 0x4d, 0x10, 0xcd, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x45, 0x52, 0x52, 0x5f, 0x48,
	0x41, 0x4c, 0x4c, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x10,
	0xce, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x45, 0x52, 0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x53,
	0x50, 0x4f, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x10, 0xcf, 0x01, 0x12, 0x1c, 0x0a, 0x17,
	0x45, 0x52, 0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x49, 0x47, 0x5f, 0x52, 0x55, 0x4c,
	0x45, 0x5f, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x10, 0xd0, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x45, 0x52,
	0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x49, 0x47, 0x5f, 0x53, 0x4c, 0x4f, 0x54, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x4f, 0x55, 0x47, 0x48, 0x10, 0xd1, 0x01, 0x12, 0x1c, 0x0a,
	0x17, 0x45, 0x52, 0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x10, 0xd2, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x45,
	0x52, 0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x10, 0xd3, 0x01, 0x12, 0x16, 0x0a,
	0x11, 0x45, 0x52, 0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x42, 0x41, 0x47, 0x5f, 0x46, 0x55,
	0x4c, 0x4c, 0x10, 0xd4, 0x01, 0x12, 0x23, 0x0a, 0x1e, 0x45, 0x52, 0x52, 0x5f, 0x48, 0x41, 0x4c,
	0x4c, 0x5f, 0x43, 0x44, 0x4b, 0x45, 0x59, 0x5f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xd5, 0x01, 0x12, 0x24, 0x0a, 0x1f, 0x45, 0x52,
	0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x43, 0x44, 0x4b, 0x45, 0x59, 0x5f, 0x45, 0x58, 0x43,
	0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0xd6, 0x01,
	0x12, 0x20, 0x0a, 0x1b, 0x45, 0x52, 0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x43, 0x44, 0x4b,
	0x45, 0x59, 0x5f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x4d, 0x41, 0x58, 0x10,
	0xd7, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x45, 0x52, 0x52, 0x5f, 0x48, 0x41, 0x4c, 0x4c, 0x5f, 0x43,
	0x44, 0x4b, 0x45, 0x59, 0x5f, 0x45, 0x58, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x55, 0x53,
	0x45, 0x44, 0x10, 0xd8, 0x01, 0x12, 0x15, 0x0a, 0x10, 0x45, 0x52, 0x52, 0x5f, 0x48, 0x41, 0x4c,
	0x4c, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0xab, 0x02, 0x12, 0x16, 0x0a, 0x11,
	0x45, 0x52, 0x52, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x52, 0x4f, 0x4f,
	0x4d, 0x10, 0xac, 0x02, 0x12, 0x19, 0x0a, 0x14, 0x45, 0x52, 0x52, 0x5f, 0x53, 0x50, 0x4f, 0x54,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x49, 0x4e, 0x5f, 0x52, 0x4f, 0x4f, 0x4d, 0x10, 0xad, 0x02, 0x12,
	0x19, 0x0a, 0x14, 0x45, 0x52, 0x52, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x52, 0x4f, 0x4f, 0x4d,
	0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x10, 0xae, 0x02, 0x12, 0x1a, 0x0a, 0x15, 0x45, 0x52,
	0x52, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x4b, 0x45, 0x45, 0x50, 0x45, 0x4e, 0x54, 0x5f, 0x46,
	0x55, 0x4c, 0x4c, 0x10, 0xaf, 0x02, 0x12, 0x1f, 0x0a, 0x1a, 0x45, 0x52, 0x52, 0x5f, 0x53, 0x50,
	0x4f, 0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x57, 0x45, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x4c,
	0x49, 0x4d, 0x49, 0x54, 0x10, 0xb0, 0x02, 0x12, 0x1c, 0x0a, 0x17, 0x45, 0x52, 0x52, 0x5f, 0x53,
	0x50, 0x4f, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x45, 0x58, 0x49,
	0x53, 0x54, 0x10, 0xb1, 0x02, 0x12, 0x1e, 0x0a, 0x19, 0x45, 0x52, 0x52, 0x5f, 0x53, 0x50, 0x4f,
	0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x4f, 0x55, 0x47, 0x48, 0x5f, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x10, 0xb2, 0x02, 0x12, 0x16, 0x0a, 0x11, 0x45, 0x52, 0x52, 0x5f, 0x53, 0x50, 0x4f,
	0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x4f, 0x55, 0x54, 0x10, 0xb3, 0x02, 0x12, 0x1d, 0x0a,
	0x18, 0x45, 0x52, 0x52, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x49,
	0x4e, 0x5f, 0x4b, 0x45, 0x45, 0x50, 0x45, 0x4e, 0x54, 0x10, 0xb4, 0x02, 0x12, 0x1a, 0x0a, 0x15,
	0x45, 0x52, 0x52, 0x5f, 0x53, 0x50, 0x4f, 0x54, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x48, 0x41,
	0x53, 0x5f, 0x4f, 0x55, 0x54, 0x10, 0xb5, 0x02, 0x12, 0x1e, 0x0a, 0x19, 0x45, 0x52, 0x52, 0x5f,
	0x50, 0x41, 0x59, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x90, 0x03, 0x12, 0x19, 0x0a, 0x14, 0x45, 0x52, 0x52, 0x5f,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x10, 0x91, 0x03, 0x12, 0x1b, 0x0a, 0x16, 0x45, 0x52, 0x52, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52,
	0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x92, 0x03,
	0x12, 0x16, 0x0a, 0x11, 0x45, 0x52, 0x52, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x93, 0x03, 0x12, 0x1a, 0x0a, 0x15, 0x45, 0x52, 0x52, 0x5f,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x94, 0x03, 0x12, 0x1f, 0x0a, 0x1a, 0x45, 0x52, 0x52, 0x5f, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x5f, 0x55, 0x4e, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48,
	0x45, 0x44, 0x10, 0x95, 0x03, 0x12, 0x1a, 0x0a, 0x15, 0x45, 0x52, 0x52, 0x5f, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x59, 0x45, 0x54, 0x10, 0x96,
	0x03, 0x12, 0x19, 0x0a, 0x14, 0x45, 0x52, 0x52, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x43,
	0x4c, 0x4f, 0x53, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x97, 0x03, 0x12, 0x2a, 0x0a, 0x25,
	0x45, 0x52, 0x52, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59,
	0x5f, 0x50, 0x41, 0x59, 0x45, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x41, 0x4e,
	0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x98, 0x03, 0x12, 0x25, 0x0a, 0x20, 0x45, 0x52, 0x52, 0x5f,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x48, 0x41, 0x44,
	0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x55, 0x4d, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x99, 0x03, 0x12,
	0x2a, 0x0a, 0x25, 0x45, 0x52, 0x52, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x56, 0x45, 0x52,
	0x49, 0x46, 0x59, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e,
	0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x9a, 0x03, 0x12, 0x28, 0x0a, 0x23, 0x45,
	0x52, 0x52, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f,
	0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x4a, 0x53, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x9b, 0x03, 0x12, 0x2d, 0x0a, 0x28, 0x45, 0x52, 0x52, 0x5f, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x9c, 0x03, 0x12, 0x22, 0x0a, 0x1d, 0x45, 0x52, 0x52, 0x5f, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x43, 0x4f, 0x4e, 0x44,
	0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x9d, 0x03, 0x12, 0x1e, 0x0a, 0x19, 0x45, 0x52, 0x52, 0x5f,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x9e, 0x03, 0x12, 0x2c, 0x0a, 0x27, 0x45, 0x52, 0x52, 0x5f,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x4e, 0x4f,
	0x5f, 0x53, 0x55, 0x43, 0x48, 0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x10, 0x9f, 0x03, 0x12, 0x2d, 0x0a, 0x28, 0x45, 0x52, 0x52, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52,
	0x5f, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f,
	0x49, 0x44, 0x10, 0xa0, 0x03, 0x12, 0x2a, 0x0a, 0x25, 0x45, 0x52, 0x52, 0x5f, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xa1,
	0x03, 0x12, 0x1c, 0x0a, 0x17, 0x45, 0x52, 0x52, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xa2, 0x03, 0x12,
	0x26, 0x0a, 0x21, 0x45, 0x52, 0x52, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x42,
	0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45,
	0x5f, 0x41, 0x47, 0x45, 0x10, 0xa3, 0x03, 0x12, 0x1f, 0x0a, 0x1a, 0x45, 0x52, 0x52, 0x5f, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x4c, 0x49, 0x4d, 0x49,
	0x54, 0x5f, 0x41, 0x47, 0x45, 0x10, 0xa4, 0x03, 0x12, 0x1e, 0x0a, 0x19, 0x45, 0x52, 0x52, 0x5f,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x5f, 0x4c, 0x49, 0x4d, 0x49,
	0x54, 0x5f, 0x41, 0x47, 0x45, 0x10, 0xa5, 0x03, 0x12, 0x17, 0x0a, 0x12, 0x45, 0x52, 0x52, 0x5f,
	0x54, 0x41, 0x53, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0xf4,
	0x03, 0x12, 0x18, 0x0a, 0x13, 0x45, 0x52, 0x52, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x10, 0xf5, 0x03, 0x12, 0x19, 0x0a, 0x14, 0x45,
	0x52, 0x52, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x44, 0x49, 0x53, 0x50,
	0x4f, 0x53, 0x45, 0x10, 0xf6, 0x03, 0x12, 0x17, 0x0a, 0x12, 0x45, 0x52, 0x52, 0x5f, 0x52, 0x41,
	0x4e, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0xd8, 0x04, 0x12,
	0x16, 0x0a, 0x11, 0x45, 0x52, 0x52, 0x5f, 0x52, 0x41, 0x4e, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x4f, 0x50, 0x45, 0x4e, 0x10, 0xd9, 0x04, 0x12, 0x1b, 0x0a, 0x16, 0x45, 0x52, 0x52, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53,
	0x54, 0x10, 0xbc, 0x05, 0x12, 0x1d, 0x0a, 0x18, 0x45, 0x52, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x49, 0x54, 0x59, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x45, 0x44,
	0x10, 0xbd, 0x05, 0x12, 0x17, 0x0a, 0x12, 0x45, 0x52, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56,
	0x49, 0x54, 0x59, 0x5f, 0x45, 0x4e, 0x44, 0x45, 0x44, 0x10, 0xbe, 0x05, 0x12, 0x21, 0x0a, 0x1c,
	0x45, 0x52, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x41, 0x4c, 0x52,
	0x45, 0x41, 0x44, 0x59, 0x5f, 0x43, 0x4c, 0x41, 0x49, 0x4d, 0x45, 0x44, 0x10, 0xbf, 0x05, 0x12,
	0x1f, 0x0a, 0x1a, 0x45, 0x52, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0xc0, 0x05,
	0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x2e, 0x6b, 0x65, 0x65, 0x70, 0x66, 0x61, 0x6e, 0x63,
	0x79, 0x2e, 0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x2d, 0x65, 0x6e, 0x64, 0x2f, 0x66,
	0x61, 0x6e, 0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x63, 0x70, 0x62, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x50, 0x42, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_errors_proto_rawDescOnce sync.Once
	file_errors_proto_rawDescData = file_errors_proto_rawDesc
)

func file_errors_proto_rawDescGZIP() []byte {
	file_errors_proto_rawDescOnce.Do(func() {
		file_errors_proto_rawDescData = protoimpl.X.CompressGZIP(file_errors_proto_rawDescData)
	})
	return file_errors_proto_rawDescData
}

var file_errors_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_errors_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_errors_proto_goTypes = []interface{}{
	(ErrCode)(0),          // 0: common.ErrCode
	(*Result)(nil),        // 1: common.Result
	(*PaginationReq)(nil), // 2: common.PaginationReq
}
var file_errors_proto_depIdxs = []int32{
	0, // 0: common.Result.code:type_name -> common.ErrCode
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_errors_proto_init() }
func file_errors_proto_init() {
	if File_errors_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_errors_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_errors_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaginationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_errors_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_errors_proto_goTypes,
		DependencyIndexes: file_errors_proto_depIdxs,
		EnumInfos:         file_errors_proto_enumTypes,
		MessageInfos:      file_errors_proto_msgTypes,
	}.Build()
	File_errors_proto = out.File
	file_errors_proto_rawDesc = nil
	file_errors_proto_goTypes = nil
	file_errors_proto_depIdxs = nil
}
