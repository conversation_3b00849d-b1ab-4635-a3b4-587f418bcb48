// 登录模块协议

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: task.proto

package taskPB

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 获取任务列表
type GetTaskListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category common.TASK_CATEGORY `protobuf:"varint,1,opt,name=category,proto3,enum=common.TASK_CATEGORY" json:"category,omitempty"`
}

func (x *GetTaskListReq) Reset() {
	*x = GetTaskListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaskListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskListReq) ProtoMessage() {}

func (x *GetTaskListReq) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskListReq.ProtoReflect.Descriptor instead.
func (*GetTaskListReq) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{0}
}

func (x *GetTaskListReq) GetCategory() common.TASK_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.TASK_CATEGORY(0)
}

type GetTaskListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result       `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	TaskList []*common.TaskInfo   `protobuf:"bytes,2,rep,name=task_list,json=taskList,proto3" json:"task_list,omitempty"`
	Category common.TASK_CATEGORY `protobuf:"varint,3,opt,name=category,proto3,enum=common.TASK_CATEGORY" json:"category,omitempty"`
}

func (x *GetTaskListRsp) Reset() {
	*x = GetTaskListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaskListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskListRsp) ProtoMessage() {}

func (x *GetTaskListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskListRsp.ProtoReflect.Descriptor instead.
func (*GetTaskListRsp) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{1}
}

func (x *GetTaskListRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetTaskListRsp) GetTaskList() []*common.TaskInfo {
	if x != nil {
		return x.TaskList
	}
	return nil
}

func (x *GetTaskListRsp) GetCategory() common.TASK_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.TASK_CATEGORY(0)
}

// 下发推送更新
type UpdateTaskNTF struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret  *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Info []*common.TaskInfo `protobuf:"bytes,2,rep,name=info,proto3" json:"info,omitempty"` // 任务信息
}

func (x *UpdateTaskNTF) Reset() {
	*x = UpdateTaskNTF{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTaskNTF) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTaskNTF) ProtoMessage() {}

func (x *UpdateTaskNTF) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTaskNTF.ProtoReflect.Descriptor instead.
func (*UpdateTaskNTF) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateTaskNTF) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *UpdateTaskNTF) GetInfo() []*common.TaskInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

// 领取奖励
type RewardTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId   int64                `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"` // 任务id
	Category common.TASK_CATEGORY `protobuf:"varint,2,opt,name=category,proto3,enum=common.TASK_CATEGORY" json:"category,omitempty"`
}

func (x *RewardTaskReq) Reset() {
	*x = RewardTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardTaskReq) ProtoMessage() {}

func (x *RewardTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardTaskReq.ProtoReflect.Descriptor instead.
func (*RewardTaskReq) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{3}
}

func (x *RewardTaskReq) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *RewardTaskReq) GetCategory() common.TASK_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.TASK_CATEGORY(0)
}

type RewardTaskRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result   `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Info   *common.TaskInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"` // 任务信息
	Reward *common.Reward   `protobuf:"bytes,3,opt,name=reward,proto3" json:"reward,omitempty"`
}

func (x *RewardTaskRsp) Reset() {
	*x = RewardTaskRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardTaskRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardTaskRsp) ProtoMessage() {}

func (x *RewardTaskRsp) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardTaskRsp.ProtoReflect.Descriptor instead.
func (*RewardTaskRsp) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{4}
}

func (x *RewardTaskRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *RewardTaskRsp) GetInfo() *common.TaskInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *RewardTaskRsp) GetReward() *common.Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

// 获取奖励进度请求
type TaskProgressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category common.TASK_CATEGORY `protobuf:"varint,1,opt,name=category,proto3,enum=common.TASK_CATEGORY" json:"category,omitempty"` // 任务类型
}

func (x *TaskProgressReq) Reset() {
	*x = TaskProgressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskProgressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskProgressReq) ProtoMessage() {}

func (x *TaskProgressReq) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskProgressReq.ProtoReflect.Descriptor instead.
func (*TaskProgressReq) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{5}
}

func (x *TaskProgressReq) GetCategory() common.TASK_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.TASK_CATEGORY(0)
}

// 获取奖励进度响应
type TaskProgressRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result         `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Category common.TASK_CATEGORY   `protobuf:"varint,2,opt,name=category,proto3,enum=common.TASK_CATEGORY" json:"category,omitempty"` // 任务类型
	List     []*common.TaskProgress `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`                                    // 进度数据
}

func (x *TaskProgressRsp) Reset() {
	*x = TaskProgressRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskProgressRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskProgressRsp) ProtoMessage() {}

func (x *TaskProgressRsp) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskProgressRsp.ProtoReflect.Descriptor instead.
func (*TaskProgressRsp) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{6}
}

func (x *TaskProgressRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *TaskProgressRsp) GetCategory() common.TASK_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.TASK_CATEGORY(0)
}

func (x *TaskProgressRsp) GetList() []*common.TaskProgress {
	if x != nil {
		return x.List
	}
	return nil
}

// 广播协议
type TaskProgressNTF struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category common.TASK_CATEGORY   `protobuf:"varint,2,opt,name=category,proto3,enum=common.TASK_CATEGORY" json:"category,omitempty"` // 任务类型
	List     []*common.TaskProgress `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`                                    // 有变化的进度
}

func (x *TaskProgressNTF) Reset() {
	*x = TaskProgressNTF{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskProgressNTF) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskProgressNTF) ProtoMessage() {}

func (x *TaskProgressNTF) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskProgressNTF.ProtoReflect.Descriptor instead.
func (*TaskProgressNTF) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{7}
}

func (x *TaskProgressNTF) GetCategory() common.TASK_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.TASK_CATEGORY(0)
}

func (x *TaskProgressNTF) GetList() []*common.TaskProgress {
	if x != nil {
		return x.List
	}
	return nil
}

// 领取奖励进度请求
type TaskProgressRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category common.TASK_CATEGORY `protobuf:"varint,1,opt,name=category,proto3,enum=common.TASK_CATEGORY" json:"category,omitempty"` // 任务类型
	SubId    int64                `protobuf:"varint,2,opt,name=sub_id,json=subId,proto3" json:"sub_id,omitempty"`                    // 子类型
	Index    int64                `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty"`                                 // 领取索引
}

func (x *TaskProgressRewardReq) Reset() {
	*x = TaskProgressRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskProgressRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskProgressRewardReq) ProtoMessage() {}

func (x *TaskProgressRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskProgressRewardReq.ProtoReflect.Descriptor instead.
func (*TaskProgressRewardReq) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{8}
}

func (x *TaskProgressRewardReq) GetCategory() common.TASK_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.TASK_CATEGORY(0)
}

func (x *TaskProgressRewardReq) GetSubId() int64 {
	if x != nil {
		return x.SubId
	}
	return 0
}

func (x *TaskProgressRewardReq) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

// 领取奖励进度返回
type TaskProgressRewardRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result       `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Info   *common.TaskProgress `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`     // 进度信息
	Reward *common.Reward       `protobuf:"bytes,3,opt,name=reward,proto3" json:"reward,omitempty"` // 奖励信息
}

func (x *TaskProgressRewardRsp) Reset() {
	*x = TaskProgressRewardRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskProgressRewardRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskProgressRewardRsp) ProtoMessage() {}

func (x *TaskProgressRewardRsp) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskProgressRewardRsp.ProtoReflect.Descriptor instead.
func (*TaskProgressRewardRsp) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{9}
}

func (x *TaskProgressRewardRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *TaskProgressRewardRsp) GetInfo() *common.TaskProgress {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *TaskProgressRewardRsp) GetReward() *common.Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

var File_task_proto protoreflect.FileDescriptor

var file_task_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x74, 0x61,
	0x73, 0x6b, 0x50, 0x42, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x43, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x31,
	0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x22, 0x94, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2d, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74, 0x61, 0x73,
	0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x52, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x57, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x4e, 0x54, 0x46, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x24, 0x0a, 0x04, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66,
	0x6f, 0x22, 0x5b, 0x0a, 0x0d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x7f,
	0x0a, 0x0d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x73, 0x70, 0x12,
	0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65,
	0x74, 0x12, 0x24, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x22,
	0x44, 0x0a, 0x0f, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x41,
	0x53, 0x4b, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x52, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x90, 0x01, 0x0a, 0x0f, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x31, 0x0a, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x28,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x6e, 0x0a, 0x0f, 0x54, 0x61, 0x73, 0x6b,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x4e, 0x54, 0x46, 0x12, 0x31, 0x0a, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x28,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x77, 0x0a, 0x15, 0x54, 0x61, 0x73, 0x6b,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x41, 0x53,
	0x4b, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x75, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x75, 0x62, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x22, 0x8b, 0x01, 0x0a, 0x15, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x28, 0x0a,
	0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x42,
	0x3d, 0x5a, 0x3b, 0x67, 0x69, 0x74, 0x2e, 0x6b, 0x65, 0x65, 0x70, 0x66, 0x61, 0x6e, 0x63, 0x79,
	0x2e, 0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x2d, 0x65, 0x6e, 0x64, 0x2f, 0x66, 0x61,
	0x6e, 0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63,
	0x70, 0x62, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x3b, 0x74, 0x61, 0x73, 0x6b, 0x50, 0x42, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_task_proto_rawDescOnce sync.Once
	file_task_proto_rawDescData = file_task_proto_rawDesc
)

func file_task_proto_rawDescGZIP() []byte {
	file_task_proto_rawDescOnce.Do(func() {
		file_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_task_proto_rawDescData)
	})
	return file_task_proto_rawDescData
}

var file_task_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_task_proto_goTypes = []interface{}{
	(*GetTaskListReq)(nil),        // 0: taskPB.GetTaskListReq
	(*GetTaskListRsp)(nil),        // 1: taskPB.GetTaskListRsp
	(*UpdateTaskNTF)(nil),         // 2: taskPB.UpdateTaskNTF
	(*RewardTaskReq)(nil),         // 3: taskPB.RewardTaskReq
	(*RewardTaskRsp)(nil),         // 4: taskPB.RewardTaskRsp
	(*TaskProgressReq)(nil),       // 5: taskPB.TaskProgressReq
	(*TaskProgressRsp)(nil),       // 6: taskPB.TaskProgressRsp
	(*TaskProgressNTF)(nil),       // 7: taskPB.TaskProgressNTF
	(*TaskProgressRewardReq)(nil), // 8: taskPB.TaskProgressRewardReq
	(*TaskProgressRewardRsp)(nil), // 9: taskPB.TaskProgressRewardRsp
	(common.TASK_CATEGORY)(0),     // 10: common.TASK_CATEGORY
	(*common.Result)(nil),         // 11: common.Result
	(*common.TaskInfo)(nil),       // 12: common.TaskInfo
	(*common.Reward)(nil),         // 13: common.Reward
	(*common.TaskProgress)(nil),   // 14: common.TaskProgress
}
var file_task_proto_depIdxs = []int32{
	10, // 0: taskPB.GetTaskListReq.category:type_name -> common.TASK_CATEGORY
	11, // 1: taskPB.GetTaskListRsp.ret:type_name -> common.Result
	12, // 2: taskPB.GetTaskListRsp.task_list:type_name -> common.TaskInfo
	10, // 3: taskPB.GetTaskListRsp.category:type_name -> common.TASK_CATEGORY
	11, // 4: taskPB.UpdateTaskNTF.ret:type_name -> common.Result
	12, // 5: taskPB.UpdateTaskNTF.info:type_name -> common.TaskInfo
	10, // 6: taskPB.RewardTaskReq.category:type_name -> common.TASK_CATEGORY
	11, // 7: taskPB.RewardTaskRsp.ret:type_name -> common.Result
	12, // 8: taskPB.RewardTaskRsp.info:type_name -> common.TaskInfo
	13, // 9: taskPB.RewardTaskRsp.reward:type_name -> common.Reward
	10, // 10: taskPB.TaskProgressReq.category:type_name -> common.TASK_CATEGORY
	11, // 11: taskPB.TaskProgressRsp.ret:type_name -> common.Result
	10, // 12: taskPB.TaskProgressRsp.category:type_name -> common.TASK_CATEGORY
	14, // 13: taskPB.TaskProgressRsp.list:type_name -> common.TaskProgress
	10, // 14: taskPB.TaskProgressNTF.category:type_name -> common.TASK_CATEGORY
	14, // 15: taskPB.TaskProgressNTF.list:type_name -> common.TaskProgress
	10, // 16: taskPB.TaskProgressRewardReq.category:type_name -> common.TASK_CATEGORY
	11, // 17: taskPB.TaskProgressRewardRsp.ret:type_name -> common.Result
	14, // 18: taskPB.TaskProgressRewardRsp.info:type_name -> common.TaskProgress
	13, // 19: taskPB.TaskProgressRewardRsp.reward:type_name -> common.Reward
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_task_proto_init() }
func file_task_proto_init() {
	if File_task_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_task_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaskListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaskListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTaskNTF); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardTaskRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskProgressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskProgressRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskProgressNTF); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskProgressRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskProgressRewardRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_task_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_task_proto_goTypes,
		DependencyIndexes: file_task_proto_depIdxs,
		MessageInfos:      file_task_proto_msgTypes,
	}.Build()
	File_task_proto = out.File
	file_task_proto_rawDesc = nil
	file_task_proto_goTypes = nil
	file_task_proto_depIdxs = nil
}
