// 数据分析

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: ta.proto

package taPB

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 数据上报请求
type DataReportReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReportType common.DATA_REPORT_TYPE `protobuf:"varint,1,opt,name=report_type,json=reportType,proto3,enum=common.DATA_REPORT_TYPE" json:"report_type,omitempty"` // 上报类型
	ReportData string                  `protobuf:"bytes,2,opt,name=report_data,json=reportData,proto3" json:"report_data,omitempty"`                               // 上报内容
}

func (x *DataReportReq) Reset() {
	*x = DataReportReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ta_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataReportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataReportReq) ProtoMessage() {}

func (x *DataReportReq) ProtoReflect() protoreflect.Message {
	mi := &file_ta_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataReportReq.ProtoReflect.Descriptor instead.
func (*DataReportReq) Descriptor() ([]byte, []int) {
	return file_ta_proto_rawDescGZIP(), []int{0}
}

func (x *DataReportReq) GetReportType() common.DATA_REPORT_TYPE {
	if x != nil {
		return x.ReportType
	}
	return common.DATA_REPORT_TYPE(0)
}

func (x *DataReportReq) GetReportData() string {
	if x != nil {
		return x.ReportData
	}
	return ""
}

type DataReportRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
}

func (x *DataReportRsp) Reset() {
	*x = DataReportRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ta_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataReportRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataReportRsp) ProtoMessage() {}

func (x *DataReportRsp) ProtoReflect() protoreflect.Message {
	mi := &file_ta_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataReportRsp.ProtoReflect.Descriptor instead.
func (*DataReportRsp) Descriptor() ([]byte, []int) {
	return file_ta_proto_rawDescGZIP(), []int{1}
}

func (x *DataReportRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// 批量数据上报请求
type BatchDataReportReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reports []*DataReportReq `protobuf:"bytes,1,rep,name=reports,proto3" json:"reports,omitempty"` // 上报列表
}

func (x *BatchDataReportReq) Reset() {
	*x = BatchDataReportReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ta_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDataReportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDataReportReq) ProtoMessage() {}

func (x *BatchDataReportReq) ProtoReflect() protoreflect.Message {
	mi := &file_ta_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDataReportReq.ProtoReflect.Descriptor instead.
func (*BatchDataReportReq) Descriptor() ([]byte, []int) {
	return file_ta_proto_rawDescGZIP(), []int{2}
}

func (x *BatchDataReportReq) GetReports() []*DataReportReq {
	if x != nil {
		return x.Reports
	}
	return nil
}

type BatchDataReportRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
}

func (x *BatchDataReportRsp) Reset() {
	*x = BatchDataReportRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ta_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDataReportRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDataReportRsp) ProtoMessage() {}

func (x *BatchDataReportRsp) ProtoReflect() protoreflect.Message {
	mi := &file_ta_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDataReportRsp.ProtoReflect.Descriptor instead.
func (*BatchDataReportRsp) Descriptor() ([]byte, []int) {
	return file_ta_proto_rawDescGZIP(), []int{3}
}

func (x *BatchDataReportRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

var File_ta_proto protoreflect.FileDescriptor

var file_ta_proto_rawDesc = []byte{
	0x0a, 0x08, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x74, 0x61, 0x50, 0x42,
	0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6b, 0x0a, 0x0d, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x39, 0x0a, 0x0b, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x52,
	0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x31, 0x0a, 0x0d, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x22, 0x43, 0x0a, 0x12, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x2d, 0x0a, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x74, 0x61, 0x50, 0x42, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x52, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x22,
	0x36, 0x0a, 0x12, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x42, 0x39, 0x5a, 0x37, 0x67, 0x69, 0x74, 0x2e, 0x6b,
	0x65, 0x65, 0x70, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2e, 0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61, 0x63,
	0x6b, 0x2d, 0x65, 0x6e, 0x64, 0x2f, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x70, 0x62, 0x2f, 0x74, 0x61, 0x3b, 0x74, 0x61,
	0x50, 0x42, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ta_proto_rawDescOnce sync.Once
	file_ta_proto_rawDescData = file_ta_proto_rawDesc
)

func file_ta_proto_rawDescGZIP() []byte {
	file_ta_proto_rawDescOnce.Do(func() {
		file_ta_proto_rawDescData = protoimpl.X.CompressGZIP(file_ta_proto_rawDescData)
	})
	return file_ta_proto_rawDescData
}

var file_ta_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_ta_proto_goTypes = []interface{}{
	(*DataReportReq)(nil),        // 0: taPB.DataReportReq
	(*DataReportRsp)(nil),        // 1: taPB.DataReportRsp
	(*BatchDataReportReq)(nil),   // 2: taPB.BatchDataReportReq
	(*BatchDataReportRsp)(nil),   // 3: taPB.BatchDataReportRsp
	(common.DATA_REPORT_TYPE)(0), // 4: common.DATA_REPORT_TYPE
	(*common.Result)(nil),        // 5: common.Result
}
var file_ta_proto_depIdxs = []int32{
	4, // 0: taPB.DataReportReq.report_type:type_name -> common.DATA_REPORT_TYPE
	5, // 1: taPB.DataReportRsp.ret:type_name -> common.Result
	0, // 2: taPB.BatchDataReportReq.reports:type_name -> taPB.DataReportReq
	5, // 3: taPB.BatchDataReportRsp.ret:type_name -> common.Result
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_ta_proto_init() }
func file_ta_proto_init() {
	if File_ta_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ta_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataReportReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ta_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataReportRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ta_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDataReportReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ta_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDataReportRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ta_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_ta_proto_goTypes,
		DependencyIndexes: file_ta_proto_depIdxs,
		MessageInfos:      file_ta_proto_msgTypes,
	}.Build()
	File_ta_proto = out.File
	file_ta_proto_rawDesc = nil
	file_ta_proto_goTypes = nil
	file_ta_proto_depIdxs = nil
}
