package config

import (
	"fmt"
	"sync"
)

// InitFunc 是初始化函数的类型
type InitFunc func() error

// ServiceConfig 用于存储每个服务的初始化函数
type ServiceConfig struct {
	initFunctions map[string]InitFunc
	mu            sync.Mutex
}

// NewServiceConfig 创建一个新的服务配置实例
func NewServiceConfig() *ServiceConfig {
	return &ServiceConfig{
		initFunctions: make(map[string]InitFunc),
	}
}

// Register 注册一个初始化函数
func (sc *ServiceConfig) Register(name string, fn InitFunc) {
	sc.mu.Lock()
	defer sc.mu.Unlock()
	if _, exists := sc.initFunctions[name]; !exists {
		sc.initFunctions[name] = fn
	}
}

// ExecuteAll 执行所有已注册的初始化函数
func (sc *ServiceConfig) ExecuteAll() error {
	sc.mu.Lock()
	defer sc.mu.Unlock()
	for name, initFunc := range sc.initFunctions {
		if err := initFunc(); err != nil {
			return fmt.Errorf("failed to init %s: %w", name, err)
		}
	}
	return nil
}