package crpc_hook

import (
	"sync"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_common"
	hookRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hookrpc"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

type HookRpcClient struct {
}

var (
	once     = &sync.Once{}
	instance *HookRpcClient
)

func GetHookRpcInstance() *HookRpcClient {
	if instance != nil {
		return instance
	}

	once.Do(func() {
		instance = &HookRpcClient{}
	})
	return instance
}

// GetHookRpcClient 获取HookRpc服务的client
func (h *HookRpcClient) GetHookRpcClient() hookRpc.HookServiceClient {
	// 区分rpc 到hook的服务
	// TODO 这里后续删除掉
	hookSrvType := viper.GetInt32("rpc_hook_server")

	hookSrvName := dict_common.HookSrv
	if hookSrvType == 2 {
		hookSrvName = dict_common.Hook2Srv
	}

	cc, err := grpcHa.GetConnection(hookSrvName)

	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return hookRpc.NewHookServiceClient(cc)
}
