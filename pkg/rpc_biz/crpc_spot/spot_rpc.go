package crpc_spot

import (
	"sync"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_common"
	spotRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/spotrpc"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"github.com/sirupsen/logrus"
)

type SpotRpcClient struct {
}

var (
	once     = &sync.Once{}
	instance *SpotRpcClient
)

func GetSpotRpcInstance() *SpotRpcClient {
	if instance != nil {
		return instance
	}

	once.Do(func() {
		instance = &SpotRpcClient{}
	})
	return instance
}

// SpotRpcClient 获取SpotRpc服务的client
func (h *SpotRpcClient) GetSpotRpcClient() spotRpc.SpotServiceClient {
	cc, err := grpcHa.GetConnection(dict_common.SpotSrv)

	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return spotRpc.NewSpotServiceClient(cc)
}
