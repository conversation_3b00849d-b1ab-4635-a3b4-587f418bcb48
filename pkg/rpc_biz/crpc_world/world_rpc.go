package crpc_world

import (
	"context"
	"fmt"
	"sync"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_common"
	worldRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/worldrpc"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"github.com/sirupsen/logrus"
)

type WorldRpcClient struct {
}

var (
	once     = &sync.Once{}
	instance *WorldRpcClient
)

func GetWorldRpcInstance() *WorldRpcClient {
	if instance != nil {
		return instance
	}

	once.Do(func() {
		instance = &WorldRpcClient{}
	})
	return instance
}

// GetWorldRpcClient 获取WorldRpc服务的client
func (u *WorldRpcClient) GetWorldRpcClient() worldRpc.WorldServiceClient {
	cc, err := grpcHa.GetConnection(dict_common.WorldSrv)

	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return worldRpc.NewWorldServiceClient(cc)
}

// RpcGetWeather 获取现在天气
func RpcGetWeather(ctx context.Context, pondID int64) (*worldRpc.WeatherRsp, error) {
	rpcClient := GetWorldRpcInstance().GetWorldRpcClient()
	if rpcClient == nil {
		return nil, fmt.Errorf("world rpc client is nil")
	}

	reqRpc := &worldRpc.WeatherReq{PondId: pondID}

	hRet, errRet := rpcClient.GetWeather(ctx, reqRpc)
	return hRet, errRet
}
