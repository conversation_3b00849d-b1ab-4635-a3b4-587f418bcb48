package crpc_user

import (
	"context"
	"fmt"
	"sync"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_common"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"github.com/sirupsen/logrus"
)

type UserRpcClient struct {
}

var (
	once     = &sync.Once{}
	instance *UserRpcClient
)

func GetUserRpcInstance() *UserRpcClient {
	if instance != nil {
		return instance
	}

	once.Do(func() {
		instance = &UserRpcClient{}
	})
	return instance
}

// GetUserRpcClient 获取UserRpc服务的client
func (u *UserRpcClient) GetUserRpcClient() userRpc.UserServiceClient {
	cc, err := grpcHa.GetConnection(dict_common.UserSrv)

	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return userRpc.NewUserServiceClient(cc)
}

// RpcGetPlayerInfo RPC到UserSrv GetPlayerInfo
func RpcGetPlayerInfo(ctx context.Context, productId int32, playerID uint64) (*userRpc.GetPlayerInfoRsp, error) {
	userRpcClient := GetUserRpcInstance().GetUserRpcClient()

	if userRpcClient == nil {
		return nil, fmt.Errorf("user rpc client is nil")
	}

	reqRpc := &userRpc.GetPlayerInfoReq{
		ProductId: productId,
		PlayerId:  playerID,
	}

	hRet, errRet := userRpcClient.GetPlayerInfo(ctx, reqRpc)
	return hRet, errRet
}

// RpcUpdatePlayerInfo 修改玩家信息
func RpcUpdatePlayerInfo(ctx context.Context, productId int32, playerID uint64, updateParams map[string]string) (*userRpc.UpdatePlayerInfoRsp, error) {
	if updateParams == nil {
		return nil, fmt.Errorf("updateParams is nil")
	}

	userRpcClient := GetUserRpcInstance().GetUserRpcClient()

	if userRpcClient == nil {
		return nil, fmt.Errorf("user rpc client is nil")
	}

	reqRpc := &userRpc.UpdatePlayerInfoReq{
		ProductId: productId,
		PlayerId:  playerID,
		UpdateMap: updateParams,
	}
	hRet, errRet := userRpcClient.UpdatePlayerInfo(ctx, reqRpc)
	return hRet, errRet
}

// RpcRealNameAuthQuery 实名认证状态查询
func RpcRealNameAuthQuery(ctx context.Context, productId int32, playerID uint64) (*userRpc.RealNameAuthQueryRsp, error) {
	userRpcClient := GetUserRpcInstance().GetUserRpcClient()

	if userRpcClient == nil {
		return nil, fmt.Errorf("user rpc client is nil")
	}

	reqRpc := &userRpc.RealNameAuthQueryReq{
		ProductId: productId,
		PlayerId:  playerID,
	}
	hRet, errRet := userRpcClient.RealNameAuthQuery(ctx, reqRpc)
	return hRet, errRet
}

// RpcRealNameAuthUpdate 更新实名认证状态
func RpcRealNameAuthUpdate(ctx context.Context, req *userRpc.RealNameAuthReq) (*userRpc.RealNameAuthRsp, error) {

	userRpcClient := GetUserRpcInstance().GetUserRpcClient()

	if userRpcClient == nil {
		return nil, fmt.Errorf("user rpc client is nil")
	}

	ret, err := userRpcClient.RealNameAuth(ctx, req)

	return ret, err
}

// RpcQueryPlayerAgeInfo 查询用户年龄段
func RpcQueryPlayerAgeInfo(ctx context.Context, req *userRpc.PlayerAgeQueryReq) (*userRpc.PlayerAgeQueryRsp, error) {
	userRpcClient := GetUserRpcInstance().GetUserRpcClient()

	if userRpcClient == nil {
		return nil, fmt.Errorf("user rpc client is nil")
	}

	ret, err := userRpcClient.PlayerAgeQuery(ctx, req)

	return ret, err
}

// RpcQueryPlayerInfoMulti 批量查询玩家信息(查询不到跳过)
func RpcQueryPlayerInfoMulti(ctx context.Context, productId int32, playerIds []uint64) (map[uint64]*commonPB.RichUserInfo, error) {

	userRpcClient := GetUserRpcInstance().GetUserRpcClient()

	if userRpcClient == nil {
		return nil, fmt.Errorf("user rpc client is nil")
	}
	req := &userRpc.PlayerMultiQueryReq{
		ProductId: productId,
		PlayerId:  playerIds,
	}

	ret, err := userRpcClient.PlayerMultiQuery(ctx, req)
	if err != nil {
		return nil, err
	}

	return ret.PlayerInfo, nil
}

// RpcQueryPlayerBriefMulti 批量查询玩家简要信息(查询不到跳过)
func RpcQueryPlayerBriefMulti(ctx context.Context, playerIds []uint64) (map[uint64]*commonPB.BriefUserInfo, error) {
	userRpcClient := GetUserRpcInstance().GetUserRpcClient()

	if userRpcClient == nil {
		return nil, fmt.Errorf("user rpc client is nil")
	}
	req := &userRpc.BatchPlayerBriefInfoReq{
		ProductId:  int32(commonPB.PRODUCT_ID_PID_FISHER),
		PlayerList: playerIds,
	}

	ret, err := userRpcClient.BatchPlayerBriefInfo(ctx, req)
	if err != nil {
		return nil, err
	}

	playerInfoMap := make(map[uint64]*commonPB.BriefUserInfo, len(ret.GetPlayerInfo()))

	for _, v := range ret.PlayerInfo {
		playerInfoMap[v.PlayerId] = v
	}

	return playerInfoMap, nil
}

// RpcQueryPlayerExtendInfo 查询玩家拓展信息
func RpcQueryPlayerExtendInfo(ctx context.Context, req *userRpc.QueryPlayerExtendInfoReq) (*commonPB.ExtendUserInfo, error) {
	userRpcClient := GetUserRpcInstance().GetUserRpcClient()

	if userRpcClient == nil {
		return nil, fmt.Errorf("user rpc client is nil")
	}

	ret, err := userRpcClient.QueryPlayerExtendInfo(ctx, req)
	if err != nil || ret == nil {
		return nil, err
	}

	return ret.ExtendInfo, nil
}

// RpcUpdatePlayerExtendInfo 更新玩家拓展信息
func RpcUpdatePlayerExtendInfo(ctx context.Context, req *userRpc.UpdatePlayerExtendInfoReq) (*userRpc.UpdatePlayerExtendInfoRsp, error) {
	userRpcClient := GetUserRpcInstance().GetUserRpcClient()

	if userRpcClient == nil {
		return nil, fmt.Errorf("user rpc client is nil")
	}

	ret, err := userRpcClient.UpdatePlayerExtendInfo(ctx, req)

	return ret, err
}
