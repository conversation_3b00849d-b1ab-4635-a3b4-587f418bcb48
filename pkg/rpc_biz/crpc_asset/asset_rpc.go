package crpc_asset

import (
	"sync"

	assetRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/assetrpc"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_common"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"github.com/sirupsen/logrus"
)

type AssetRpcClient struct {
}

var (
	once   = &sync.Once{}
	single *AssetRpcClient
)

func GetAssetRpcInstance() *AssetRpcClient {
	if single != nil {
		return single
	}

	once.Do(func() {
		single = &AssetRpcClient{}
	})
	return single
}

// GetAssetRpcClient 获取asset rpc client
func (c *AssetRpcClient) GetAssetRpcClient() assetRpc.AssetServiceClient {
	cc, err := grpcHa.GetConnection(dict_common.AssetSrv)
	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return assetRpc.NewAssetServiceClient(cc)
}
