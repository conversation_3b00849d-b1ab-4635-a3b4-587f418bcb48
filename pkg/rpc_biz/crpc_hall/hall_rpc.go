package crpc_hall

import (
	"context"
	"fmt"
	"sync"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_common"
	hallRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hallrpc"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"github.com/sirupsen/logrus"
)

type HallRpcClient struct {
}

var (
	once     = &sync.Once{}
	instance *HallRpcClient
)

func GetHallRpcInstance() *HallRpcClient {
	if instance != nil {
		return instance
	}

	once.Do(func() {
		instance = &HallRpcClient{}
	})
	return instance
}

// GetHallRpcClient 获取HallRpc服务的client
func (h *HallRpcClient) GetHallRpcClient() hallRpc.HallServiceClient {
	cc, err := grpcHa.GetConnection(dict_common.HallSrv)

	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return hallRpc.NewHallServiceClient(cc)
}

// 通用接口
// RpcOptPlayerItem 操作玩家道具
func RpcOptPlayerItem(ctx context.Context, req *hallRpc.OptPlayerItemReq) (*commonPB.Reward, error) {
	rpcHallCli := GetHallRpcInstance().GetHallRpcClient()
	if rpcHallCli == nil {
		return nil, fmt.Errorf("rpc hall client is nil")
	}

	rsp, err := rpcHallCli.OptPlayerItem(ctx, req)
	if err != nil || rsp.Ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		logrus.Errorf("add player:%d item failed, req:%s, err:%v", req.GetPlayerId(), req.String(), err)
		return nil, err
	}

	return rsp.RewardInfo, nil
}

// 通用接口
// RpcQueryPlayerBaseInfo 查询玩家基础信息
func RpcQueryPlayerBaseInfo(ctx context.Context, playerId uint64) (*commonPB.PlayerBaseInfo, error) {
	if playerId <= 0 {
		return nil, fmt.Errorf("playerId is invalid")
	}

	rpcHallCli := GetHallRpcInstance().GetHallRpcClient()
	if rpcHallCli == nil {
		return nil, fmt.Errorf("rpc hall client is nil")
	}

	req := &hallRpc.QueryPlayerBaseInfoReq{
		PlayerId: playerId,
	}

	rsp, err := rpcHallCli.QueryPlayerBaseInfo(ctx, req)

	if err != nil || rsp.PlayerInfo == nil {
		logrus.Errorf("add player:%d item failed, req:%s, err:%v", req.GetPlayerId(), req.String(), err)
		return nil, err
	}

	return rsp.PlayerInfo, nil
}

// RpcQueryPlayerRodInfo 查询杆包
func RpcQueryPlayerRodInfo(ctx context.Context, playerId uint64, id int32) (*commonPB.RodBagInfo, error) {
	if playerId <= 0 {
		return nil, fmt.Errorf("playerId is invalid")
	}

	rpcHallCli := GetHallRpcInstance().GetHallRpcClient()
	if rpcHallCli == nil {
		return nil, fmt.Errorf("rpc hall client is nil")
	}

	req := &hallRpc.QueryPlayerRodInfoReq{
		PlayerId: playerId,
		Id:       id,
	}
	rsp, err := rpcHallCli.QueryPlayerRodInfo(ctx, req)
	if err != nil {
		return nil, err
	}

	return rsp.GetRodInfo(), err
}
