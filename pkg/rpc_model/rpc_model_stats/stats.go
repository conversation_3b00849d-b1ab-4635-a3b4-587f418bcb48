package rpc_model_stats

import (
	"context"
	"sync"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	consul_config "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_stats"
	"github.com/sirupsen/logrus"
)

type StatsMgr struct {
}

var (
	once              = &sync.Once{}
	singletonInstance *StatsMgr
)

func GetStatsMgrInstance() *StatsMgr {
	once.Do(func() {
		singletonInstance = &StatsMgr{}
		singletonInstance.Init()
	})
	return singletonInstance
}

func (mgr *StatsMgr) Init() {
}

// 鱼的统计数据
type FishStats struct {
	Id        int64 // 鱼id
	Count     int64 // 统计数量
	WeightMax int64 // 大小重量
	BestSize  int64 // 最好质量

}

// GetFishStats 获取鱼统计数据
func (mgr *StatsMgr) GetFishStats(ctx context.Context, productId int32, playerId uint64, fishSpecial int64) (*FishStats, error) {
	list, err := crpc_stats.RpcGetStatsList(ctx, productId, playerId)
	if err != nil {
		return nil, err
	}
	fishStats := &FishStats{
		Id: fishSpecial,
	}
	cfgMap := cmodel.GetAllStats(consul_config.WithGrpcCtx(ctx))
	if cfgMap == nil {
		logrus.Errorf("statsAllConfig not found")
		return nil, protox.CodeError(commonPB.ErrCode_ERR_CONF_ERROR, "statsALlConfig not found")
	}
	for _, item := range list {
		cfg, ok := cfgMap[item.Id]
		if !ok {
			logrus.Warnf("stats config not found:%+v", item.Id)
			continue
		}
		if cfg.Type == int32(commonPB.STATS_TYPE_ST_FISH) {
			if cfg.Target == fishSpecial {
				switch commonPB.StatsSubType(cfg.SubType) {
				case commonPB.StatsSubType_STS_FISH_WEIGHT_MAX:
					fishStats.WeightMax = item.Val
				case commonPB.StatsSubType_STS_FISH_BEST_SIZE:
					fishStats.BestSize = item.Val
				case commonPB.StatsSubType_STS_FISH_COUNT:
					fishStats.Count = item.Val
				}
			}
		}
	}
	return fishStats, nil
}
