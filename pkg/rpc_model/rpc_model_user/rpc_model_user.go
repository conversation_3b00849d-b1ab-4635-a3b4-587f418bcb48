package rpc_model_user

import (
	"context"
	"sync"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_user"
)

// TODO: commonPB richUserInfo

type InfoMgr struct {
}

var (
	once              = &sync.Once{}
	singletonInstance *InfoMgr
)

func GetUserInfoMgrInstance() *InfoMgr {
	if singletonInstance != nil {
		return singletonInstance
	}

	once.Do(func() {
		singletonInstance = &InfoMgr{}
		singletonInstance.Init()
	})
	return singletonInstance
}

func (s *InfoMgr) Init() {
}

// GetRichUser 查询用户全量信息
func (mgr *InfoMgr) GetRichUser(productId int32, playerId uint64) (*commonPB.RichUserInfo, error) {
	rsp, err := crpc_user.RpcGetPlayerInfo(context.Background(), productId, playerId)
	if err != nil {
		return nil, err
	}

	return rsp.RichUserInfo, nil
}

func (mgr *InfoMgr) GetBriefUser(playerId uint64) (*commonPB.BriefUserInfo, error) {
	return nil, nil
}
