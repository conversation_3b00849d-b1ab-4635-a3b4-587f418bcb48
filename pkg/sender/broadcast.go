package sender

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_gate"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"git.keepfancy.xyz/back-end/frameworks/kit/srv_mgr"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/proto"
)

// NotifyMultiPlayerMessage 推送消息给指定的多个玩家
func NotifyMultiPlayerMessage(ctx context.Context, playerIDs []uint64, msgID commonPB.MsgID, body proto.Message) error {
	return crpc_gate.GetGateClient().BroadcastPackageByPlayerID(ctx, playerIDs, msgID, body)
}

// NotifyToPlayer 推送消息给指定单个玩家
func NotifyToPlayer(ctx context.Context, playerID uint64, msgID commonPB.MsgID, body proto.Message) error {
	return crpc_gate.GetGateClient().SendPackageByPlayerID(ctx, playerID, msgID, body)
}

// BroadcastGlobal 全服广播
func BroadcastGlobal(ctx context.Context, cmd commonPB.MsgID, body proto.Message) error {
	return crpc_gate.GetGateClient().BroadcastGlobal(ctx, cmd, body)
}

// NotifyMultiPlayerAddrMessage 推送给指定地址的多个玩家
func NotifyMultiPlayerAddrMessage(ctx context.Context, playerAddr map[uint64]string, msgID commonPB.MsgID, body proto.Message) error {
	return crpc_gate.GetGateClient().BroadcastPackageByPlayerIdAddr(ctx, playerAddr, msgID, body)
}

// BroadCastCmd 全体服务执行
func BroadCastCmd(srvName string, fun func(*grpc.ClientConn) error) error {
	addrs := srv_mgr.GetAllSrvIpByName(srvName)

	for _, addr := range addrs {
		cc, err := grpcHa.GetConnectionByAddr(addr)
		if err != nil {
			logrus.Errorf("get connect fail srvName:%s addr:%s err:%v", srvName, addr, err)
			return err
		}

		err = fun(cc)
		if err != nil {
			logrus.Errorf("call fail srvName:%s addr:%s err:%v", srvName, addr, err)
			return err
		}
	}

	return nil
}
