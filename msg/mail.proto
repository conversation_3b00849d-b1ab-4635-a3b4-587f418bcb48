syntax = "proto3";
package mailPB;

option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall;mailPB";

import "errors.proto";
import "common.proto";
import "enum.proto";

// 获取邮件列表请求
message GetMailListReq {
  int32            page_index = 1; // 页码
  common.MAIL_TYPE mail_type  = 2; // 邮件类型
}

message GetMailListRsp {
  common.Result ret                           = 1;
  common.MAIL_TYPE mail_type                  = 2; // 邮件类型
  repeated common.MailDetailInfo  mail_list   = 3; // 邮件列表
  int32 unclaimed_count                       = 4; // 未领取邮件数量(总数)
  int32 total_size                            = 5; // 邮件总数量
  int32 unread_count                          = 6; // 未读邮件数量(总数)
}

// 读邮件请求
message ReadMailReq {
  uint64 mail_id             = 1; // 邮件ID
  common.MAIL_TYPE mail_type = 2; // 邮件类型
}

message ReadMailRsp {
  common.Result   ret        = 1;
  common.MAIL_TYPE mail_type = 2; // 邮件类型
  uint64 mail_id             = 3; // 邮件ID
}

// 领取附件奖励请求
message ClaimRewardAttachReq {
  bool claim_all             = 1;  // 是否领取全部
  uint64 mail_id             = 2;  // 邮件ID
  common.MAIL_TYPE mail_type = 3;  // 邮件类型
}

// 领取附件奖励响应 (具体奖励信息以奖励数据通知为准)
message ClaimRewardAttachRsp {
  common.Result ret             = 1;
  bool claim_all                = 2;  // 是否领取全部
  uint64 mail_id                = 3;  // 邮件ID
  common.MAIL_TYPE mail_type    = 4;  // 邮件类型
  common.ItemBaseList item_list = 5;  // 奖励列表
}

// 新邮件通知
message NewMailNotify {
  common.MailDetailInfo  mail_info = 1; // 邮件信息
}

// 新系统邮件通知
// todo 暂时用空结构体客户端收到后拉取系统邮件
message NewSystemMailNotify {
}

// 广播
message MsgBroadcastNtf {
  common.MsgBroadcastDetailInfo  msg_broadcast_info = 1; // 广播信息
}

// 读邮件请求
message AnnPopupInfoReq {
  common.CHANNEL_TYPE channel_id = 1; // 渠道id
  int32               id        = 2; // 拍脸图唯一id
  int32               enable    = 3; // 是否开启
}

message AnnPopupInfoRes {
  common.Result          ret         = 1;
  repeated AnnPopup      popup_info  = 2; // 拍脸图
}

// 弹窗公告配置
message AnnPopup {
  int32 id         = 1; // 唯一标识（自增主键）
  int32 priority   = 2; // 优先级（数值越小越优先）
  int32 channel_id = 3; // 渠道ID
  int32 pop_style  = 4; // 弹窗样式（1=有关闭按钮）

  // 文本内容配置（JSON格式）
  string ann_content = 5;

  // 用户行为配置（JSON格式）
  string ann_action = 6;

  // 展示条件配置（JSON格式）
  string ann_conditions = 7;

  bool  enable     = 8;       // 是否启用（默认true）
  int64 start_time = 9;  // 生效开始时间
  int64 end_time   = 10;   // 生效结束时间
}
