#!/bin/bash

# 获取脚本文件所在绝对路径(自动跟踪符号链接)
get_real_path(){
    local source="${BASH_SOURCE[0]}"
    while [ -h "$source" ]; do
        local dir="$( cd -P "$( dirname "$source" )" && pwd )"
        source="$( readlink "$source" )"
        [[ $source != /* ]] && source="$dir/$source"
    done
#    echo "$( cd -P "$( dirname "$source" )/.." && pwd )"
    echo "$( cd -P "$( dirname "$source" )" && pwd )"
}

ROOT=$(get_real_path)

source ${ROOT}/funcs/const.sh
cd ${ROOT}

usage(){
	echo "Usage: sh run.sh cmd:[build|start|stop|restart] srvName:[loginsrv|...|all]"
	echo "Usage: sh run.sh cmd:[pb|rpc] to gen pb or rpc"
	echo "Usage: sh run.sh cmd:event to gen event pb"
	echo "Usage: sh run.sh cmd:dkBuild srvName:[...] to gen docker"
	echo "Usage: sh run.sh cmd:dkfile srvName:[...] to gen dockerfile"
	exit 1
}

ApplicationName=$2

build() {
  echo "########### run build start ###########"
  # shellcheck disable=SC2086
  "$SCRIPT_PATH"/funcs/build.sh ${ApplicationName}
  echo "########### run build end ###########"
}

start() {
  echo "########### run start start ###########"
  "$SCRIPT_PATH"/funcs/start.sh "${ApplicationName}" "start"
  echo "########### run start end ###########"
}

stop() {
  echo "########### run stop start ###########"
  # shellcheck disable=SC2086
  $SCRIPT_PATH/funcs/start.sh ${ApplicationName} "stop"
  echo "########### run stop end ###########"
}

restart() {
  echo "########### run restart start ###########"
  "$SCRIPT_PATH"/funcs/start.sh "${ApplicationName}" "restart"
  echo "########### run restart end ###########"
}

refresh() {
  build
  restart
}

pbMaker() {
  echo "########### run pb start ###########"
  cd "$SCRIPT_PATH"/../protocols
  pwd
  ./pbmaker.sh
  echo "########### run pb end ###########"
}

rpcMaker() {
  echo "########### run rpc start ###########"
  cd "$SCRIPT_PATH"/../pkg/intranetrpc
  pwd
  ./rpc_maker.sh
  echo "########### run rpc end ###########"
}

eventMaker() {
  echo "########### eventPb start ###########"
  cd "$SCRIPT_PATH"/../pkg/pubsub
  pwd
  ./event_pb_maker.sh
  echo "########### eventPb end ###########"
}

dockerBuild(){
  echo "########### run docker build start ###########"
  "$SCRIPT_PATH"/funcs/docker.sh "${ApplicationName}" 
  echo "########### run docker build end ###########"
}

dockerfile() {
  echo "########### run dockerfile make start ###########"
  "$SCRIPT_PATH"/funcs/dkfile.sh "${ApplicationName}" 
  echo "########### run dockerfile make end ###########"
}

case "$1" in
	"build")
		build
		;;
	"start")
		start
		;;
	"refresh")  # 编译并且restart
    refresh
		;;
	"stop")
		stop
		;;
	"restart")
		restart
		;;
	"pb")
		pbMaker
		;;
	"rpc")
		rpcMaker
		;;
  "dkBuild")
    dockerBuild
    ;;
  "dkFile")
    dockerfile 
    ;;
  "event")
  eventMaker
  ;;
	*)
		usage
		;;
esac

