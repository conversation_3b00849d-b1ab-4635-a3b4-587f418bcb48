name: fisher

services:
  gatewaysrv:
    build:
      context: ${GOPATH}/src/fancygame/gatewaysrv
      additional_contexts:
        basesrc: ${GOPATH}/src/base
        pkg: ${GOPATH}/pkg
    image: ************:8089/fisher/gatewaysrv
    container_name: gatewaysrv
    network_mode: "host"  # 使用配置表网络穿透
    volumes:
      - ${GOPATH}/src/fancygame/dkconfig/gatewaysrv.yml:/app/fancygame/bin/gatewaysrv/config.yml

  usersrv:
    build:
      context: ${GOPATH}/src/fancygame/usersrv
      additional_contexts:
        basesrc: ${GOPATH}/src/base
        pkg: ${GOPATH}/pkg
    image: ************:8089/fisher/usersrv
    container_name: usersrv
    network_mode: "host"
    volumes:
      - ${GOPATH}/src/fancygame/dkconfig/usersrv.yml:/app/fancygame/bin/usersrv/config.yml

  loginsrv:
    build:
      context: ${GOPATH}/src/fancygame/loginsrv
      additional_contexts:
        basesrc: ${GOPATH}/src/base
        pkg: ${GOPATH}/pkg
    image: ************:8089/fisher/loginsrv
    container_name: loginsrv
    network_mode: "host"
    volumes:
      - ${GOPATH}/src/fancygame/dkconfig/loginsrv.yml:/app/fancygame/bin/loginsrv/config.yml
    
  gmsrv:
    build:
      context: ${GOPATH}/src/fancygame/gmsrv
      additional_contexts:
        basesrc: ${GOPATH}/src/base
        pkg: ${GOPATH}/pkg
    image: ************:8089/fisher/gmsrv
    container_name: gmsrv
    network_mode: "host"
    volumes:
      - ${GOPATH}/src/fancygame/dkconfig/gmsrv.yml:/app/fancygame/bin/gmsrv/config.yml
    
  assetsrv:
    build:
      context: ${GOPATH}/src/fancygame/assetsrv
      additional_contexts:
        basesrc: ${GOPATH}/src/base
        pkg: ${GOPATH}/pkg
    image: ************:8089/fisher/assetsrv
    container_name: assetsrv
    network_mode: "host"
    volumes:
      - ${GOPATH}/src/fancygame/dkconfig/assetsrv.yml:/app/fancygame/bin/assetsrv/config.yml
    