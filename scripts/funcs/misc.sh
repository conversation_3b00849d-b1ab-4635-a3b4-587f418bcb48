#!/bin/bash

build_srv_list=$2

DOC[dkCpCfg]="初始化docker配置文件"
function fun_dkCpCfg() {
  echo  ">>>>> cpy docker config start <<<<<"
  ## 控制执行范围
  if [ "${build_srv_list}" == "all" ]; then
       # shellcheck disable=SC2206
       build_srv_list=(${SRV_LIST[@]})
  fi

  # shellcheck disable=SC2128
  echo target srv : "${build_srv_list[@]}"
  mkdir -p ${SRC_PATH}/fancygame/dkconfig
  for srvname in ${build_srv_list[@]}
  do
    if [[ ! -f "${SRC_PATH}/fancygame/dkconfig/${srvname}.yml" ]]; then
      # shellcheck disable=SC2164
      cd "$SERVER_PATH"/"${srvname}"
      pwd

      cp cmd/config.yml ${SRC_PATH}/fancygame/dkconfig/${srvname}.yml
      sed -i  's/\b[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\b/localhost/g' ${SRC_PATH}/fancygame/dkconfig/${srvname}.yml
    fi
  done
  echo ">>>>>>>> cpy docker config end >>>>>>>>"
}
