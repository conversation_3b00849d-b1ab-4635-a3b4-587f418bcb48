#!/bin/bash

. ./funcs/common.sh

server_name=$1 #目标参数1.服务名称或all
command=$2 #目标命令
build_srv_list=(${SRV_LIST[*]}) #缺省所有的服务 all
commandList=(start restart stop)

function startSrv() {
    srvName=$1 #函数变量

    if pid_is_exist "${srvName}" ; then
      echo "$srvName 检查到正在运行,直接退出..."
      return
    else
      echo "$srvName 检查到没有运行"
    fi

    srv_bin_path=`getSrvBin ${srvName}`
    srv_exe_path=$srv_bin_path/${srvName}
    chmod a+x ${srv_bin_path}
    srv_conf_path=`getSrvConf ${srvName}`
#    srv_log_file=$srv_bin_path/${srvName}.log
    srv_log_file=$srv_bin_path/launch.log
#    echo ${srv_conf_path}
#    echo ${srv_log_file}
#    `nohup ${srv_exe_path} --config ${srv_conf_path} > ${srv_log_file} 2>&1 &`
    cd ${srv_bin_path}
    if [[ ! ${BASER_SRV} == "" ]]; then
        cp config.yml config.test.yml 
<<<<<<< HEAD
        sed -i  's/\b[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\b/localhost/g' config.test.yml
=======
        sed -i  "s/\b[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\b/${BASER_SRV}/g" config.test.yml
>>>>>>> test
    fi

    `nohup ${srv_exe_path} --config ${srv_conf_path} >/dev/null 2>${srv_log_file} &`
    sleep 2

    if pid_is_exist ${srvName} ; then
      echo "$srvName pid:${pid} 启动成功"
    else
        echo "$srvName 启动失败"
    fi

    return
}

function stopSrv() {
    pid=`getSrvPID $1`
    if [ -z "${pid}" ]; then
         echo "$1 没有在运行"
         return
    fi

    kill -9 $pid
    echo "$1 pid:$pid 停止运行"
}

main() {
  if [ ! ${server_name} == "all" ]; then
      build_srv_list=($server_name)
  fi
  echo "需要 ${command} 的SRV: ${build_srv_list[*]}"

   srvCheck $build_srv_list

  #检查是否有进程在跑
  # shellcheck disable=SC2068
  for srvName in ${build_srv_list[@]}
  do
    #备份老的服务和配置
    #具体服务启动
    if [ "$command" == "start" ]; then
        startSrv $srvName
    elif [ "$command" == "stop"   ]; then
        stopSrv $srvName
    elif [ "$command" == "restart"   ]; then
        stopSrv $srvName
        sleep 1
        startSrv $srvName
    else
        echo "unknow commond"
    fi
    sleep 1
  done
}

############## PROG #######################
main