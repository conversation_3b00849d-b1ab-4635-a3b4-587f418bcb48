#!/bin/bash

. ./funcs/common.sh

build_srv_list=$1

# 构建基础包
function build_base() {
  echo "build base start"
  cd "${GOPATH}"
  echo "docker build -t builder-base -f ${SCRIPT_PATH}/funcs/Dockerfile ."


  docker build -t builder-base -f ${SCRIPT_PATH}/funcs/Dockerfile .

  echo "build base finish"
}

# 构建docker
function build() {
  echo "build docker start"

  build_base

  if [ "${build_srv_list}" == "all" ]; then
       # shellcheck disable=SC2206
       build_srv_list=(${SRV_LIST[@]})
  fi

  # shellcheck disable=SC2128
  echo target srv : "$build_srv_list"


  for srvname in ${build_srv_list[@]}
      do

        # shellcheck disable=SC2164
        cd "$SERVER_PATH"/"${srvname}"
        pwd

        docker build -t "${srvname}"srv .

  done
  echo ">>>>>>>> build end >>>>>>>>"
}

build 