#!/bin/bash

. ./funcs/const.sh

pathCheck(){
  if [[ -z "$SERVER_PATH" ]]; then
      echo "Please Input Correct SERVER_PATH"
      exit 2;
  fi

  if [[ -z "$SERVER_BIN" ]]; then
      echo "Please Input Correct SERVER_BIN"
      exit 2;
  fi
}

function getSrvPID() {
  pid=`ps -ef|grep ${1} | grep -v grep | grep "\--config" | awk '{print $2}'`
  echo "${pid}"
}

pid_is_exist() {
  pid=`getSrvPID $1`
  if [ -z "${pid}" ]; then
     return 1
  else
     echo $1 ${pid} 在运行
     return 0
  fi
}

srvCheck() {
  found=0
  newList=${SRV_LIST[*]}
  newList[${#newList[@]}]="all"
  # shellcheck disable=SC2068
  for srvName in ${newList[@]};do
    if [ "$1" = "$srvName" ]; then
      found=1
    fi
  done
  if [ $found -ne 1 ]; then
    echo "Please Input Valid Server Name: $1 Invalid Server Name"
    exit 1;
  fi
}

function getSrvBin() {
  echo ${SERVER_BIN}/$1
}

function getSrvConf() {
    if [[ ${BASER_SRV} ]]; then
        echo ${SERVER_BIN}/$1/config.test.yml
    else
        echo ${SERVER_BIN}/$1/config.yml
    fi
}
