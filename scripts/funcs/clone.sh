#!/usr/bin/bash

WorkPath=${GOPATH}/src

cloneBase() {
    SubPath=base
    modules=(frameworks fancy-common)
    if [ ! -d "${WorkPath}/${SubPath}" ]; then
        mkdir -p ${WorkPath}/${SubPath}
    fi
    for i in ${modules[@]};
    do
        # if [[ "${i}" == "fancy-common"  ]]; then
        #     git submodule update --init --recursive
        # fi
        if [ ! -d "${WorkPath}/${SubPath}/${i}" ]; then
            <NAME_EMAIL>:back-end/${i}.git ${WorkPath}/${SubPath}/${i}
            cd ${WorkPath}/${SubPath}/${i}
            git checkout develop
        fi
    done
}

cloneBaseicSrv() {
    # BaseicServices
    SubPath=fancygame
    modules=(gatewaysrv loginsrv usersrv webapisrv assetsrv gmsrv msgsrv ranksrv)
    if [ ! -d "${WorkPath}/${SubPath}" ]; then
        mkdir -p ${WorkPath}/${SubPath}
    fi
    for i in ${modules[@]};
    do
        if [ ! -d "${WorkPath}/${SubPath}/${i}" ]; then
            <NAME_EMAIL>:back-end/basic-services/${i}.git ${WorkPath}/${SubPath}/${i}
            cd ${WorkPath}/${SubPath}/${i}
            git checkout develop
        fi
    done
}

cloneFishSrv() {
    # 业务逻辑
    SubPath=fancygame
    modules=(chaossrv spotsrv tripsrv worldsrv hallsrv tasksrv )
    if [ ! -d "${WorkPath}/${SubPath}" ]; then
        mkdir -p ${WorkPath}/${SubPath}
    fi
    for i in ${modules[@]};
    do
        if [ ! -d "${WorkPath}/${SubPath}/${i}" ]; then
            <NAME_EMAIL>:back-end/fisher/${i}.git ${WorkPath}/${SubPath}/${i}
            cd ${WorkPath}/${SubPath}/${i}
            git checkout develop
        fi
    done
}

DOC[clone]="拷贝服务架构到本地"
fun_clone() {
    cloneBase
    cloneBaseicSrv
    cloneFishSrv
}