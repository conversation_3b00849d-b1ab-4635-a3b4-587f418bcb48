#!/bin/bash

# 日志路径
LOG_PATH=${GOPATH}/src/fancygame/logs/
# 移除记录
RM_RECORD_LOG=${LOG_PATH}/rm_record.log
# 移除时间 {day}
RM_TIME=10

function del(){
    DIR=$1
    RM_DAYS=${RM_TIME}
    for log in `find $DIR -name "*.log" -type f -mtime +${RM_DAYS}`
    do
        echo ${log}
        echo `date +"%Y-%m-%d %H:%M:%S"` "  ${log}" >> "${RM_RECORD_LOG}"
        rm -f "${log}"
    done
}

function cron_del() {
    for dir in `find ${LOG_PATH}/* -type d` 
    do
        del $dir
    done
}

cron_del