#!/bin/bash

genProto(){
    local dirName=$1
    local outDirName=./../pkg/cpb/"$dirName"
    mkdir -p "$outDirName"
#    protoc -I $dirName -I $PWD/common --go_out=$outDirName ./$dirName/*.proto
    protoc -I "$dirName" -I "$PWD"/common --go_out="$outDirName" --go_opt=paths=source_relative  ./"$dirName"/*.proto
}

#genProto common
#genProto http
#genProto gate
#genProto login
#genProto world
#genProto spot

for dir in ./*/
do
    if [ -d "$dir" ]; then
      echo "${dir%*/}"
      genProto "${dir%*/}"
    fi
done
