package main

import (
	"context"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/driver"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"runtime"
	"spotsrv/internal/config"
	"spotsrv/internal/proc"
	"spotsrv/internal/pubsub/subscribe"
	"spotsrv/internal/repo/record"
	"spotsrv/internal/server/rpc"
	"spotsrv/internal/timer"
)

type spotService struct {
	Name string
	Ctx  context.Context
}

func (s *spotService) Init() error {
	s.Ctx = context.Background()
	s.Name = viper.GetString(dict.ConfigRpcServerName)
	logrus.Infoln(s.Name + "服务Init")

	// 初始化配置
	err := config.InitConfig()
	if err != nil {
		logrus.Errorf("初始化配置失败:%+v", err)
		return err
	}

	// 注册客户端消息
	proc.RegClientMsgHandler()

	// 初始化rpc 服务器
	rpc.InitSpotRpc()

	// 注册GM接口
	proc.InitGmRpc()

	// 广播消息订阅
	subscribe.InitSubScribe()

	// 定时处理相关
	timer.Init()

	// 流水相关
	record.Init()
	// 实例化日志流水作业
	record.DefaultLogging = record.NewSpotRecordWorkPool()
	record.DefaultLogging.StartHookFishRecordWorkerPool()

	return nil
}

func (s *spotService) Start() error {
	return nil
}

func (s *spotService) Stop() error {
	return nil
}

func (s *spotService) ForceStop() error {
	return nil
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU() * 2)
	random.NewInitSource()
	driver.Run(&spotService{})
}
