package main

import (
	"activitysrv/config"
	"activitysrv/internal/proc"
	"activitysrv/internal/pubsub/subscribe"
	"activitysrv/internal/server/rpc"
	"context"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/driver"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"runtime"
)

type activityService struct {
	Name string
	Ctx  context.Context
}

func (s *activityService) Init() error {
	s.Ctx = context.Background()
	s.Name = viper.GetString(dict.ConfigRpcServerName)
	logrus.Infoln(s.Name + "服务Init")

	// 初始化配置
	err := config.InitConfig()
	if err != nil {
		logrus.Errorf("初始化配置失败:%+v", err)
		return err
	}

	proc.RegClientMsgHandler()
	rpc.InitActivityRpc()
	subscribe.InitSubscribe()

	return nil
}

func (s *activityService) Start() error {
	logrus.Infoln(s.Name + "服务启动成功")
	return nil
}

func (s *activityService) Stop() error {
	logrus.Infoln(s.Name + "服务关闭中...")
	return nil
}

func (s *activityService) ForceStop() error {
	logrus.Infoln(s.Name + " ForceStop ...")
	return nil
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU() * 2)
	random.NewInitSource() // 初始化随机数
	driver.Run(&activityService{})
}
